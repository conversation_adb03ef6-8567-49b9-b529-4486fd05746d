<?php
/*
 * Template Name: <PERSON><PERSON><PERSON> màu web
 */
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 Công cụ phối màu web design - Tạo bảng màu chuyên nghiệp</title>
    <meta property="og:url" content="https://vutruso.com/cong-cu-phoi-mau-web/">
    <link rel="canonical" href="https://vutruso.com/cong-cu-phoi-mau-web/">
    <meta name="description" content="Công cụ phối màu web design miễn phí này được phát triển dựa trên lý thuyết màu sắc khoa học và kinh nghiệm thực tế trong thiết kế web. Tạo bảng màu chuyên nghiệp cho website, WordPress trong vài phút">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:site_name" content="Công cụ phối màu web design">
    <meta property="og:locale" content="vi_VN">
    <meta property="og:type" content="website">
    <meta property="og:title" content="🎨 Công cụ phối màu web design - Tạo bảng màu chuyên nghiệp">
    <meta property="og:description" content="Công cụ phối màu web design miễn phí này được phát triển dựa trên lý thuyết màu sắc khoa học và kinh nghiệm thực tế trong thiết kế web. Tạo bảng màu chuyên nghiệp cho website, WordPress trong vài phút">
    <meta property="og:image" content="https://vutruso.com/wp-content/uploads/2025/08/cong-cu-phoi-mau-website.png">
    <meta property="og:image:secure_url" content="https://vutruso.com/wp-content/uploads/2025/08/cong-cu-phoi-mau-web-design.png">
    <meta property="og:image:width" content="405">
    <meta property="og:image:height" content="270">
    <meta property="og:image:alt" content="Công cụ phối màu web design - Vutruso">
    <meta property="fb:admins" content="100002603951764">
    <meta property="fb:app_id" content="***************">
    
    <!-- Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@vutruso">
    <meta name="twitter:title" content="🎨 Công cụ phối màu web design - Tạo bảng màu chuyên nghiệp">
    <meta name="twitter:description" content="Công cụ phối màu web design miễn phí này được phát triển dựa trên lý thuyết màu sắc khoa học và kinh nghiệm thực tế trong thiết kế web. Tạo bảng màu chuyên nghiệp cho website, WordPress trong vài phút">
    <meta name="twitter:image" content="https://vutruso.com/wp-content/uploads/2025/08/cong-cu-phoi-mau-web-design.png">
<style>
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
    position: relative;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px 30px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.header h1 {
    font-size: 2.8em;
    margin-bottom: 15px;
    position: relative;
    z-index: 1;
}

.header p {
    font-size: 1.2em;
    opacity: 0.95;
    position: relative;
    z-index: 1;
}

/* Navigation Tabs */
.nav-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 3px solid #e9ecef;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-tab {
    flex: 1;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
    border: none;
    font-size: 1.1em;
    font-weight: 600;
    color: #666;
    position: relative;
}

.nav-tab.active {
    background: white;
    color: #667eea;
    transform: translateY(1px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.nav-tab:hover:not(.active) {
    background: #e9ecef;
    color: #333;
}

.nav-tab::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background: #667eea;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-tab.active::before {
    width: 100%;
}

/* Main Content */
.main-content {
    padding: 40px 30px;
}

.tab-content {
    display: none;
    animation: fadeIn 0.5s ease;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Tool Cards */
.tool-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.tool-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.tool-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.tool-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.tool-icon {
    font-size: 2.5em;
    margin-right: 15px;
}

.tool-title {
    font-size: 1.4em;
    font-weight: 700;
    color: #333;
}

.tool-subtitle {
    color: #666;
    font-size: 0.95em;
    margin-top: 5px;
    line-height: 1.4;
}

/* Color Input Section */
.color-input-section {
    display: flex;
    align-items: center;
    gap: 20px;
    margin: 20px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 15px;
    border: 2px dashed #ddd;
    transition: all 0.3s ease;
}

.color-input-section:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.color-input {
    width: 80px;
    height: 80px;
    border: 4px solid white;
    border-radius: 20px;
    cursor: pointer;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.color-input:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

.color-info {
    flex: 1;
}

.color-label {
    font-size: 1.1em;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.color-description {
    color: #666;
    font-size: 0.9em;
    line-height: 1.4;
}

/* File Upload */
.file-upload-area {
    border: 3px dashed #ddd;
    border-radius: 15px;
    padding: 40px 20px;
    text-align: center;
    transition: all 0.3s ease;
    background: #fafafa;
    margin: 20px 0;
    position: relative;
    overflow: hidden;
}

.file-upload-area:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.file-upload-area.dragover {
    border-color: #28a745;
    background: #f0fff4;
}

.upload-icon {
    font-size: 3em;
    color: #ccc;
    margin-bottom: 15px;
}

.file-upload-area:hover .upload-icon {
    color: #667eea;
}

.file-input {
    display: none;
}

.file-label {
    display: inline-block;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 15px 30px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    margin-top: 10px;
}

.file-label:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin-top: 25px;
}

.btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1em;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #ffffff40;
    border-radius: 50%;
    border-top: 2px solid #ffffff;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Results Section */
.results-section {
    margin-top: 40px;
}

.section-title {
    font-size: 1.8em;
    font-weight: 700;
    color: #333;
    margin-bottom: 25px;
    position: relative;
    padding-left: 20px;
}

.section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

/* Harmony Colors */
.harmony-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.harmony-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
}

.harmony-card:hover {
    transform: translateY(-5px);
    border-color: #667eea;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.harmony-title {
    font-size: 1.1em;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
    text-align: center;
}

.harmony-colors {
    display: flex;
    height: 60px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
    margin-bottom: 15px;
}

.harmony-color {
    flex: 1;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.harmony-color:hover {
    transform: scaleY(1.2);
    z-index: 10;
}

.harmony-color::after {
    content: attr(title);
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 5px 8px;
    border-radius: 5px;
    font-size: 0.8em;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.harmony-color:hover::after {
    opacity: 1;
}

/* Palette Cards */
.palette-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
}

.palette-card {
    background: white;
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.palette-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.palette-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.palette-title {
    font-weight: 700;
    margin-bottom: 20px;
    color: #333;
    text-align: center;
    font-size: 1.2em;
}

.color-row {
    display: flex;
    margin-bottom: 20px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.color-block {
    flex: 1;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.color-block:hover {
    transform: scale(1.05);
    z-index: 10;
}

.color-code {
    color: white;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    font-size: 0.9em;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.color-block:hover .color-code {
    opacity: 1;
}

/* Preview Section */
.preview-section {
    background: #f8f9fa;
    border-radius: 20px;
    padding: 30px;
    margin-top: 40px;
    border: 2px solid #e9ecef;
}

.preview-title {
    text-align: center;
    margin-bottom: 25px;
    color: #333;
    font-size: 1.6em;
    font-weight: 700;
}

.website-preview {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    transform: perspective(1000px) rotateX(5deg);
    transition: transform 0.3s ease;
}

.website-preview:hover {
    transform: perspective(1000px) rotateX(0deg);
}

.preview-header {
    padding: 25px;
    color: white;
    font-weight: 700;
    font-size: 1.2em;
}

.preview-content {
    padding: 30px;
}

.preview-card {
    padding: 20px;
    border-radius: 10px;
    margin: 15px 0;
    color: white;
    font-weight: 600;
}

/* Uploaded Image */
.uploaded-image {
    max-width: 100%;
    max-height: 200px;
    border-radius: 15px;
    margin: 20px 0;
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    transition: transform 0.3s ease;
}

.uploaded-image:hover {
    transform: scale(1.05);
}

/* Toast */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 15px 25px;
    border-radius: 10px;
    z-index: 1000;
    transform: translateX(120%);
    transition: transform 0.3s ease;
    box-shadow: 0 5px 20px rgba(40, 167, 69, 0.3);
    font-weight: 600;
}

.toast.show {
    transform: translateX(0);
}

.guide ul{
    margin-left: 24px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .header {
        padding: 30px 20px;
    }
    
    .header h1 {
        font-size: 2.2em;
    }
    
    .main-content {
        padding: 20px;
    }

    .nav-tab {
        padding: 15px 10px;
        font-size: 0.95em;
    }

    .tool-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .color-input-section {
        flex-direction: column;
        text-align: center;
    }

    .action-buttons {
        justify-content: center;
    }
}

/* Success State */
.success-indicator {
    display: inline-block;
    color: #28a745;
    font-size: 1.2em;
    margin-left: 10px;
    animation: bounce 0.5s ease;
}

@keyframes bounce {
    0%, 20%, 60%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    80% { transform: translateY(-5px); }
}

@keyframes highlightPreview {
    0% { box-shadow: 0 10px 30px rgba(0,0,0,0.15); }
    50% { box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3); }
    100% { box-shadow: 0 10px 30px rgba(0,0,0,0.15); }
}

.scroll-indicator {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(102, 126, 234, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.8em;
    animation: bounce 2s infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.scroll-indicator.show {
    opacity: 1;
}

/* Popular Colors */
.popular-color {
    width: 60px;
    height: 35px;
    border: 2px solid #fff;
    border-radius: 8px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
    font-size: 0.7em;
    color: white;
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    position: relative;
    overflow: hidden;
}

.popular-color:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0,0,0,0.25);
    border-color: #667eea;
}

.popular-color::after {
    content: attr(title);
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75em;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    white-space: nowrap;
}

.popular-color:hover::after {
    opacity: 1;
}

/* Hex Input Styling */
#hexInput {
    transition: all 0.3s ease;
}

#hexInput:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    outline: none;
}

#hexInput.valid {
    border-color: #28a745 !important;
    background: #f8fff9;
}

#hexInput.invalid {
    border-color: #dc3545 !important;
    background: #fff5f5;
}
</style>

<!-- Structured Data JSON-LD -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Công cụ phối màu web design",
    "url": "https://vutruso.com/cong-cu-phoi-mau-web/",
    "description": "Công cụ phối màu web design miễn phí này được phát triển dựa trên lý thuyết màu sắc khoa học và kinh nghiệm thực tế trong thiết kế web. Tạo bảng màu chuyên nghiệp cho website, WordPress trong vài phút",
    "applicationCategory": "UtilityApplication",
    "operatingSystem": "Any",
    "browserRequirements": "Requires JavaScript. Requires HTML5.",
    "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "VND"
    },
    "featureList": [
        "Tạo bảng màu chuyên nghiệp",
        "Dựa trên lý thuyết màu sắc khoa học",
        "Phù hợp cho thiết kế web",
        "Copy mã màu hex nhanh chóng",
        "Bảng màu Modern & Clean",
        "Bảng màu Professional",
        "Phù hợp cho WordPress",
        "Phù hợp cho ecommerce",
        "Giao diện thân thiện",
        "Hoàn toàn miễn phí"
    ],
    "screenshot": "https://vutruso.com/wp-content/uploads/2025/08/cong-cu-phoi-mau-web-design.png",
    "creator": {
        "@type": "Organization",
        "name": "Vũ Trụ Số",
        "url": "https://vutruso.com",
        "alternateName": "Vũ Trụ Số",
        "description": "Vũ Trụ Số chuyên cung cấp các giải pháp công nghệ, thiết kế website, quản trị website cho doanh nghiệp, cung cấp máy chủ ảo VPS/Hosting, dịch vụ chạy quảng cáo Google, quảng cáo Facebook, quảng cáo Zalo, các dịch vụ bảo mật website WordPress, tăng tốc website WordPress và SEO mới từ khóa lên top Google",
        "logo": "https://vutruso.com/wp-content/uploads/2020/07/vutruso_login.jpg",
        "sameAs": [
            "https://www.facebook.com/vutruso",
            "https://twitter.com/@vutruso",
            "https://www.pinterest.com/vutruso/",
            "https://www.instagram.com/vutruso",
            "https://www.youtube.com/channel/UCOEXvu-rxMQ6Tvqm1kZJUrg/about",
            "https://www.linkedin.com/in/vutruso",
            "https://g.page/vutruso",
            "https://vutruso.business.site/",
            "https://sites.google.com/view/vutruweb",
            "https://vutruso.tumblr.com/",
            "https://ok.ru/profile/589668477610"
        ],
        "vatID": "0317358676",
        "contactPoint": [
            {
                "@type": "ContactPoint",
                "telephone": "+***********",
                "email": "<EMAIL>",
                "contactOption": "TollFree",
                "contactType": "customer support"
            }
        ]
    }
}
</script>

</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🎨 Công Cụ Phối Màu Web Design</h1>
            <p>Tạo bảng màu đẹp mắt và chuyên nghiệp cho website của bạn</p>
        </div>

        <!-- Navigation -->
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="switchTab(0)">
                🎯 Chọn Màu Chính
            </button>
            <button class="nav-tab" onclick="switchTab(1)">
                📸 Từ Ảnh
            </button>
            <button class="nav-tab" onclick="switchTab(2)">
                👀 Kết Quả
            </button>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Tab 1: Color Picker -->
            <div class="tab-content active" id="tab-0">
                <div class="tool-grid">
                    <div class="tool-card">
                        <div class="tool-header">
                            <div class="tool-icon">🎨</div>
                            <div>
                                <div class="tool-title">Tạo Từ Màu Chính</div>
                                <div class="tool-subtitle">Chọn một màu cơ bản để tạo bảng màu hài hòa theo lý thuyết màu sắc</div>
                            </div>
                        </div>
                        
                        <div class="color-input-section">
                            <input type="color" id="baseColor" class="color-input" value="#667eea" onchange="syncColorInputs()">
                            <div class="color-info">
                                <div class="color-label">Màu chính của bạn</div>
                                <div class="color-description">Chọn màu hoặc nhập mã hex để tạo bảng màu hài hòa</div>
                            </div>
                        </div>

                        <!-- Hex Input Section -->
                        <div style="margin: 20px 0;">
                            <label for="hexInput" style="display: block; font-weight: 600; margin-bottom: 8px; color: #333;">
                                🎯 Hoặc nhập mã màu hex:
                            </label>
                            <div style="display: flex; gap: 10px; align-items: center;">
                                <input 
                                    type="text" 
                                    id="hexInput" 
                                    placeholder="#667eea" 
                                    value="#667eea"
                                    style="flex: 1; padding: 12px 15px; border: 2px solid #ddd; border-radius: 10px; font-size: 1em; font-family: monospace; transition: all 0.3s ease;"
                                    oninput="validateAndSyncHex()"
                                    onkeypress="handleHexKeyPress(event)"
                                    maxlength="7"
                                >
                                <button class="btn" onclick="applyHexColor()" style="padding: 12px 20px;">
                                    ✅ Áp dụng
                                </button>
                            </div>
                            <div id="hexError" style="color: #dc3545; font-size: 0.85em; margin-top: 5px; display: none;">
                                ❌ Mã màu không hợp lệ! Định dạng: #RRGGBB (ví dụ: #667eea)
                            </div>
                        </div>

                        <!-- Popular Colors -->
                        <div style="margin: 20px 0;">
                            <label style="display: block; font-weight: 600; margin-bottom: 10px; color: #333;">
                                🌟 Màu phổ biến:
                            </label>
                            <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                <button class="popular-color" onclick="selectPopularColor('#667eea')" style="background: #667eea;" title="Blue Purple">#667eea</button>
                                <button class="popular-color" onclick="selectPopularColor('#48bb78')" style="background: #48bb78;" title="Green">#48bb78</button>
                                <button class="popular-color" onclick="selectPopularColor('#ed8936')" style="background: #ed8936;" title="Orange">#ed8936</button>
                                <button class="popular-color" onclick="selectPopularColor('#9f7aea')" style="background: #9f7aea;" title="Purple">#9f7aea</button>
                                <button class="popular-color" onclick="selectPopularColor('#4299e1')" style="background: #4299e1;" title="Blue">#4299e1</button>
                                <button class="popular-color" onclick="selectPopularColor('#ed64a6')" style="background: #ed64a6;" title="Pink">#ed64a6</button>
                                <button class="popular-color" onclick="selectPopularColor('#38b2ac')" style="background: #38b2ac;" title="Teal">#38b2ac</button>
                                <button class="popular-color" onclick="selectPopularColor('#f56565')" style="background: #f56565;" title="Red">#f56565</button>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button class="btn" onclick="generateFromColor()">
                                <span id="generateBtn">✨ Tạo Bảng Màu</span>
                            </button>
                            <button class="btn btn-secondary" onclick="generateRandomPalette()">
                                🎲 Ngẫu Nhiên
                            </button>
                        </div>
                    </div>

                    <div class="tool-card">
                        <div class="tool-header">
                            <div class="tool-icon">🌈</div>
                            <div>
                                <div class="tool-title">Bảng Màu Có Sẵn</div>
                                <div class="tool-subtitle">Chọn từ các bảng màu được thiết kế sẵn cho web design</div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button class="btn btn-success" onclick="loadPresetPalettes()">
                                📋 Xem Bảng Màu Có Sẵn
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab 2: Image Upload -->
            <div class="tab-content" id="tab-1">
                <div class="tool-grid">
                    <div class="tool-card">
                        <div class="tool-header">
                            <div class="tool-icon">📸</div>
                            <div>
                                <div class="tool-title">Trích Xuất Màu Từ Ảnh</div>
                                <div class="tool-subtitle">Upload ảnh để tự động phân tích và tạo bảng màu từ các màu chủ đạo</div>
                            </div>
                        </div>

                        <div class="file-upload-area" id="uploadArea">
                            <div class="upload-icon">📁</div>
                            <h3>Kéo thả ảnh vào đây hoặc click để chọn</h3>
                            <p>Hỗ trợ JPG, PNG, GIF (tối đa 5MB)</p>
                            <input type="file" id="imageUpload" class="file-input" accept="image/*" onchange="extractColorsFromImage()">
                            <label for="imageUpload" class="file-label">📎 Chọn Ảnh</label>
                        </div>

                        <div id="uploadedImageContainer"></div>
                    </div>
                </div>
            </div>

            <!-- Tab 3: Results -->
            <div class="tab-content" id="tab-2">
                <div id="resultsContainer">
                    <div style="text-align: center; padding: 60px 20px; color: #666;">
                        <div style="font-size: 4em; margin-bottom: 20px;">🎨</div>
                        <h3 style="margin-bottom: 10px;">Chưa có kết quả</h3>
                        <p>Hãy tạo bảng màu từ tab "Chọn Màu Chính" hoặc "Từ Ảnh" để xem kết quả</p>
                    </div>
                </div>
            </div>

            <!-- Color Harmony Section -->
            <div id="colorHarmonySection" style="display: none;">
                <div class="results-section">
                    <h3 class="section-title">🎨 Các Kiểu Phối Màu Hài Hòa</h3>
                    <div class="harmony-grid" id="harmonyContainer"></div>
                </div>
            </div>

            <!-- Generated Palettes -->
            <div id="palettesSection" style="display: none;">
                <div class="results-section">
                    <h3 class="section-title">🎯 Bảng Màu Được Đề Xuất</h3>
                    <div id="palettesContainer" class="palette-grid"></div>
                </div>
            </div>

            <!-- Website Preview -->
            <div class="preview-section" id="previewSection" style="display: none; position: relative;">
                <h3 class="preview-title">👀 Preview Website</h3>
                <div class="scroll-indicator" id="scrollIndicator">Scroll để xem đầy đủ ⬇️</div>
                <div class="website-preview">
                    <div class="preview-header" id="previewHeader">
                        🏠 Header Website
                    </div>
                    <div class="preview-content">
                        <h2 style="color: #333; margin-bottom: 15px;">Nội dung chính</h2>
                        <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
                            Đây là preview website với bảng màu bạn đã chọn. 
                            Bạn có thể thấy cách các màu sắc kết hợp với nhau trong thiết kế thực tế.
                        </p>
                        <div class="preview-card" id="previewCard1">
                            📦 Component Card 1
                        </div>
                        <div class="preview-card" id="previewCard2">
                            💼 Component Card 2
                        </div>
                        <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                            <strong>💡 Tip:</strong> Click vào các màu trong bảng màu để copy mã hex!
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Guide and Information Section -->
        <div class="guide" style="background: #f8f9fa; padding: 40px 30px; margin-top: 40px;">
            <div style="max-width:100%; margin: 0 auto;">
                <h2 style="text-align: center; color: #333; margin-bottom: 30px; font-size: 2.2em;">
                    📚 Hướng Dẫn Sử Dụng Công Cụ Phối Màu Web Design
                </h2>

                <!-- How to Use -->
                <div style="background: white; border-radius: 15px; padding: 30px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                    <h3 style="color: #667eea; margin-bottom: 20px; font-size: 1.5em;">🚀 Cách Sử Dụng</h3>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px;">
                        <div>
                            <h4 style="color: #333; margin-bottom: 10px;">🎯 Tạo Từ Màu Chính</h4>
                            <ul style="color: #666; line-height: 1.6;">
                                <li><strong>Chọn màu:</strong> Sử dụng color picker hoặc nhập mã hex trực tiếp</li>
                                <li><strong>Nhập mã hex:</strong> Gõ mã màu như #667eea (có thể bỏ dấu #)</li>
                                <li><strong>Màu phổ biến:</strong> Click vào 8 màu trending để chọn nhanh</li>
                                <li><strong>Tạo bảng màu:</strong> Click "Tạo Bảng Màu" để nhận 5 kiểu phối màu hài hòa</li>
                            </ul>
                        </div>
                        
                        <div>
                            <h4 style="color: #333; margin-bottom: 10px;">📸 Trích Xuất Từ Ảnh</h4>
                            <ul style="color: #666; line-height: 1.6;">
                                <li><strong>Upload ảnh:</strong> Kéo thả hoặc click chọn file (JPG, PNG, GIF)</li>
                                <li><strong>Kích thước:</strong> Tối đa 5MB, chất lượng cao cho kết quả tốt nhất</li>
                                <li><strong>Phân tích tự động:</strong> Hệ thống trích xuất 5 màu chủ đạo</li>
                                <li><strong>Bảng màu đề xuất:</strong> Nhận 4 phong cách khác nhau từ màu ảnh</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Benefits -->
                <div style="background: white; border-radius: 15px; padding: 30px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                    <h3 style="color: #667eea; margin-bottom: 20px; font-size: 1.5em;">✨ Lợi Ích Của Công Cụ</h3>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                        <div style="padding: 20px; background: #f8f9fa; border-radius: 10px;">
                            <h4 style="color: #333; margin-bottom: 10px;">🎨 Thiết Kế Chuyên Nghiệp</h4>
                            <p style="color: #666; line-height: 1.5;">Tạo bảng màu hài hòa theo lý thuyết màu sắc khoa học, giúp website trông chuyên nghiệp và thu hút.</p>
                        </div>
                        
                        <div style="padding: 20px; background: #f8f9fa; border-radius: 10px;">
                            <h4 style="color: #333; margin-bottom: 10px;">⚡ Tiết Kiệm Thời Gian</h4>
                            <p style="color: #666; line-height: 1.5;">Không cần mất hàng giờ nghiên cứu màu sắc. Chỉ vài click là có ngay bảng màu hoàn chỉnh.</p>
                        </div>
                        
                        <div style="padding: 20px; background: #f8f9fa; border-radius: 10px;">
                            <h4 style="color: #333; margin-bottom: 10px;">🎯 Phù Hợp Mọi Dự Án</h4>
                            <p style="color: #666; line-height: 1.5;">Từ website cá nhân đến doanh nghiệp, từ blog đến ecommerce - đều có bảng màu phù hợp.</p>
                        </div>
                        
                        <div style="padding: 20px; background: #f8f9fa; border-radius: 10px;">
                            <h4 style="color: #333; margin-bottom: 10px;">📱 Responsive Design</h4>
                            <p style="color: #666; line-height: 1.5;">Preview trực tiếp trên giao diện website, đảm bảo màu sắc hoạt động tốt trên mọi thiết bị.</p>
                        </div>
                    </div>
                </div>

                <!-- Color Theory -->
                <div style="background: white; border-radius: 15px; padding: 30px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                    <h3 style="color: #667eea; margin-bottom: 20px; font-size: 1.5em;">🌈 Lý Thuyết Màu Sắc</h3>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px;">
                        <div>
                            <h4 style="color: #333; margin-bottom: 10px;">🎨 Đơn Sắc (Monochromatic)</h4>
                            <p style="color: #666; line-height: 1.5;">Sử dụng các tông màu khác nhau của cùng một màu. Tạo cảm giác nhất quán, thanh lịch và dễ nhìn.</p>
                        </div>
                        
                        <div>
                            <h4 style="color: #333; margin-bottom: 10px;">🌈 Tương Tự (Analogous)</h4>
                            <p style="color: #666; line-height: 1.5;">Kết hợp các màu liền kề trên bánh xe màu. Tạo cảm giác hài hòa, tự nhiên và dễ chịu.</p>
                        </div>
                        
                        <div>
                            <h4 style="color: #333; margin-bottom: 10px;">⚖️ Bổ Sung (Complementary)</h4>
                            <p style="color: #666; line-height: 1.5;">Sử dụng màu đối diện trên bánh xe màu. Tạo độ tương phản cao, nổi bật và thu hút sự chú ý.</p>
                        </div>
                        
                        <div>
                            <h4 style="color: #333; margin-bottom: 10px;">🔺 Tam Giác (Triadic)</h4>
                            <p style="color: #666; line-height: 1.5;">Ba màu cách đều nhau trên bánh xe màu. Cân bằng giữa hài hòa và tương phản, rất phù hợp web hiện đại.</p>
                        </div>
                    </div>
                </div>

                <!-- Tips and Best Practices -->
                <div style="background: white; border-radius: 15px; padding: 30px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                    <h3 style="color: #667eea; margin-bottom: 20px; font-size: 1.5em;">💡 Tips & Best Practices</h3>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px;">
                        <div>
                            <h4 style="color: #333; margin-bottom: 10px;">🎯 Chọn Màu Chính</h4>
                            <ul style="color: #666; line-height: 1.6;">
                                <li>Chọn màu phù hợp với thương hiệu và mục đích website</li>
                                <li>Xem xét tâm lý màu sắc: xanh lam (tin cậy), xanh lá (tự nhiên), đỏ (năng lượng)</li>
                                <li>Test màu sắc trên nhiều thiết bị khác nhau</li>
                                <li>Đảm bảo độ tương phản đủ cho accessibility</li>
                            </ul>
                        </div>
                        
                        <div>
                            <h4 style="color: #333; margin-bottom: 10px;">📸 Sử Dụng Ảnh</h4>
                            <ul style="color: #666; line-height: 1.6;">
                                <li>Chọn ảnh có màu sắc rõ ràng và tương phản cao</li>
                                <li>Ảnh phong cảnh, sản phẩm thường cho kết quả tốt</li>
                                <li>Tránh ảnh quá tối hoặc quá sáng đơn điệu</li>
                                <li>Có thể dùng ảnh logo, artwork để lấy màu thương hiệu</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- FAQ -->
                <div style="background: white; border-radius: 15px; padding: 30px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                    <h3 style="color: #667eea; margin-bottom: 20px; font-size: 1.5em;">❓ Câu Hỏi Thường Gặp</h3>
                    
                    <div style="display: grid; gap: 20px;">
                        <div style="border-left: 4px solid #667eea; padding-left: 20px;">
                            <h4 style="color: #333; margin-bottom: 8px;">Làm sao để copy mã màu?</h4>
                            <p style="color: #666; line-height: 1.5;">Click trực tiếp vào bất kỳ màu nào trong bảng màu, mã hex sẽ tự động được copy vào clipboard. Bạn sẽ thấy thông báo xác nhận.</p>
                        </div>
                        
                        <div style="border-left: 4px solid #667eea; padding-left: 20px;">
                            <h4 style="color: #333; margin-bottom: 8px;">Tôi có thể sử dụng cho WordPress không?</h4>
                            <p style="color: #666; line-height: 1.5;">Hoàn toàn có thể! Copy các mã màu và paste vào theme customizer, CSS, hoặc page builder như Elementor, Divi.</p>
                        </div>
                        
                        <div style="border-left: 4px solid #667eea; padding-left: 20px;">
                            <h4 style="color: #333; margin-bottom: 8px;">Bảng màu nào phù hợp cho ecommerce?</h4>
                            <p style="color: #666; line-height: 1.5;">Khuyến nghị sử dụng "Modern & Clean" hoặc "Professional" với màu chính là xanh lam hoặc xanh lá để tạo cảm giác tin cậy.</p>
                        </div>
                        
                        <div style="border-left: 4px solid #667eea; padding-left: 20px;">
                            <h4 style="color: #333; margin-bottom: 8px;">Tại sao cần 5 màu trong bảng màu?</h4>
                            <p style="color: #666; line-height: 1.5;">5 màu đủ để tạo hierarchy: màu chính (CTA), màu phụ (accent), nền chính, nền phụ, và màu text/border.</p>
                        </div>
                    </div>
                </div>

                <!-- About Tool -->
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 15px; padding: 30px; color: white; text-align: center;">
                    <h3 style="margin-bottom: 15px; font-size: 1.5em;">🎨 Về Công Cụ Phối Màu Web Design</h3>
                    <p style="line-height: 1.6; opacity: 0.95; max-width: 800px; margin: 0 auto;">
                        Công cụ phối màu web design miễn phí này được phát triển dựa trên lý thuyết màu sắc khoa học và kinh nghiệm thực tế trong thiết kế web. 
                        Chúng tôi hiểu rằng việc chọn màu sắc phù hợp là một trong những yếu tố quan trọng nhất quyết định thành công của một website. 
                        Với giao diện thân thiện và tính năng mạnh mẽ, tool này giúp designer, developer và business owner tạo ra những bảng màu 
                        chuyên nghiệp trong vài phút thay vì vài giờ.
                    </p>
                    <div style="margin-top: 20px; font-size: 0.9em; opacity: 0.8;">
                        <strong>Keywords:</strong> color palette generator, web design colors, website color scheme, hex color picker, 
                        color harmony tool, design system colors, brand colors, UI color palette, responsive design colors
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast notification -->
    <div id="toast" class="toast"></div>

    <script>
        let currentTab = 0;
        let isGenerating = false;

        // Color validation and sync functions
        function isValidHex(hex) {
            return /^#[0-9A-F]{6}$/i.test(hex);
        }

        function syncColorInputs() {
            const colorPicker = document.getElementById('baseColor');
            const hexInput = document.getElementById('hexInput');
            hexInput.value = colorPicker.value.toUpperCase();
            validateHexInput();
        }

        function validateAndSyncHex() {
            const hexInput = document.getElementById('hexInput');
            let value = hexInput.value.trim();
            
            // Auto-add # if missing
            if (value && !value.startsWith('#')) {
                value = '#' + value;
                hexInput.value = value;
            }
            
            // Convert to uppercase
            hexInput.value = value.toUpperCase();
            
            validateHexInput();
        }

        function validateHexInput() {
            const hexInput = document.getElementById('hexInput');
            const hexError = document.getElementById('hexError');
            const value = hexInput.value;
            
            if (!value) {
                hexInput.className = '';
                hexError.style.display = 'none';
                return false;
            }
            
            if (isValidHex(value)) {
                hexInput.className = 'valid';
                hexError.style.display = 'none';
                
                // Sync with color picker
                document.getElementById('baseColor').value = value;
                return true;
            } else {
                hexInput.className = 'invalid';
                hexError.style.display = 'block';
                return false;
            }
        }

        function applyHexColor() {
            if (validateHexInput()) {
                const hexValue = document.getElementById('hexInput').value;
                document.getElementById('baseColor').value = hexValue;
                generateFromColor();
                showToast(`🎨 Đã áp dụng màu ${hexValue}!`);
            } else {
                showToast('❌ Vui lòng nhập mã màu hợp lệ!');
            }
        }

        function selectPopularColor(hexColor) {
            document.getElementById('baseColor').value = hexColor;
            document.getElementById('hexInput').value = hexColor.toUpperCase();
            validateHexInput();
            showToast(`🎨 Đã chọn màu ${hexColor}! Đang tạo bảng màu...`);
            
            // Auto generate after a short delay
            setTimeout(() => {
                generateFromColor();
            }, 800);
        }

        function handleHexKeyPress(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                applyHexColor();
            }
        }

        // Tab switching
        function switchTab(tabIndex) {
            // Update tab buttons
            document.querySelectorAll('.nav-tab').forEach((tab, index) => {
                tab.classList.toggle('active', index === tabIndex);
            });

            // Update tab content
            document.querySelectorAll('.tab-content').forEach((content, index) => {
                content.classList.toggle('active', index === tabIndex);
            });

            currentTab = tabIndex;

            // Auto switch to results tab when there are results
            if (tabIndex === 2) {
                showResults();
            }
        }

        function showResults() {
            const harmonySection = document.getElementById('colorHarmonySection');
            const palettesSection = document.getElementById('palettesSection');
            const previewSection = document.getElementById('previewSection');
            const resultsContainer = document.getElementById('resultsContainer');

            // Check if there are any results to show
            const hasResults = harmonySection.style.display !== 'none' || 
                              palettesSection.style.display !== 'none' || 
                              previewSection.style.display !== 'none';

            if (hasResults) {
                resultsContainer.innerHTML = '';
                resultsContainer.appendChild(harmonySection);
                resultsContainer.appendChild(palettesSection);
                resultsContainer.appendChild(previewSection);
            }
        }

        // Drag and drop functionality
        const uploadArea = document.getElementById('uploadArea');
        
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, unhighlight, false);
        });

        function highlight(e) {
            uploadArea.classList.add('dragover');
        }

        function unhighlight(e) {
            uploadArea.classList.remove('dragover');
        }

        uploadArea.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            if (files.length > 0) {
                document.getElementById('imageUpload').files = files;
                extractColorsFromImage();
            }
        }

        // Color theory functions (same as before but with loading states)
        function hexToHsl(hex) {
            const r = parseInt(hex.slice(1, 3), 16) / 255;
            const g = parseInt(hex.slice(3, 5), 16) / 255;
            const b = parseInt(hex.slice(5, 7), 16) / 255;

            const max = Math.max(r, g, b);
            const min = Math.min(r, g, b);
            let h, s, l = (max + min) / 2;

            if (max === min) {
                h = s = 0;
            } else {
                const d = max - min;
                s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
                switch (max) {
                    case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                    case g: h = (b - r) / d + 2; break;
                    case b: h = (r - g) / d + 4; break;
                }
                h /= 6;
            }

            return [h * 360, s * 100, l * 100];
        }

        function hslToHex(h, s, l) {
            h /= 360;
            s /= 100;
            l /= 100;

            const hue2rgb = (p, q, t) => {
                if (t < 0) t += 1;
                if (t > 1) t -= 1;
                if (t < 1/6) return p + (q - p) * 6 * t;
                if (t < 1/2) return q;
                if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
                return p;
            };

            const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
            const p = 2 * l - q;

            const r = Math.round(hue2rgb(p, q, h + 1/3) * 255);
            const g = Math.round(hue2rgb(p, q, h) * 255);
            const b = Math.round(hue2rgb(p, q, h - 1/3) * 255);

            return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
        }

        function generateColorHarmony(baseColor) {
            const [h, s, l] = hexToHsl(baseColor);
            
            return {
                monochromatic: [
                    baseColor,
                    hslToHex(h, s, Math.max(20, l - 20)),
                    hslToHex(h, s, Math.min(80, l + 20)),
                    hslToHex(h, Math.max(20, s - 20), l),
                    hslToHex(h, Math.min(80, s + 20), l)
                ],
                analogous: [
                    baseColor,
                    hslToHex((h + 30) % 360, s, l),
                    hslToHex((h - 30 + 360) % 360, s, l),
                    hslToHex((h + 60) % 360, s, l),
                    hslToHex((h - 60 + 360) % 360, s, l)
                ],
                complementary: [
                    baseColor,
                    hslToHex((h + 180) % 360, s, l),
                    hslToHex(h, s, Math.max(20, l - 30)),
                    hslToHex((h + 180) % 360, s, Math.max(20, l - 30)),
                    hslToHex(h, Math.max(20, s - 20), l)
                ],
                triadic: [
                    baseColor,
                    hslToHex((h + 120) % 360, s, l),
                    hslToHex((h + 240) % 360, s, l),
                    hslToHex(h, s, Math.max(20, l - 20)),
                    hslToHex((h + 120) % 360, s, Math.max(20, l - 20))
                ],
                tetradic: [
                    baseColor,
                    hslToHex((h + 90) % 360, s, l),
                    hslToHex((h + 180) % 360, s, l),
                    hslToHex((h + 270) % 360, s, l),
                    hslToHex(h, Math.max(20, s - 30), l)
                ]
            };
        }

        function displayColorHarmony(baseColor) {
            const harmonies = generateColorHarmony(baseColor);
            const harmonyNames = {
                monochromatic: '🎨 Đơn sắc (Monochromatic)',
                analogous: '🌈 Tương tự (Analogous)',
                complementary: '⚖️ Bổ sung (Complementary)',
                triadic: '🔺 Tam giác (Triadic)',
                tetradic: '⬜ Tứ giác (Tetradic)'
            };

            const container = document.getElementById('harmonyContainer');
            container.innerHTML = '';

            Object.entries(harmonies).forEach(([type, colors]) => {
                const harmonyCard = document.createElement('div');
                harmonyCard.className = 'harmony-card';
                
                const harmonyColors = colors.map(color => 
                    `<div class="harmony-color" style="background-color: ${color}" 
                          onclick="copyToClipboard('${color}')" title="${color}"></div>`
                ).join('');

                harmonyCard.innerHTML = `
                    <div class="harmony-title">${harmonyNames[type]}</div>
                    <div class="harmony-colors">${harmonyColors}</div>
                    <button class="btn" style="margin-top: 15px; width: 100%; font-size: 0.9em;" 
                            onclick="generatePaletteFromHarmony(['${colors.join("','")}'], '${harmonyNames[type]}')">
                        ✨ Tạo bảng màu từ này
                    </button>
                `;

                container.appendChild(harmonyCard);
            });

            document.getElementById('colorHarmonySection').style.display = 'block';
        }

        function generateFromColor() {
            if (isGenerating) return;
            
            // Validate hex input before proceeding
            const hexInput = document.getElementById('hexInput');
            if (hexInput.value && !validateHexInput()) {
                showToast('❌ Vui lòng nhập mã màu hex hợp lệ!');
                hexInput.focus();
                return;
            }
            
            isGenerating = true;
            const btn = document.getElementById('generateBtn');
            btn.innerHTML = '<span class="loading"></span>Đang tạo...';
            
            setTimeout(() => {
                const baseColor = document.getElementById('baseColor').value;
                displayColorHarmony(baseColor);
                generateWebsitePalettes(baseColor);
                
                btn.innerHTML = '✨ Tạo Bảng Màu <span class="success-indicator">✓</span>';
                isGenerating = false;
                
                // Auto switch to results tab
                setTimeout(() => {
                    switchTab(2);
                }, 500);
                
                setTimeout(() => {
                    btn.innerHTML = '✨ Tạo Bảng Màu';
                }, 2000);
            }, 800);
        }

        function generateWebsitePalettes(baseColor) {
            const [h, s, l] = hexToHsl(baseColor);
            
            const palettes = [
                {
                    name: '🌟 Modern & Clean',
                    description: 'Phù hợp cho website hiện đại, sạch sẽ',
                    colors: [
                        baseColor,
                        '#ffffff',
                        '#f8f9fa',
                        hslToHex(h, Math.max(10, s - 40), 15),
                        hslToHex(h, s, Math.max(20, l - 20))
                    ]
                },
                {
                    name: '🌙 Dark Theme',
                    description: 'Cho website tối màu, chuyên nghiệp',
                    colors: [
                        baseColor,
                        '#1a1a1a',
                        '#2d2d2d',
                        '#404040',
                        hslToHex(h, s, Math.min(80, l + 30))
                    ]
                },
                {
                    name: '🎨 Gradient Style',
                    description: 'Phong cách gradient, năng động',
                    colors: [
                        baseColor,
                        hslToHex((h + 30) % 360, s, l),
                        hslToHex((h - 30 + 360) % 360, s, l),
                        '#ffffff',
                        hslToHex(h, Math.max(20, s - 20), Math.max(20, l - 40))
                    ]
                },
                {
                    name: '💼 Professional',
                    description: 'Màu sắc chuyên nghiệp, tin cậy',
                    colors: [
                        baseColor,
                        '#2c3e50',
                        '#ecf0f1',
                        '#bdc3c7',
                        hslToHex(h, Math.max(20, s - 30), Math.max(30, l - 20))
                    ]
                }
            ];

            displayPalettes(palettes);
        }

        function generateRandomPalette() {
            const randomHue = Math.floor(Math.random() * 360);
            const randomColor = hslToHex(randomHue, 60 + Math.random() * 30, 45 + Math.random() * 20);
            
            // Update both inputs
            document.getElementById('baseColor').value = randomColor;
            document.getElementById('hexInput').value = randomColor.toUpperCase();
            validateHexInput();
            
            generateFromColor();
        }

        function loadPresetPalettes() {
            const presetPalettes = [
                {
                    name: '🚀 Tech Startup',
                    description: 'Xanh dương hiện đại, năng động',
                    colors: ['#667eea', '#764ba2', '#ffffff', '#f8f9fa', '#2d3748']
                },
                {
                    name: '🌿 Nature & Eco',
                    description: 'Xanh lá tự nhiên, thân thiện',
                    colors: ['#48bb78', '#68d391', '#ffffff', '#f7fafc', '#2d3748']
                },
                {
                    name: '🔥 Energy & Bold',
                    description: 'Đỏ cam mạnh mẽ, thu hút',
                    colors: ['#ed8936', '#f56500', '#ffffff', '#fffaf0', '#1a202c']
                },
                {
                    name: '💜 Creative & Art',
                    description: 'Tím sáng tạo, nghệ thuật',
                    colors: ['#9f7aea', '#805ad5', '#ffffff', '#faf5ff', '#322659']
                },
                {
                    name: '🌊 Ocean Blue',
                    description: 'Xanh biển tươi mát, thanh lịch',
                    colors: ['#4299e1', '#3182ce', '#ffffff', '#ebf8ff', '#2a4365']
                },
                {
                    name: '🌸 Soft Pink',
                    description: 'Hồng nhẹ nhàng, nữ tính',
                    colors: ['#ed64a6', '#d53f8c', '#ffffff', '#fef5e7', '#97266d']
                }
            ];

            displayPalettes(presetPalettes);
            document.getElementById('palettesSection').style.display = 'block';
            switchTab(2);
        }

        function generatePaletteFromHarmony(colors, name) {
            const palette = {
                name: name || '🎨 Bảng Màu Đã Chọn',
                description: 'Được tạo từ lý thuyết màu sắc',
                colors: colors
            };
            displayPalettes([palette]);
            document.getElementById('palettesSection').style.display = 'block';
            switchTab(2);
        }

        function displayPalettes(palettes) {
            const container = document.getElementById('palettesContainer');
            container.innerHTML = '';

            palettes.forEach((palette, index) => {
                const paletteCard = document.createElement('div');
                paletteCard.className = 'palette-card';
                paletteCard.style.animationDelay = `${index * 0.1}s`;

                const colorRows = palette.colors.map(color => `
                    <div class="color-block" style="background-color: ${color}" 
                         onclick="copyToClipboard('${color}')" title="Click để copy ${color}">
                        <span class="color-code">${color}</span>
                    </div>
                `).join('');

                paletteCard.innerHTML = `
                    <div class="palette-title">${palette.name}</div>
                    ${palette.description ? `<p style="text-align: center; color: #666; margin-bottom: 15px; font-size: 0.9em;">${palette.description}</p>` : ''}
                    <div class="color-row">${colorRows}</div>
                    <button class="btn use-palette-btn" onclick="previewPalette(['${palette.colors.join("','")}'], '${palette.name}')" style="width: 100%; margin-top: 15px;">
                        👀 Preview Website ⬇️
                    </button>
                `;

                container.appendChild(paletteCard);
            });

            document.getElementById('palettesSection').style.display = 'block';
        }

        function extractColorsFromImage() {
            const input = document.getElementById('imageUpload');
            const file = input.files[0];
            
            if (!file) return;

            // Validate file size (5MB max)
            if (file.size > 5 * 1024 * 1024) {
                showToast('❌ File quá lớn! Vui lòng chọn ảnh dưới 5MB');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    // Display uploaded image
                    const container = document.getElementById('uploadedImageContainer');
                    container.innerHTML = `
                        <div style="text-align: center; margin-top: 20px;">
                            <img src="${e.target.result}" class="uploaded-image" alt="Uploaded image">
                            <p style="margin-top: 10px; color: #666;">✅ Ảnh đã được tải lên thành công!</p>
                        </div>
                    `;

                    // Extract colors using canvas
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    canvas.width = img.width;
                    canvas.height = img.height;
                    
                    ctx.drawImage(img, 0, 0);
                    
                    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                    const colors = extractDominantColors(imageData);
                    
                    generatePalettesFromImage(colors);
                    showToast('🎨 Đã trích xuất màu từ ảnh thành công!');
                    
                    // Auto switch to results tab
                    setTimeout(() => {
                        switchTab(2);
                    }, 1000);
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        function extractDominantColors(imageData) {
            const data = imageData.data;
            const colorCounts = {};
            const step = 10; // Sample every 10th pixel for performance

            for (let i = 0; i < data.length; i += 4 * step) {
                const r = data[i];
                const g = data[i + 1];
                const b = data[i + 2];
                const alpha = data[i + 3];

                if (alpha > 128) { // Only consider non-transparent pixels
                    const color = `rgb(${r},${g},${b})`;
                    colorCounts[color] = (colorCounts[color] || 0) + 1;
                }
            }

            // Get top colors and convert to hex
            const sortedColors = Object.entries(colorCounts)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 10)
                .map(([color]) => {
                    const matches = color.match(/rgb\((\d+),(\d+),(\d+)\)/);
                    const r = parseInt(matches[1]);
                    const g = parseInt(matches[2]);
                    const b = parseInt(matches[3]);
                    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
                });

            return sortedColors.slice(0, 5); // Return top 5 colors
        }

        function generatePalettesFromImage(imageColors) {
            const palettes = [
                {
                    name: '📸 Màu Từ Ảnh',
                    description: 'Các màu chủ đạo được trích xuất từ ảnh',
                    colors: imageColors
                },
                {
                    name: '🤍 Ảnh + Minimalist',
                    description: 'Kết hợp màu ảnh với tông trắng xám tối giản',
                    colors: [
                        imageColors[0],
                        imageColors[1] || imageColors[0],
                        '#ffffff',
                        '#f5f5f5',
                        '#333333'
                    ]
                },
                {
                    name: '🖤 Ảnh + Dark Mode',
                    description: 'Kết hợp màu ảnh với theme tối',
                    colors: [
                        imageColors[0],
                        imageColors[1] || imageColors[0],
                        '#1a1a1a',
                        '#2d2d2d',
                        '#404040'
                    ]
                },
                {
                    name: '✨ Ảnh + Luxury',
                    description: 'Kết hợp màu ảnh với tông sang trọng',
                    colors: [
                        imageColors[0],
                        imageColors[1] || imageColors[0],
                        '#f8f6f0',
                        '#d4af37',
                        '#2c1810'
                    ]
                }
            ];

            displayPalettes(palettes);
            
            // Also show color harmony for the dominant color
            if (imageColors.length > 0) {
                displayColorHarmony(imageColors[0]);
            }
        }

        function previewPalette(colors, paletteName) {
            document.getElementById('previewSection').style.display = 'block';
            
            // Function to determine if color is light or dark for text contrast
            function isLightColor(hex) {
                const r = parseInt(hex.slice(1, 3), 16);
                const g = parseInt(hex.slice(3, 5), 16);
                const b = parseInt(hex.slice(5, 7), 16);
                const brightness = (r * 299 + g * 587 + b * 114) / 1000;
                return brightness > 155;
            }
            
            // Apply colors to preview with proper contrast
            const headerBg = `linear-gradient(135deg, ${colors[0]} 0%, ${colors[1] || colors[0]} 100%)`;
            document.getElementById('previewHeader').style.background = headerBg;
            document.getElementById('previewHeader').style.color = isLightColor(colors[0]) ? '#333333' : '#ffffff';
            
            // Card 1 styling
            const card1Color = colors[2] || colors[0];
            document.getElementById('previewCard1').style.backgroundColor = card1Color;
            document.getElementById('previewCard1').style.color = isLightColor(card1Color) ? '#333333' : '#ffffff';
            
            // Card 2 styling  
            const card2Color = colors[3] || colors[1] || colors[0];
            document.getElementById('previewCard2').style.backgroundColor = card2Color;
            document.getElementById('previewCard2').style.color = isLightColor(card2Color) ? '#333333' : '#ffffff';
            
            // Update preview content background and text
            const contentBg = colors[4] || '#ffffff';
            const previewContent = document.querySelector('.preview-content');
            previewContent.style.backgroundColor = contentBg;
            
            // Update main content text colors
            const h2 = previewContent.querySelector('h2');
            const p = previewContent.querySelector('p');
            const tipBox = previewContent.querySelector('div[style*="background: #f8f9fa"]');
            
            if (isLightColor(contentBg)) {
                h2.style.color = '#333333';
                p.style.color = '#666666';
                tipBox.style.background = '#f0f0f0';
                tipBox.style.color = '#333333';
            } else {
                h2.style.color = '#ffffff';
                p.style.color = '#cccccc';
                tipBox.style.background = 'rgba(255,255,255,0.1)';
                tipBox.style.color = '#ffffff';
            }

            // Update preview title
            document.querySelector('.preview-title').textContent = `👀 Preview: ${paletteName || 'Bảng Màu'}`;

            showToast(`🎉 Đã tạo preview! Scroll xuống để xem website mẫu ⬇️`);
            
            // Show scroll indicator
            const scrollIndicator = document.getElementById('scrollIndicator');
            scrollIndicator.classList.add('show');
            
            // Smooth scroll to preview section
            setTimeout(() => {
                document.getElementById('previewSection').scrollIntoView({ 
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // Add a subtle highlight effect
                const previewSection = document.getElementById('previewSection');
                previewSection.style.animation = 'highlightPreview 2s ease-in-out';
                setTimeout(() => {
                    previewSection.style.animation = '';
                }, 2000);
                
                // Hide scroll indicator after a few seconds
                setTimeout(() => {
                    scrollIndicator.classList.remove('show');
                }, 4000);
            }, 100);
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showToast(`📋 Đã copy ${text} vào clipboard!`);
            }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showToast(`📋 Đã copy ${text} vào clipboard!`);
            });
        }

        function showToast(message) {
            const toast = document.getElementById('toast');
            toast.textContent = message;
            toast.classList.add('show');
            
            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }

        // Initialize with a default palette
        document.addEventListener('DOMContentLoaded', function() {
            // Sync initial values
            syncColorInputs();
            
            // Auto-generate a sample palette
            setTimeout(() => {
                loadPresetPalettes();
            }, 500);
        });
    </script>
</body>
</html>