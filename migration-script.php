<?php
/**
 * VTS Theme Migration Script
 * 
 * Script để backup và chuyển đổi từ cấu trúc cũ sang cấu trúc mới
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class VTS_Migration_Script {
    
    private $backup_dir;
    private $theme_dir;
    
    public function __construct() {
        $this->theme_dir = get_template_directory();
        $this->backup_dir = $this->theme_dir . '/backup-' . date('Y-m-d-H-i-s');
    }
    
    /**
     * Run the complete migration process
     */
    public function run_migration() {
        echo "<h2>VTS Theme Migration Script</h2>";
        
        // Step 1: Create backup
        $this->create_backup();
        
        // Step 2: Verify new structure
        $this->verify_new_structure();
        
        // Step 3: Switch to new functions.php
        $this->switch_functions_file();
        
        // Step 4: Test functionality
        $this->test_functionality();
        
        echo "<p><strong>Migration completed successfully!</strong></p>";
        echo "<p>Backup created at: " . $this->backup_dir . "</p>";
    }
    
    /**
     * Create backup of current files
     */
    private function create_backup() {
        echo "<h3>Step 1: Creating backup...</h3>";
        
        // Create backup directory
        if (!wp_mkdir_p($this->backup_dir)) {
            wp_die('Cannot create backup directory');
        }
        
        // Files to backup
        $files_to_backup = [
            'functions.php',
            'inc/functions/',
        ];
        
        foreach ($files_to_backup as $file) {
            $source = $this->theme_dir . '/' . $file;
            $destination = $this->backup_dir . '/' . $file;
            
            if (is_file($source)) {
                copy($source, $destination);
                echo "<p>✅ Backed up: $file</p>";
            } elseif (is_dir($source)) {
                $this->copy_directory($source, $destination);
                echo "<p>✅ Backed up directory: $file</p>";
            }
        }
    }
    
    /**
     * Verify new structure exists
     */
    private function verify_new_structure() {
        echo "<h3>Step 2: Verifying new structure...</h3>";
        
        $required_files = [
            'functions-new.php',
            'inc/functions-new/autoloader.php',
            'inc/functions-new/core/theme-setup.php',
            'inc/functions-new/optimization/performance.php',
            'inc/functions-new/frontend/scripts-styles.php',
        ];
        
        foreach ($required_files as $file) {
            $file_path = $this->theme_dir . '/' . $file;
            if (file_exists($file_path)) {
                echo "<p>✅ Found: $file</p>";
            } else {
                wp_die("❌ Missing required file: $file");
            }
        }
    }
    
    /**
     * Switch to new functions.php
     */
    private function switch_functions_file() {
        echo "<h3>Step 3: Switching to new functions.php...</h3>";
        
        $old_functions = $this->theme_dir . '/functions.php';
        $new_functions = $this->theme_dir . '/functions-new.php';
        $temp_functions = $this->theme_dir . '/functions-old.php';
        
        // Rename old functions.php to functions-old.php
        if (file_exists($old_functions)) {
            rename($old_functions, $temp_functions);
            echo "<p>✅ Renamed old functions.php to functions-old.php</p>";
        }
        
        // Rename functions-new.php to functions.php
        if (file_exists($new_functions)) {
            rename($new_functions, $old_functions);
            echo "<p>✅ Activated new functions.php</p>";
        }
    }
    
    /**
     * Test basic functionality
     */
    private function test_functionality() {
        echo "<h3>Step 4: Testing functionality...</h3>";
        
        // Test autoloader
        if (class_exists('VTS_Functions_Autoloader')) {
            echo "<p>✅ Autoloader class loaded</p>";
            
            $loaded_files = VTS_Functions_Autoloader::get_loaded_files();
            echo "<p>✅ Loaded " . count($loaded_files) . " function files</p>";
        } else {
            echo "<p>❌ Autoloader not found</p>";
        }
        
        // Test theme support
        if (current_theme_supports('post-thumbnails')) {
            echo "<p>✅ Post thumbnails support active</p>";
        }
        
        if (current_theme_supports('woocommerce')) {
            echo "<p>✅ WooCommerce support active</p>";
        }
    }
    
    /**
     * Rollback to old structure
     */
    public function rollback() {
        echo "<h2>Rolling back to old structure...</h2>";
        
        $old_functions = $this->theme_dir . '/functions.php';
        $temp_functions = $this->theme_dir . '/functions-old.php';
        
        if (file_exists($temp_functions)) {
            // Remove new functions.php
            if (file_exists($old_functions)) {
                unlink($old_functions);
            }
            
            // Restore old functions.php
            rename($temp_functions, $old_functions);
            echo "<p>✅ Restored old functions.php</p>";
        }
        
        echo "<p><strong>Rollback completed!</strong></p>";
    }
    
    /**
     * Copy directory recursively
     */
    private function copy_directory($source, $destination) {
        if (!is_dir($destination)) {
            wp_mkdir_p($destination);
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($source, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $item) {
            $dest_path = $destination . DIRECTORY_SEPARATOR . $iterator->getSubPathName();
            
            if ($item->isDir()) {
                wp_mkdir_p($dest_path);
            } else {
                copy($item, $dest_path);
            }
        }
    }
}

// Only run if accessed directly with proper authentication
if (isset($_GET['run_migration']) && current_user_can('administrator')) {
    $migration = new VTS_Migration_Script();
    
    if (isset($_GET['rollback'])) {
        $migration->rollback();
    } else {
        $migration->run_migration();
    }
} elseif (isset($_GET['show_migration_panel']) && current_user_can('administrator')) {
    ?>
    <div style="max-width: 800px; margin: 20px auto; padding: 20px; background: #fff; border: 1px solid #ddd;">
        <h2>VTS Theme Migration Panel</h2>
        <p>This script will migrate your theme from the old structure to the new optimized structure.</p>
        
        <h3>What will happen:</h3>
        <ul>
            <li>✅ Create backup of current files</li>
            <li>✅ Verify new structure exists</li>
            <li>✅ Switch to new functions.php</li>
            <li>✅ Test basic functionality</li>
        </ul>
        
        <h3>Actions:</h3>
        <p>
            <a href="?run_migration=1" class="button button-primary" onclick="return confirm('Are you sure you want to run the migration?')">
                Run Migration
            </a>
            
            <a href="?run_migration=1&rollback=1" class="button" onclick="return confirm('Are you sure you want to rollback?')">
                Rollback to Old Structure
            </a>
        </p>
        
        <h3>Manual Steps:</h3>
        <ol>
            <li>Test your website thoroughly after migration</li>
            <li>Check all WooCommerce functionality</li>
            <li>Verify admin panel works correctly</li>
            <li>Test frontend performance</li>
        </ol>
    </div>
    <?php
}
