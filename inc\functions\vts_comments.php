<?php

/* Comments output html
*===============================================================*/
function vts_comments( $comment, $args, $depth ) {
    // Get correct tag used for the comments
    if ( 'div' === $args['style'] ) {
        $tag       = 'div';
        $add_below = 'comment';
    } else {
        $tag       = 'li';
        $add_below = 'div-comment';
    } ?>

    <<?php echo $tag; ?> <?php comment_class( empty( $args['has_children'] ) ? '' : 'parent' ); ?> id="comment-<?php comment_ID() ?>">

            <div id="div-comment-<?php comment_ID() ?>" class="comment-body clearfix">

            <div class="avatar">
                <?php
                // Display avatar unless size is set to 0
                if ( $args['avatar_size'] != 0 ) {
                    $avatar_size = ! empty( $args['avatar_size'] ) ? $args['avatar_size'] : 70; // set default avatar size
                        echo get_avatar( $comment, $avatar_size );
                }
                ?>
            </div><!-- .comment-author -->
            <div class="vts-comment-text">
                <div class="author">
                    <span>
                        <?php 
                            // Display author name
                            printf( __( '<cite class="fn">%s</cite> <span class="says">Viết:</span>', 'vutruso' ), 
                            get_comment_author_link() ); 
                         ?>
                    </span>
                    <div class="date">
                        <a href="<?php echo get_permalink($comment->comment_post_ID);?>#comment-<?php comment_ID() ?>">
                            <?php
                                /* translators: 1: date, 2: time */
                                printf(
                                    __( '%1$s lúc %2$s', 'vutruso' ),
                                    get_comment_date(),
                                    get_comment_time()
                                ); 
                            ?>
                        </a>
                    </div>
                </div>
                <div class="comment-meta commentmetadata">
                    <a href="<?php echo htmlspecialchars( get_comment_link( $comment->comment_ID ) ); ?>"></a>
                    <?php edit_comment_link( __( '(Edit)', 'vutruso' ), '  ', '' ); ?>
                </div><!-- .comment-meta -->
                <div class="comment-content"><?php comment_text(); ?></div><!-- .comment-text -->
                <?php
                // Display comment moderation text
                if ( $comment->comment_approved == '0' ) { ?>
                    <em class="comment-awaiting-moderation"><?php _e( 'Bình luận của bạn đang chờ kiểm duyệt', 'vutruso' ); ?></em><br/><?php
                } ?>
                <div class="reply">
                    <?php
                            // Display comment reply link
                            comment_reply_link( array_merge( $args, array(
                                'add_below' => $add_below,
                                'depth'     => $depth,
                                'max_depth' => $args['max_depth']
                            ) ) ); 
                    ?>
                </div>
            </div><!-- .comment-details -->
    <?php
}


/* Remove website URL field from comment forms
*===============================================================*/
function remove_url_from_comments($fields) {
unset($fields['url']);
return $fields;
}
add_filter('comment_form_default_fields','remove_url_from_comments');



/* Remote set_comment_cookies checkbox in comment form
*===============================================================*/
remove_action( 'set_comment_cookies', 'wp_set_comment_cookies' );