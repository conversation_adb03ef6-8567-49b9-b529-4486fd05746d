<!DOCTYPE html>
<html lang="vi" itemscope itemtype="http://schema.org/WebPage">
<head itemscope itemtype="https://schema.org/WebSite">
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=5.0">
<meta name="theme-color" content="#0076b6" />
<?php if( is_front_page() ) :?>
<link rel="preload" as="image" href="https://vutruso.com/wp-content/uploads/2021/11/vu-tru-so.webp">
<?php endif; ?>
<link rel="preload" href="https://vutruso.com/wp-content/themes/vutruso/fonts/google-fonts/montserrat-v15-vietnamese_latin-600.woff2" as="font" type="font/woff2" crossorigin>
<link rel="preload" href="https://vutruso.com/wp-content/themes/vutruso/fonts/google-fonts/montserrat-v15-vietnamese_latin-regular.woff2" as="font" type="font/woff2" crossorigin>
<link rel="preload" href="https://vutruso.com/wp-content/themes/vutruso/fonts/google-fonts/baloo-chettan-2-v1-vietnamese_latin-700.woff2" as="font" type="font/woff2" crossorigin>
<style>@font-face { font-family: 'Montserrat'; font-style: normal; font-weight: 400; font-display: swap; src: url('https://vutruso.com/wp-content/themes/vutruso/fonts/google-fonts/montserrat-v15-vietnamese_latin-regular.woff2') format('woff2'), url('https://vutruso.com/wp-content/themes/vutruso/fonts/google-fonts/montserrat-v15-vietnamese_latin-regular.woff') format('woff'); unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD, U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB; } @font-face { font-family: 'Montserrat'; font-style: normal; font-weight: 600; font-display: swap; src: url('https://vutruso.com/wp-content/themes/vutruso/fonts/google-fonts/montserrat-v15-vietnamese_latin-600.woff2') format('woff2'), url('https://vutruso.com/wp-content/themes/vutruso/fonts/google-fonts/montserrat-v15-vietnamese_latin-600.woff') format('woff'); unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD, U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB; } @font-face { font-family: 'Baloo Chettan 2'; font-style: normal; font-weight: 700; font-display: swap; src: url('https://vutruso.com/wp-content/themes/vutruso/fonts/google-fonts/baloo-chettan-2-v1-vietnamese_latin-700.woff2') format('woff2'), url('https://vutruso.com/wp-content/themes/vutruso/fonts/google-fonts/baloo-chettan-2-v1-vietnamese_latin-700.woff') format('woff'); unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD, U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB; } body { font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif; }</style>
<?php wp_head(); ?>
<?php if(is_single()){ $image_schema = get_the_post_thumbnail_url(); $title_schema = get_the_title(); echo '<script type="application/ld+json"> { "@type": "ImageObject", "@id": "https://vutruso.com/#primaryimage", "inLanguage": "vi", "url": "'.$image_schema.'", "width": 1140, "height": 641, "caption": "'.$title_schema.'" } </script>'; echo '<script type="application/ld+json"> { "@type": "WebSite", "@id": "https://vutruso.com/#website", "url": "https://vutruso.com", "name": "Vũ Trụ Số - Chất lượng số!", "description": "", "potentialAction": [ { "@type": "SearchAction", "target": { "@type": "EntryPoint", "urlTemplate": "https://vutruso.com/?s={search_term_string}" }, "query-input": "required name=search_term_string" } ], "inLanguage": "vi" } </script>'; } ?>

<script type='application/ld+json'> { "@context": "http://www.schema.org", "@type": "ProfessionalService", "name": "Vũ Trụ Số", "url": "https://vutruso.com/", "sameAs": [ "https://www.pinterest.com/vutruso/", "https://twitter.com/vutruso", "https://www.facebook.com/vutruso", "https://vutruso.medium.com/", "https://vutruso.tumblr.com/", "https://www.behance.net/vutruso", "https://sites.google.com/view/vutruso/home", "https://www.linkedin.com/company/vutruso/about/", "https://www.youtube.com/channel/UCOEXvu-rxMQ6Tvqm1kZJUrg/about" ], "logo": "https://vutruso.com/wp-content/uploads/2020/07/vutruso_login.jpg", "image": "https://vutruso.com/wp-content/uploads/2022/05/logo.png", "description": "Vũ Trụ Số chuyên cung cấp các giải pháp thiết kế website, quản trị website cho doanh nghiệp, cung cấp máy chủ ảo VPS/Hosting, dịch vụ chạy quảng cáo Google, quảng cáo Facebook, quảng cáo Zalo, các dịch vụ bảo mật website WordPress, tăng tốc website WordPress và SEO từ khóa lên top Google", "address": { "@type": "PostalAddress", "streetAddress": "84/47/12 Bùi Quang Là, Phường 12", "addressLocality": "Thành phố Hồ Chí Minh ", "addressRegion": "Gò Vấp", "postalCode": "700000", "addressCountry": "Việt Nam" }, "geo": { "@type": "GeoCoordinates", "latitude": "10.8343046", "longitude": "106.6379302" }, "hasMap": "https://g.page/vutruso", "openingHours": "Mo, We, Th, Fr, Sa 08:00-17:00 Tu 07:30-17:00", "contactPoint": { "@type": "ContactPoint", "telephone": "+84868017791", "contactType": "Customer support" } } </script>
</head>
<body <?php body_class() ?> itemscope itemtype="https://schema.org/WebPage">
<?php wp_body_open(); ?>
    <header itemscope="itemscope" itemtype="https://schema.org/WPHeader">
        <div id="topbar" class="container-fluid">
            <div class="row" style=" max-width: 1286px; margin: 0 auto;">
                <div class="user col-lg-8">
                <svg style="fill: #c82020;" width="17" viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M1152 896q0 106-75 181t-181 75-181-75-75-181 75-181 181-75 181 75 75 181zm128 0q0-159-112.5-271.5t-271.5-112.5-271.5 112.5-112.5 271.5 112.5 271.5 271.5 112.5 271.5-112.5 112.5-271.5zm128 0q0 212-150 362t-362 150-362-150-150-362 150-362 362-150 362 150 150 362zm128 0q0-130-51-248.5t-136.5-204-204-136.5-248.5-51-248.5 51-204 136.5-136.5 204-51 248.5 51 248.5 136.5 204 204 136.5 248.5 51 248.5-51 204-136.5 136.5-204 51-248.5zm128 0q0 209-103 385.5t-279.5 279.5-385.5 103-385.5-103-279.5-279.5-103-385.5 103-385.5 279.5-279.5 385.5-103 385.5 103 279.5 279.5 103 385.5z"/></svg>
                    <strong itemprop="name">Vũ Trụ Số</strong> - Chất lượng số!
                </div> 
                <div class="contact col-lg-4">
                    <?php //if(wp_is_mobile()) : ?>
                    <div class="logo-mobile" itemscope itemtype="https://schema.org/Brand">
                        <span class="logo-title" itemprop="name">
                            <a itemprop="url" href="<?php echo esc_url(home_url()); ?>" title="Vũ Trụ Số - Chất lượng số">
                                <span style="font-family: 'Baloo Chettan 2'!important; font-size: 36px; color: #0076b6; font-weight: 700;">Vũ Trụ Số</span>
                            </a>
                        </span>
                    </div>
                    <?php //endif; ?>
                    <div class="social">
                        <?php if(is_user_logged_in()) { ?>

                            <a class="only-desktop" aria-label="Đăng xuất" href="<?php echo wp_logout_url(); ?>" title="Đăng xuất">
                                <span class="facebook"><svg style="fill: #4f79b1; margin-left: 6px; margin-top: 6px;" width="22"  height="22" viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M1664 896q0 156-61 298t-164 245-245 164-298 61-298-61-245-164-164-245-61-298q0-182 80.5-343t226.5-270q43-32 95.5-25t83.5 50q32 42 24.5 94.5t-49.5 84.5q-98 74-151.5 181t-53.5 228q0 104 40.5 198.5t109.5 163.5 163.5 109.5 198.5 40.5 198.5-40.5 163.5-109.5 109.5-163.5 40.5-198.5q0-121-53.5-228t-151.5-181q-42-32-49.5-84.5t24.5-94.5q31-43 84-50t95 25q146 109 226.5 270t80.5 343zm-640-768v640q0 52-38 90t-90 38-90-38-38-90v-640q0-52 38-90t90-38 90 38 38 90z"/></svg></span>
                            </a>

                        <?php } else { ?>

                            <a class="only-desktop" aria-label="Đăng nhập" href="https://vutruso.com/tai-khoan/" title="Đăng nhập">
                                <span class="facebook"><svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-login-2" width="35" height="35" viewBox="0 0 24 24" stroke-width="1.5" stroke="#2c3e50" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M9 8v-2a2 2 0 0 1 2 -2h7a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-7a2 2 0 0 1 -2 -2v-2" />
  <path d="M3 12h13l-3 -3" />
  <path d="M13 15l3 -3" />
</svg></span>
                            </a>

                        <?php } ?>
                        <a id="search-btn" title="Tìm kiếm...">
                        <svg style="fill: #4f79b1; margin-left: 6px; margin-top: -2px;" width="22" viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M1216 832q0-185-131.5-316.5t-316.5-131.5-316.5 131.5-131.5 316.5 131.5 316.5 316.5 131.5 316.5-131.5 131.5-316.5zm512 832q0 52-38 90t-90 38q-54 0-90-38l-343-342q-179 124-399 124-143 0-273.5-55.5t-225-150-150-225-55.5-273.5 55.5-273.5 150-225 225-150 273.5-55.5 273.5 55.5 225 150 150 225 55.5 273.5q0 220-124 399l343 343q37 37 37 90z"/></svg></a>
                        <!-- Callback Search here-->

                        <!-- mobile navigation -->
                        <div class="nav-mobile navbar-wrapper par mobile-show push_menu">
                            <div class="navbar navbar-inverse push-nav">
                                <button type="button" class="navbar-toggle">
                                    <span class="sr-only">Toggle navigation</span>
                                    <span class="icon-bar"></span>
                                    <span class="icon-bar"></span>
                                    <span class="icon-bar"></span>
                                </button>
                            </div>
                        </div>
                        <!--end mobile navigation-->
                    </div>
                    <div class="telephone">
                        <i><svg width="30" height="30" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" d="M20.25 3.75v4.5m0-4.5h-4.5m4.5 0-6 6m3 12c-8.284 0-15-6.716-15-15V4.5A2.25 2.25 0 0 1 4.5 2.25h1.372c.516 0 .966.351 1.091.852l1.106 4.423c.11.44-.054.902-.417 1.173l-1.293.97a1.062 1.062 0 0 0-.38 1.21 12.035 12.035 0 0 0 7.143 7.143c.441.162.928-.004 1.21-.38l.97-1.293a1.125 1.125 0 0 1 1.173-.417l4.423 1.106c.5.125.852.575.852 1.091V19.5a2.25 2.25 0 0 1-2.25 2.25h-2.25Z"></path></svg></i><a target="_blank" href="tel:0868017791">0868.01.77.91</a>
                    </div>

                </div>
            </div>
    </div><!-- end top -->

    <div class="container-fluid" id="menu">
        <div class="row" style=" max-width: 1286px; margin: 0 auto; ">
            <div class="logo col-2" itemscope itemtype="https://schema.org/Brand">

                    <span class="logo-title" itemprop="name">
                        <a itemprop="url" href="<?php echo esc_url(home_url()); ?>" title="Vũ Trụ Số - Chất lượng số">
                            <span style="font-family: 'Baloo Chettan 2'!important; font-size: 33px; color: #0076b6; font-weight: 700;">Vũ Trụ Số</span>
                            <strong itemprop="description"><?php bloginfo('description'); ?></strong>
                        </a>
                    </span>   

            </div>
                
            <div id="vts-menu" class="menu col-10 px-1" itemscope itemtype="https://schema.org/SiteNavigationElement">
                <?php 
                        if(has_nav_menu('main_menu')) : 
                                $args = array(
                                                "theme_location"        => 'main_menu',
                                                'container'             => '',
                                                'menu_class'            => 'menu',
                                );
                                wp_nav_menu($args);
                        endif;
                ?>
            </div>
        </div>
    </div><!-- end menu -->

    <div id="infoBlock">
        <div class="innerContainer">
            <div class="table row">
                <div class="cell phone col-4">
                    <div class="icon"><svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" style="fill: rgb(255 255 255);transform: ;msFilter: ;"><path d="M20 2H4c-1.103 0-2 .897-2 2v12c0 1.103.897 2 2 2h3v3.766L13.277 18H20c1.103 0 2-.897 2-2V4c0-1.103-.897-2-2-2zm0 14h-7.277L9 18.234V16H4V4h16v12z"></path><circle cx="15" cy="10" r="2"></circle><circle cx="9" cy="10" r="2"></circle></svg></div>
                      Click live chat qua Zalo<br>
                      <a  rel="noopener" target="_blank" href="https://zalo.me/0868017791">0868017791</a>
                </div>
                <div class="cell email col-4">
                    <div class="icon"><svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" style="fill: rgb(255 255 255);transform: ;msFilter: ;"><path d="M20 4H6c-1.103 0-2 .897-2 2v5h2V8l6.4 4.8a1.001 1.001 0 0 0 1.2 0L20 8v9h-8v2h8c1.103 0 2-.897 2-2V6c0-1.103-.897-2-2-2zm-7 6.75L6.666 6h12.668L13 10.75z"></path><path d="M2 12h7v2H2zm2 3h6v2H4zm3 3h4v2H7z"></path></svg></div>
                        Liên hệ với chúng tôi qua email<br>
                        <a  rel="noopener" target="_blank" href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="cell social col-4">
                  <div class="icon"><svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" style="fill: rgb(255 255 255);transform: ;msFilter: ;"><path d="M5.5 15a3.51 3.51 0 0 0 2.36-.93l6.26 3.58a3.06 3.06 0 0 0-.12.85 3.53 3.53 0 1 0 1.14-2.57l-6.26-3.58a2.74 2.74 0 0 0 .12-.76l6.15-3.52A3.49 3.49 0 1 0 14 5.5a3.35 3.35 0 0 0 .12.85L8.43 9.6A3.5 3.5 0 1 0 5.5 15zm12 2a1.5 1.5 0 1 1-1.5 1.5 1.5 1.5 0 0 1 1.5-1.5zm0-13A1.5 1.5 0 1 1 16 5.5 1.5 1.5 0 0 1 17.5 4zm-12 6A1.5 1.5 0 1 1 4 11.5 1.5 1.5 0 0 1 5.5 10z"></path></svg></div>
                      Kết nối với Vũ Trụ Số
                      <ul class="vtsSocial">
                          <li class="facebook">
                              <a aria-label="Facebook" href="https://www.facebook.com/vutruso" title="Vũ Trụ Số trên Facebook" rel="noopener" target="_blank"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" style="fill: rgb(251 189 0);transform: ;"><path d="M20 3H4a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1h8.615v-6.96h-2.338v-2.725h2.338v-2c0-2.325 1.42-3.592 3.5-3.592.699-.002 1.399.034 2.095.107v2.42h-1.435c-1.128 0-1.348.538-1.348 1.325v1.735h2.697l-.35 2.725h-2.348V21H20a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1z"></path></svg></a>
                          </li>
                          <li class="twitter">
                              <a aria-label="twitter" href="#" title="Vũ Trụ Số trên Twitter" rel="noopener"  target="_blank"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" style="fill: rgb(251 189 0);transform: ;msFilter:;"><path d="M19.633 7.997c.013.175.013.349.013.523 0 5.325-4.053 11.461-11.46 11.461-2.282 0-4.402-.661-6.186-1.809.324.037.636.05.973.05a8.07 8.07 0 0 0 5.001-1.721 4.036 4.036 0 0 1-3.767-2.793c.249.037.499.062.761.062.361 0 .724-.05 1.061-.137a4.027 4.027 0 0 1-3.23-3.953v-.05c.537.299 1.16.486 1.82.511a4.022 4.022 0 0 1-1.796-3.354c0-.748.199-1.434.548-2.032a11.457 11.457 0 0 0 8.306 4.215c-.062-.3-.1-.611-.1-.923a4.026 4.026 0 0 1 4.028-4.028c1.16 0 2.207.486 2.943 1.272a7.957 7.957 0 0 0 2.556-.973 4.02 4.02 0 0 1-1.771 2.22 8.073 8.073 0 0 0 2.319-.624 8.645 8.645 0 0 1-2.019 2.083z"></path></svg></a>
                          </li>
                          <li class="linkedin">
                              <a aria-label="Linkedin" rel="noopener" href="https://www.linkedin.com/company/vutruso/" title="Vũ Trụ Số trên Linkedin" target="_blank"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" style="fill: rgb(251 189 0);transform: ;msFilter:;"><path d="M20 3H4a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1zM8.339 18.337H5.667v-8.59h2.672v8.59zM7.003 8.574a1.548 1.548 0 1 1 0-3.096 1.548 1.548 0 0 1 0 3.096zm11.335 9.763h-2.669V14.16c0-.996-.018-2.277-1.388-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248h-2.667v-8.59h2.56v1.174h.037c.355-.675 1.227-1.387 2.524-1.387 2.704 0 3.203 1.778 3.203 4.092v4.71z"></path></svg></a>
                          </li>
                          <li class="pinterest">
                              <a aria-label="Pinterest" href="https://www.pinterest.com/vutruso/" title="VTS trên Pinterest" rel="noopener" target="_blank"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" style="fill: rgb(251 189 0);transform: ;msFilter:;"><path d="M11.99 2C6.472 2 2 6.473 2 11.99c0 4.232 2.633 7.85 6.35 9.306-.088-.79-.166-2.006.034-2.868.182-.78 1.172-4.966 1.172-4.966s-.299-.599-.299-1.484c0-1.388.805-2.425 1.808-2.425.853 0 1.264.64 1.264 1.407 0 .858-.546 2.139-.827 3.327-.235.994.499 1.805 1.479 1.805 1.775 0 3.141-1.872 3.141-4.575 0-2.392-1.719-4.064-4.173-4.064-2.843 0-4.512 2.132-4.512 4.335 0 .858.331 1.779.744 2.28a.3.3 0 0 1 .069.286c-.076.315-.245.994-.277 1.133-.044.183-.145.222-.335.134-1.247-.581-2.027-2.405-2.027-3.871 0-3.151 2.289-6.045 6.601-6.045 3.466 0 6.159 2.469 6.159 5.77 0 3.444-2.171 6.213-5.184 6.213-1.013 0-1.964-.525-2.29-1.146l-.623 2.374c-.225.868-.834 1.956-1.241 2.62a10 10 0 0 0 2.958.445c5.517 0 9.99-4.473 9.99-9.99S17.507 2 11.99 2"></path></svg></a>
                          </li>
                          <li class="rss">
                              <a aria-label="rss" href="https://vutruso.com/feed/" title="rss"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" style="fill: rgb(251 189 0);transform: ;msFilter:;"><path d="M19 20.001C19 11.729 12.271 5 4 5v2c7.168 0 13 5.832 13 13.001h2z"></path><path d="M12 20.001h2C14 14.486 9.514 10 4 10v2c4.411 0 8 3.589 8 8.001z"></path><circle cx="6" cy="18" r="2"></circle></svg></a>
                          </li>
                      </ul>
                </div>
            </div>
        </div>
    </div><!-- end infoBlock -->
</header>