:root {
	--accent-gradient: linear-gradient(135deg,#667eea 0%,#764ba2 100%);
	--gray-50: #f7fafc;
	--gray-100: #edf2f7;
	--gray-200: #e2e8f0;
	--gray-300: #cbd5e0;
	--gray-400: #a0aec0;
	--gray-500: #718096;
	--gray-600: #4a5568;
	--gray-700: #2d3748;
	--gray-800: #1a202c;
	--gray-900: #171923;
	--shadow-sm: 0 1px 3px 0 rgba(0,0,0,0.1), 0 1px 2px 0 rgba(0,0,0,0.06);
	--shadow-md: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
	--shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
	--shadow-xl: 0 20px 25px -5px rgba(0,0,0,0.1), 0 10px 10px -5px rgba(0,0,0,0.04);
	--border-radius-sm: 6px;
	--border-radius-md: 12px;
	--border-radius-lg: 16px;
	--border-radius-xl: 24px;
	--primary: #1a202c;
	--primary-light: #2d3748;
	--accent: #667eea;
	--accent-hover: #5a67d8;
	--accent-light: #e6fffa;
	--success: #48bb78;
	--warning: #ed8936;
	--danger: #f56565;
	--white: #ffffff;
	--text-primary: #2d3748;
	--text-secondary: #4a5568;
	--text-muted: #718096;
	--gradient-primary: linear-gradient(135deg,#667eea 0%,#764ba2 100%);
	--gradient-success: linear-gradient(135deg,#48bb78 0%,#38a169 100%);
	--gradient-danger: linear-gradient(135deg,#f56565 0%,#e53e3e 100%);
}

* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

html {
	scroll-behavior: smooth;
}

.header {
	position: fixed;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255,255,255,0.95);
	backdrop-filter: blur(20px);
	border-bottom: 1px solid var(--gray-200);
	transition: all 0.4s cubic-bezier(0.4,0,0.2,1);
}

.header.scrolled {
	background: rgba(255,255,255,0.98);
	box-shadow: var(--shadow-lg);
	border-bottom-color: var(--gray-300);
}

.header-inner {
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 60px;
	position: relative;
}

.logo {
	font-size: 1.325rem;
	font-weight: 600;
	color: var(--primary);
	text-decoration: none;
	display: flex;
	align-items: center;
	gap: 0.625rem;
	transition: all 0.3s ease;
	letter-spacing: -0.015em;
}

.logo:hover {
	transform: translateY(-1px);
}

.logo i {
	background: var(--accent-gradient);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	font-size: 1.75rem;
}

.logo-title a {
	background: linear-gradient( 30deg,#1865b0 0%,#1268b1 25%,#3a0ca3 50%,#4361ee 75%,#4cc9f0 100% );
	background-size: 200% auto;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	display: inline-block;
	text-decoration: none;
	white-space: nowrap;
	animation: textclip 2s linear infinite alternate-reverse;
}

.logo-title a span {
	font-family: "Baloo Chettan 2" !important;
	font-size: 33px;
	font-weight: 700;
	line-height: 1.2;
}

.nav {
	display: flex;
	align-items: center;
	gap: 3rem;
}

.nav-links {
	display: flex;
	list-style: none;
	gap: 2.5rem;
	margin: 0;
}

.nav-link {
	color: var(--text-secondary);
	text-decoration: none;
	font-weight: 500;
	font-size: 0.9rem;
	transition: all 0.3s cubic-bezier(0.4,0,0.2,1);
	position: relative;
	padding: 1.1rem 0;
	letter-spacing: -0.005em;
}

.nav-link:hover {
	color: var(--accent);
	transform: translateY(-1px);
}

.nav-link::after {
	content: "";
	position: absolute;
	bottom: 0;
	left: 0;
	width: 0;
	height: 2px;
	background: var(--accent-gradient);
	transition: width 0.3s cubic-bezier(0.4,0,0.2,1);
	border-radius: 1px;
}

.nav-link:hover::after, .nav-link.active::after {
	width: 100%;
}

.nav-link.active {
	color: var(--accent);
}

.header-cta {
	background: var(--accent-gradient);
	color: var(--white);
	padding: 0.5rem 1.75rem;
	border-radius: var(--border-radius-md);
	text-decoration: none;
	font-weight: 500;
	font-size: 0.9rem;
	transition: all 0.3s cubic-bezier(0.4,0,0.2,1);
	border: none;
	cursor: pointer;
	position: relative;
	overflow: hidden;
	letter-spacing: -0.005em;
}

.header-cta::before {
	content: "";
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient( 90deg, transparent, rgba(255,255,255,0.2), transparent );
	transition: left 0.5s;
}

.header-cta:hover::before {
	left: 100%;
}

.header-cta:hover {
	transform: translateY(-2px);
	box-shadow: var(--shadow-lg);
}

.mobile-toggle {
	display: none;
	background: none;
	border: none;
	color: var(--text-primary);
	font-size: 1.5rem;
	cursor: pointer;
	padding: 0.5rem;
	border-radius: var(--border-radius-sm);
	transition: transform 0.2s ease-out, color 0.2s ease-out;
	contain: layout style;
	will-change: transform;
	min-width: 44px;
	min-height: 44px;
}

.mobile-toggle:hover {
	transform: scale(1.1);
	color: var(--accent);
}

.mobile-toggle:active {
	transform: scale(0.95);
	transition-duration: 0.1s;
}

.mobile-sticky-cta {
	transform: translateY(100%);
	transition: transform 0.3s cubic-bezier(0.4,0,0.2,1);
	visibility: hidden;
}

.hero::before {
	background: radial-gradient( circle at 20% 80%, rgba(120,119,198,0.1) 0%, transparent 50% ), radial-gradient( circle at 80% 20%, rgba(255,119,198,0.1) 0%, transparent 50% );
	animation: float 6s ease-in-out infinite;
}

.mobile-toggle i {
	transition: transform 0.15s ease-out;
	will-change: transform;
	contain: layout style;
	transform-origin: center;
	display: inline-block;
}

.nav-links .nav-link {
	contain: layout style;
}

.header {
	contain: layout style;
}

.solution-card:hover, .problem-card:hover, .pricing-card:hover {
	transform: translateY(-8px) scale(1.02);
	transition: transform 0.3s cubic-bezier(0.4,0,0.2,1), box-shadow 0.3s cubic-bezier(0.4,0,0.2,1);
	will-change: transform;
	contain: layout style;
}

.fade-up, .scale-up {
	contain: layout style;
}

.header.scrolled {
	contain: layout style;
}

.nav-links {
	backface-visibility: hidden;
	perspective: 1000px;
}

.mobile-toggle {
	user-select: none;
	-webkit-user-select: none;
	-webkit-tap-highlight-color: transparent;
}

.fade-up {
	opacity: 0;
	transform: translateY(30px);
	transition: all 0.6s cubic-bezier(0.4,0,0.2,1);
}

.fade-up.visible {
	opacity: 1;
	transform: translateY(0);
}

.scale-up {
	opacity: 0;
	transform: scale(0.9);
	transition: all 0.6s cubic-bezier(0.4,0,0.2,1);
}

.scale-up.visible {
	opacity: 1;
	transform: scale(1);
}

.slide-left {
	opacity: 0;
	transform: translateX(-30px);
	transition: all 0.6s cubic-bezier(0.4,0,0.2,1);
}

.slide-left.visible {
	opacity: 1;
	transform: translateX(0);
}

.slide-right {
	opacity: 0;
	transform: translateX(30px);
	transition: all 0.6s cubic-bezier(0.4,0,0.2,1);
}

.slide-right.visible {
	opacity: 1;
	transform: translateX(0);
}

body:not(.loaded) * {
	animation-play-state: paused !important;
}

.loaded .fade-up {
	animation: fadeInUp 0.6s cubic-bezier(0.4,0,0.2,1) forwards;
}

.loaded .scale-up {
	animation: scaleIn 0.6s cubic-bezier(0.4,0,0.2,1) forwards;
}

body {
	font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
	line-height: 1.7;
	color: var(--text-primary);
	font-feature-settings: "cv02", "cv03", "cv04", "cv11";
	background: var(--white);
	overflow-x: hidden;
	font-size: 16px;
	letter-spacing: -0.01em;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.text-xs {
	font-size: 0.75rem;
	line-height: 1rem;
}

.text-sm {
	font-size: 0.875rem;
	line-height: 1.25rem;
}

.text-base {
	font-size: 1rem;
	line-height: 1.5rem;
}

.text-lg {
	font-size: 1.125rem;
	line-height: 1.75rem;
}

.text-xl {
	font-size: 1.25rem;
	line-height: 1.75rem;
}

.text-2xl {
	font-size: 1.5rem;
	line-height: 2rem;
}

.text-3xl {
	font-size: 1.875rem;
	line-height: 2.25rem;
}

.text-4xl {
	font-size: 2.25rem;
	line-height: 2.5rem;
}

.text-5xl {
	font-size: 3rem;
	line-height: 1.1;
}

.text-6xl {
	font-size: 3.75rem;
	line-height: 1.1;
}

.font-light {
	font-weight: 300;
}

.font-normal {
	font-weight: 400;
}

.font-medium {
	font-weight: 500;
}

.font-semibold {
	font-weight: 600;
}

.font-bold {
	font-weight: 700;
}

.font-extrabold {
	font-weight: 800;
}

.container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 2rem;
}

.section {
	padding: 2rem 0;
}

.section-alt {
	background: linear-gradient( 135deg, var(--gray-50) 0%, rgba(247,250,252,0.8) 100% );
	position: relative;
}

.section-alt::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23f1f5f9" fill-opacity="0.4"><circle cx="30" cy="30" r="1.5"/></g></g></svg>') repeat;
	opacity: 0.3;
	pointer-events: none;
}

.section-header {
	text-align: center;
	margin-bottom: 2rem;
	max-width: 800px;
	margin-left: auto;
	margin-right: auto;
	position: relative;
	z-index: 2;
}

.section-title {
	font-size: 1.75rem;
	font-weight: 600;
	color: var(--primary);
	margin-bottom: 1rem;
	letter-spacing: -0.015em;
	line-height: 1.3;
}

.section-subtitle {
	font-size: 0.9rem;
	color: var(--text-secondary);
	line-height: 1.6;
	font-weight: 400;
}

.hero {
	min-height: 100vh;
	display: flex;
	align-items: center;
	position: relative;
	overflow: hidden;
	background: linear-gradient(135deg,#f8fafc 0%,#e6fffa 50%,#f0f4ff 100%);
}

.hero::before {
	content: "";
	position: absolute;
	width: 200%;
	height: 200%;
	top: -50%;
	left: -50%;
	background: radial-gradient( circle at 20% 20%, rgba(102,126,234,0.1) 0%, transparent 50% ), radial-gradient( circle at 80% 80%, rgba(72,187,120,0.08) 0%, transparent 50% ), radial-gradient( circle at 40% 40%, rgba(118,75,162,0.06) 0%, transparent 50% );
	animation: float 20s ease-in-out infinite;
}

.hero::after {
	content: "";
	position: absolute;
	width: 100%;
	height: 100%;
	background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23667eea" opacity="0.05"><path d="M21.469 6.825c.84 1.537 1.318 3.3 1.318 5.175 0 3.979-2.156 7.456-5.363 9.325l3.295-9.527c.615-1.54.82-2.771.82-3.864 0-.405-.026-.78-.07-1.11m-7.981.105c.647-.03 1.232-.105 1.232-.105.582-.075.514-.93-.067-.899 0 0-1.755.135-2.88.135-1.064 0-2.85-.135-2.85-.135-.582-.03-.651.855-.068.899 0 0 .553.075 1.135.105l1.688 4.63L9.53 19.2l-3.067-8.645c.646-.03 1.231-.105 1.231-.105.582-.075.515-.93-.067-.899 0 0-1.755.135-2.88.135-.202 0-.44-.005-.69-.015C5.593 6.039 8.658 4.5 12.205 4.5c2.756 0 5.266 1.054 7.13 2.777-.045-.003-.087-.008-.132-.008-.582 0-.99.506-.99 1.051 0 .488.281.901.582 1.388.224.389.486.893.486 1.619 0 .506-.196 1.093-.451 1.908l-.595 1.98-2.17-6.468zm-9.061 5.414l2.759 7.556c-.973.285-2.005.439-3.074.439-1.261 0-2.472-.219-3.6-.617l3.915-7.378zM3.6 7.56c.26-2.24 1.175-4.24 2.6-5.85L8.109 6.35c.018.049.024.098.037.146L3.6 7.56z"/></svg>'), url('data: image/svg+xml,<svg xmlns="http: //www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2348bb78" opacity="0.03"><path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/></svg>');
	background-size: 80px 80px, 60px 60px;
	background-position: 0 0, 40px 40px;
	animation: drift 30s linear infinite;
	pointer-events: none;
}

.hero-content {
	position: relative;
	z-index: 10;
	max-width: 800px;
	text-align: center;
	margin: 0 auto;
	padding: 2rem 0;
}

.hero-badge {
	display: inline-flex;
	align-items: center;
	gap: 0.5rem;
	background: rgba(255,255,255,0.9);
	border: 1px solid rgba(102,126,234,0.2);
	padding: 0.5rem 1rem;
	border-radius: 50px;
	font-size: 0.875rem;
	font-weight: 600;
	color: var(--accent);
	margin-bottom: 2rem;
	backdrop-filter: blur(10px);
	box-shadow: 0 4px 15px rgba(102,126,234,0.1);
}

.hero-badge i {
	color: var(--success);
	animation: pulse 2s infinite;
}

.hero h1 {
	font-size: 3.5rem;
	font-weight: 800;
	margin-bottom: 1rem;
	line-height: 1.3;
	background: linear-gradient( 135deg, var(--primary) 0%, var(--accent) 50%, var(--primary) 100% );
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.hero-highlight {
	color: var(--accent);
	position: relative;
	display: inline-block;
}

.hero-highlight::after {
	content: "";
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 3px;
	background: var(--gradient-primary);
	border-radius: 2px;
	animation: expand 2s ease-out;
}

.hero-subtitle {
	font-size: 1.25rem;
	color: var(--text-secondary);
	margin-bottom: 2rem;
	font-weight: 400;
}
.trust-indicators {
	margin-top: 2rem;
	opacity: 0.7;
	display: flex;
	justify-content: center;
	gap: 1rem;
	margin: 2rem 0;
	flex-wrap: wrap;
}

.trust-item {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	background: rgba(255,255,255,0.8);
	padding: 0.75rem 1rem;
	border-radius: 50px;
	backdrop-filter: blur(10px);
	border: 1px solid rgba(102,126,234,0.1);
	transition: all 0.3s ease;
}

.trust-item:hover {
	transform: translateY(-2px);
	box-shadow: 0 8px 25px rgba(102,126,234,0.15);
}

.trust-item i {
	color: var(--success);
	font-size: 1.1rem;
}

.trust-item span {
	font-weight: 600;
	color: var(--text-primary);
	font-size: 0.9rem;
}

.hero-cta {
	display: flex;
	justify-content: center;
	gap: 1rem;
	margin: 2rem 0;
	flex-wrap: wrap;
}

.btn-primary {
	background: var(--gradient-primary);
	color: white;
	padding: 1rem 2rem;
	border-radius: 12px;
	text-decoration: none;
	font-weight: 600;
	font-size: 1.1rem;
	display: inline-flex;
	align-items: center;
	gap: 0.5rem;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
	border: none;
	cursor: pointer;
	box-shadow: 0 8px 25px rgba(102,126,234,0.3);
}

.btn-primary::before {
	content: "";
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient( 90deg, transparent, rgba(255,255,255,0.2), transparent );
	transition: left 0.5s;
}

.btn-primary:hover::before {
	left: 100%;
}

.btn-primary:hover {
	transform: translateY(-3px);
	box-shadow: 0 12px 35px rgba(102,126,234,0.4);
}

.btn-secondary {
	background: rgba(255,255,255,0.9);
	color: var(--text-primary);
	padding: 1rem 2rem;
	border: 2px solid rgba(102,126,234,0.2);
	border-radius: 12px;
	text-decoration: none;
	font-weight: 600;
	font-size: 1.1rem;
	display: inline-flex;
	align-items: center;
	gap: 0.5rem;
	transition: all 0.3s ease;
	backdrop-filter: blur(10px);
}

.btn-secondary:hover {
	border-color: var(--accent);
	color: var(--accent);
	transform: translateY(-2px);
	box-shadow: 0 8px 25px rgba(102,126,234,0.2);
}

.social-proof {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 1rem;
	margin-top: 2rem;
	opacity: 0.8;
}

.avatar-stack {
	display: flex;
	margin-left: -8px;
}

.avatar {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	border: 3px solid white;
	margin-left: -8px;
	background: var(--gradient-primary);
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	font-weight: 600;
	font-size: 0.9rem;
}

.avatar:nth-child(1) {
	background: linear-gradient(45deg,#667eea,#764ba2);
}

.avatar:nth-child(2) {
	background: linear-gradient(45deg,#f093fb,#f5576c);
}

.avatar:nth-child(3) {
	background: linear-gradient(45deg,#4facfe,#00f2fe);
}

.avatar:nth-child(4) {
	background: linear-gradient(45deg,#43e97b,#38f9d7);
}

.social-text {
	font-size: 0.9rem;
	color: var(--text-secondary);
}

.scarcity-bar-enhanced {
	background: linear-gradient(135deg,#1e40af,#3730a3);
	color: white;
	padding: 2rem 0;
	position: relative;
	overflow: hidden;
	box-shadow: 0 10px 30px rgba(30,64,175,0.3);
}

.scarcity-background {
	position: absolute;
	inset: 0;
	background: radial-gradient( circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50% ), radial-gradient( circle at 80% 80%, rgba(251,191,36,0.1) 0%, transparent 50% );
	animation: pulse-bg 4s ease-in-out infinite;
}

.scarcity-content {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 2rem;
	text-align: center;
	position: relative;
	z-index: 2;
}

.urgency-badge {
	display: inline-flex;
	align-items: center;
	gap: 0.5rem;
	background: #dc2626;
	padding: 0.5rem 1rem;
	border-radius: 20px;
	font-size: 0.8rem;
	font-weight: 600;
	margin-bottom: 1rem;
	animation: glow 2s ease-in-out infinite;
}

.urgency-banner-integrated {
	background: linear-gradient(135deg,#fef3c7,#fde68a);
	border: 1px solid #f59e0b;
	border-radius: 8px;
	padding: 0.75rem 1rem;
	margin: 1rem 0 1.5rem 0;
	text-align: center;
	font-size: 0.9rem;
	color: #92400e;
	animation: pulse-glow 3s ease-in-out infinite;
}

.trust-indicators-integrated {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background: rgba(255,255,255,0.7);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255,255,255,0.5);
	border-radius: 16px;
	padding: 0;
	margin: 1.5rem 0;
	box-shadow: 0 8px 32px rgba(0,0,0,0.08);
}

.trust-badges {
	display: flex;
	gap: 1.5rem;
	flex: 1;
}

.trust-badge {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	font-size: 0.85rem;
	font-weight: 500;
	color: var(--text-primary);
}

.trust-badge i {
	color: var(--success);
	font-size: 1rem;
}

.social-proof-inline {
	display: flex;
	align-items: center;
	gap: 0.75rem;
	font-size: 0.8rem;
	color: var(--text-muted);
}

.avatar-stack {
	display: flex;
	gap: -4px;
}

.avatar {
	width: 28px;
	height: 28px;
	border-radius: 50%;
	background: linear-gradient(45deg,#667eea,#764ba2);
	border: 2px solid white;
	margin-left: -4px;
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	font-size: 0.7rem;
	font-weight: 600;
	position: relative;
}

.main-offer h3 {
	font-size: 2rem;
	margin-bottom: 0.5rem;
	font-weight: 700;
}

.scarcity-stats {
	display: flex;
	justify-content: center;
	gap: 3rem;
	margin: 2rem 0;
}

.stat-item {
	text-align: center;
}

.stat-number {
	font-size: 2.5rem;
	font-weight: 800;
	color: #fbbf24;
	display: block;
	line-height: 1;
	animation: bounce-number 2s ease-in-out infinite;
}

.stat-label {
	font-size: 0.9rem;
	opacity: 0.8;
	margin-top: 0.25rem;
}

.scarcity-stats .stat-label {
	color: #fff;
}

.btn-scarcity {
	background: linear-gradient(135deg,#dc2626,#b91c1c);
	color: white;
	padding: 1rem 2.5rem;
	border-radius: 12px;
	text-decoration: none;
	font-weight: 700;
	font-size: 1.1rem;
	display: inline-flex;
	align-items: center;
	gap: 0.75rem;
	box-shadow: 0 8px 25px rgba(220,38,38,0.4);
	transition: all 0.3s ease;
	animation: cta-pulse 3s ease-in-out infinite;
	border: none;
	cursor: pointer;
}

.btn-scarcity:hover {
	transform: translateY(-3px) scale(1.02);
	box-shadow: 0 15px 35px rgba(220,38,38,0.6);
}

.guarantee-note {
	margin-top: 1rem;
	font-size: 0.9rem;
	opacity: 0.8;
}

.scarcity-bar-container {
	position: sticky;
	top: 60px;
	z-index: 999;
	margin: 0;
}

.scarcity-bar {
	background: linear-gradient(135deg,#dc2626,#b91c1c);
	color: white;
	box-shadow: 0 4px 20px rgba(220,38,38,0.3);
	position: relative;
	overflow: hidden;
	padding: 4px 0;
}

.spots-counter {
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
}

.scarcity-bar::before {
	content: "";
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient( 90deg, transparent, rgba(255,255,255,0.1), transparent );
	animation: shine 3s infinite;
}

.scarcity-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	max-width: 1200px;
	margin: 0 auto;
	position: relative;
	z-index: 2;
	padding: 0.25rem 2rem;
}

.scarcity-icon {
	font-size: 1.5rem;
	animation: pulse 2s infinite;
}

.scarcity-text {
	flex: 1;
	margin: 0 2rem;
}

.scarcity-main {
	display: block;
	font-size: 1.1rem;
	font-weight: 600;
	margin-bottom: 0;
}

.highlight-number {
	background: #fbbf24;
	color: #dc2626;
	padding: 0.2rem 0.5rem;
	border-radius: 4px;
	font-weight: 800;
}

.scarcity-sub {
	font-size: 0.8rem;
	opacity: 0.9;
}

.spots-remaining {
	text-align: center;
	background: rgba(255,255,255,0.15);
	padding: 0 0.75rem;
	border-radius: 8px;
	backdrop-filter: blur(10px);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	align-items: center;
	justify-content: center;
	align-content: center;
	gap: 0 2px;
}

.spots-label {
	font-size: 0.8rem;
	opacity: 0.8;
	margin-bottom: 0;
}

.spots-number {
	font-size: 1.2rem;
	font-weight: 800;
	color: #fbbf24;
	display: block;
	animation: countDown 1s ease-in-out infinite alternate;
	padding-right: 4px;
}

.spots-text {
	font-size: 0.8rem;
	opacity: 0.8;
}

.urgency-progress {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 4px;
	background: rgba(255,255,255,0.2);
}

.progress-fill {
	height: 100%;
	background: linear-gradient(90deg,#fbbf24,#f59e0b);
	transition: width 0.3s ease;
	animation: progressPulse 2s ease-in-out infinite;
}

.scarcity-bar-enhanced {
	background: linear-gradient(135deg,#1e3a8a,#3730a3);
	color: white;
	padding: 2rem 0;
	margin: 0;
	position: relative;
	overflow: hidden;
	box-shadow: 0 20px 40px rgba(30,58,138,0.3);
}

.scarcity-background {
	position: absolute;
	inset: 0;
	overflow: hidden;
}

.floating-icons {
	position: absolute;
	width: 100%;
	height: 100%;
}

.floating-icons i {
	position: absolute;
	font-size: 2rem;
	opacity: 0.1;
	animation: float 6s ease-in-out infinite;
}

.floating-icons i:nth-child(1) {
	top: 20%;
	left: 10%;
	animation-delay: 0s;
}

.floating-icons i:nth-child(2) {
	top: 60%;
	right: 20%;
	animation-delay: 2s;
}

.floating-icons i:nth-child(3) {
	top: 30%;
	right: 10%;
	animation-delay: 4s;
}

.scarcity-content-enhanced {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 2rem;
	text-align: center;
	position: relative;
	z-index: 2;
}

.urgency-badge {
	display: inline-flex;
	align-items: center;
	gap: 0.5rem;
	background: #dc2626;
	padding: 0.5rem 1rem;
	border-radius: 20px;
	font-size: 0.8rem;
	font-weight: 600;
	margin-bottom: 1rem;
	animation: badge-glow 2s ease-in-out infinite;
}

.main-offer h3 {
	font-size: 1.75rem;
	margin-bottom: 0.5rem;
	font-weight: 700;
}

.main-offer p {
	font-size: 1.1rem;
	opacity: 0.9;
	margin-bottom: 2rem;
}

.stat-number {
	font-size: 2.5rem;
	font-weight: 800;
	color: #fbbf24;
	display: block;
	line-height: 1;
}

.security-stat .stat-label {
	font-weight: 500;
	color: #000 !important;
}

.scarcity-cta {
	margin-top: 2rem;
}

.btn-scarcity {
	background: linear-gradient(135deg,#dc2626,#b91c1c);
	color: white;
	padding: 1rem 2.5rem;
	border-radius: 12px;
	text-decoration: none;
	font-weight: 700;
	font-size: 1.1rem;
	display: inline-flex;
	align-items: center;
	gap: 0.75rem;
	box-shadow: 0 8px 25px rgba(220,38,38,0.4);
	transition: all 0.3s ease;
	animation: cta-pulse 3s ease-in-out infinite;
}

.btn-scarcity:hover {
	transform: translateY(-3px) scale(1.05);
	box-shadow: 0 15px 35px rgba(220,38,38,0.6);
}

.hero-content {
	animation: slideInFromBottom 0.8s ease-out;
}

.chuyen-host-btn {
	text-align: center;
	display: block;
	width: 336px;
	margin: 0 auto;
}

.loading-skeleton {
	background: linear-gradient(90deg,#f0f0f0 25%,#e0e0e0 50%,#f0f0f0 75%);
	background-size: 200% 100%;
	animation: loading 1.5s infinite;
}

.problems-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(350px,1fr));
	gap: 1.5rem;
}

.problem-card {
	background: var(--white);
	border: 1px solid var(--gray-200);
	border-radius: var(--border-radius-xl);
	padding: 0.9rem;
	transition: all 0.4s cubic-bezier(0.4,0,0.2,1);
	position: relative;
	overflow: hidden;
	box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.problem-card::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 4px;
	background: linear-gradient(135deg, var(--danger) 0%, #ff6b6b 100%);
}

.problem-card:hover {
	transform: translateY(-6px);
	box-shadow: var(--shadow-xl);
	border-color: var(--danger);
}

.problem-icon {
	width: 56px;
	height: 56px;
	background: linear-gradient( 135deg, rgba(245,101,101,0.1) 0%, rgba(255,107,107,0.1) 100% );
	border-radius: var(--border-radius-lg);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 1rem;
	transition: all 0.3s ease;
}

.problem-card:hover .problem-icon {
	transform: scale(1.1);
	background: linear-gradient( 135deg, rgba(245,101,101,0.15) 0%, rgba(255,107,107,0.15) 100% );
}

.problem-icon i {
	color: var(--danger);
	font-size: 1.375rem;
	font-family: "Font Awesome 6 Free";
	font-weight: 900;
}

.problem-title {
	font-size: 1.125rem;
	font-weight: 600;
	color: var(--primary);
	margin-bottom: 0.875rem;
}

.problem-description {
	color: var(--text-secondary);
	line-height: 1.6;
	font-size: 0.95rem;
}

.solutions-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(280px,1fr));
	gap: 1.5rem;
}

.solution-card {
	background: var(--white);
	border: 1px solid var(--gray-200);
	border-radius: var(--border-radius-xl);
	padding: 0.9rem;
	text-align: center;
	transition: all 0.4s cubic-bezier(0.4,0,0.2,1);
	position: relative;
	overflow: hidden;
	box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.solution-card::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 4px;
	background: var(--accent-gradient);
}

.solution-card:hover {
	transform: translateY(-6px);
	box-shadow: var(--shadow-xl);
	border-color: var(--accent);
}

.solution-icon {
	width: 72px;
	height: 72px;
	background: linear-gradient( 135deg, rgba(102,126,234,0.1) 0%, rgba(118,75,162,0.1) 100% );
	border-radius: var(--border-radius-xl);
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 0.9rem;
	transition: all 0.3s ease;
}

.solution-card:hover .solution-icon {
	transform: scale(1.1) rotate(5deg);
	background: linear-gradient( 135deg, rgba(102,126,234,0.15) 0%, rgba(118,75,162,0.15) 100% );
}

.solution-icon i {
	background: var(--accent-gradient);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	font-size: 1.625rem;
}

.solution-title {
	font-size: 1.125rem;
	font-weight: 600;
	color: var(--primary);
	margin-bottom: 1rem;
	letter-spacing: -0.005em;
}

.solution-description {
	color: var(--text-secondary);
	line-height: 1.6;
	font-size: 0.95rem;
}

.tech-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(220px,1fr));
	gap: 1.5rem;
}

.tech-item {
	background: var(--white);
	border: 1px solid var(--gray-200);
	border-radius: 8px;
	padding: 0.9rem;
	text-align: center;
	transition: all 0.3s ease;
}

.tech-item:hover {
	transform: translateY(-2px);
	box-shadow: 0 8px 20px rgba(0,0,0,0.08);
	border-color: var(--accent);
}

.tech-icon {
	width: 48px;
	height: 48px;
	background: rgba(59,130,246,0.1);
	border-radius: 12px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 1rem;
}

.tech-icon i {
	color: var(--accent);
	font-size: 1.25rem;
}

.tech-name {
	font-weight: 600;
	color: var(--primary);
	margin-bottom: 0.5rem;
}

.tech-description {
	font-size: 0.85rem;
	color: var(--text-muted);
}

.stats {
	background: var(--primary);
	color: var(--white);
}

.stats-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px,1fr));
	gap: 3rem;
}

.stats-item {
	text-align: center;
}

.stats-number {
	font-size: 2rem;
	font-weight: 700;
	color: var(--white);
	margin-bottom: 0.5rem;
}

.stats-label {
	color: rgba(255,255,255,0.8);
	font-size: 1rem;
}

.pricing-wrapper {
	position: relative;
	margin-bottom: 4rem;
	padding-top: 2rem;
}

.pricing-grid {
	display: grid;
	grid-template-columns: repeat(3,1fr);
	gap: 2rem;
	max-width: 1100px;
	margin: 0 auto;
}

.pricing-card {
	background: white;
	border-radius: 16px;
	padding: 0;
	box-shadow: 0 4px 20px rgba(0,0,0,0.08);
	border: 2px solid #e5e7eb;
	border-top: 5px solid #667eea;
	position: relative;
	transition: all 0.3s ease;
	overflow: visible;
	display: flex;
	flex-direction: column;
	height: fit-content;
}

.pricing-card.popular {
	transform: scale(1.05);
	border-color: #3b82f6;
	border-top: 5px solid #3b82f6;
	box-shadow: 0 20px 40px rgba(59,130,246,0.2);
}

.pricing-card.enterprise {
	border-top: 5px solid #dc2626;
}

.pricing-card:hover {
	transform: translateY(-8px);
	box-shadow: 0 20px 40px rgba(0,0,0,0.12);
}

.pricing-card.popular:hover {
	transform: scale(1.05) translateY(-8px);
}

.popular-badge {
	position: absolute;
	top: -23px;
	left: 50%;
	transform: translateX(-50%);
	background: linear-gradient(135deg,#fbbf24,#f59e0b);
	color: #000;
	padding: 0.5rem 1.5rem;
	border-radius: 20px;
	font-size: 0.8rem;
	font-weight: 700;
	display: flex;
	align-items: center;
	gap: 0.25rem;
	box-shadow: 0 4px 12px rgba(251,191,36,0.4);
	z-index: 10;
}

.enterprise-badge {
	position: absolute;
	top: -19px;
	right: 1rem;
	background: linear-gradient(135deg,#dc2626,#b91c1c);
	color: white;
	padding: 0.4rem 0.8rem;
	border-radius: 15px;
	font-size: 0.75rem;
	font-weight: 700;
	display: flex;
	align-items: center;
	gap: 0.25rem;
	box-shadow: 0 4px 12px rgba(220,38,38,0.4);
	z-index: 10;
}

.pricing-card .card-content {
	padding: 1.4rem;
	flex: 1;
	display: flex;
	flex-direction: column;
}

.card-header {
	text-align: center;
	margin-bottom: 1.5rem;
}

.plan-name {
	font-size: 1.5rem;
	font-weight: 700;
	color: var(--primary);
	margin-bottom: 0.5rem;
}

.plan-description {
	font-size: 0.95rem;
	color: var(--text-secondary);
	margin-bottom: 1.5rem;
	line-height: 1.5;
}

.pricing-section {
	text-align: center;
	margin-bottom: 1.5rem;
}

.price-old {
	font-size: 1rem;
	color: var(--text-secondary);
	text-decoration: line-through;
	margin-bottom: 0.25rem;
	display: block;
}

.price-current {
	font-size: 2.5rem;
	font-weight: 800;
	color: var(--primary);
	line-height: 1;
	margin-bottom: 0.25rem;
}

.price-period {
	font-size: 0.9rem;
	color: var(--text-secondary);
	margin-bottom: 0.5rem;
}

.savings-badge {
	background: var(--success);
	color: white;
	padding: 0.25rem 0.75rem;
	border-radius: 12px;
	font-size: 0.8rem;
	font-weight: 600;
	display: inline-block;
}

.hosting-specs {
	background: #f8fafc;
	border-radius: 8px;
	padding: 6px;
	margin-bottom: 1.5rem;
}

.specs-title {
	font-size: 0.9rem;
	font-weight: 600;
	color: var(--primary);
	margin-bottom: 0.75rem;
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

.specs-list {
	display: grid;
	grid-template-columns: repeat(2,1fr);
	gap: 0.2rem;
}

.spec-item {
	display: flex;
	align-items: center;
	gap: 0.4rem;
	font-size: 0.8rem;
	color: var(--text-secondary);
}

.spec-item i {
	color: var(--success);
	font-size: 0.7rem;
	width: 12px;
}

.features-list {
	list-style: none;
	margin-bottom: 2rem;
	flex: 1;
}

.features-list li {
	display: flex;
	align-items: flex-start;
	gap: 0.75rem;
	margin-bottom: 0.75rem;
	font-size: 0.9rem;
	line-height: 1.4;
}

.features-list i {
	color: var(--success);
	font-size: 0.9rem;
	margin-top: 0.1rem;
	flex-shrink: 0;
}

.cta-section {
	margin-top: auto;
	text-align: center;
}

.btn-cta {
	background: linear-gradient(135deg, var(--accent), #5a67d8);
	color: white;
	padding: 1rem 1.5rem;
	border-radius: 8px;
	text-decoration: none;
	font-weight: 600;
	font-size: 0.95rem;
	transition: all 0.3s ease;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	gap: 0.5rem;
	width: 100%;
	border: none;
	cursor: pointer;
	box-shadow: 0 4px 12px rgba(102,126,234,0.3);
}

.btn-cta:hover {
	transform: translateY(-2px);
	box-shadow: 0 8px 20px rgba(102,126,234,0.4);
}

.pricing-card.popular .btn-cta {
	background: linear-gradient(135deg,#fbbf24,#f59e0b);
	color: #000;
	box-shadow: 0 4px 12px rgba(251,191,36,0.3);
}

.pricing-card.popular .btn-cta:hover {
	box-shadow: 0 8px 20px rgba(251,191,36,0.4);
}

.pricing-card.enterprise .btn-cta {
	background: linear-gradient(135deg,#dc2626,#b91c1c);
	box-shadow: 0 4px 12px rgba(220,38,38,0.3);
}

.pricing-card.enterprise .btn-cta:hover {
	box-shadow: 0 8px 20px rgba(220,38,38,0.4);
}

.guarantee {
	font-size: 0.8rem;
	color: var(--text-secondary);
	margin-top: 0.75rem;
	text-align: center;
}

.main-cta {
	text-align: center;
	margin-top: 3rem;
}

.btn-main {
	background: linear-gradient(135deg,#059669,#047857);
	color: white;
	padding: 1.25rem 2.5rem;
	border-radius: 12px;
	text-decoration: none;
	font-weight: 600;
	font-size: 1.1rem;
	transition: all 0.3s ease;
	display: inline-flex;
	align-items: center;
	gap: 0.75rem;
	box-shadow: 0 8px 25px rgba(5,150,105,0.3);
	border: none;
	cursor: pointer;
}

.btn-main:hover {
	transform: translateY(-3px);
	box-shadow: 0 12px 35px rgba(5,150,105,0.4);
}

.main-cta-subtitle {
	margin-top: 1rem;
	color: var(--text-secondary);
	font-size: 0.9rem;
}

.faq-container {
	max-width: 800px;
	margin: 0 auto;
}

.faq-item {
	background: var(--white);
	border: 1px solid var(--gray-200);
	border-radius: 8px;
	margin-bottom: 1rem;
	overflow: hidden;
}

.faq-question {
	width: 100%;
	padding: 1.2rem;
	background: none;
	border: none;
	text-align: left;
	font-size: 1.125rem;
	font-weight: 600;
	color: var(--primary);
	cursor: pointer;
	display: flex;
	justify-content: space-between;
	align-items: center;
	transition: all 0.2s ease;
}

.faq-question:hover {
	background: var(--gray-50);
}

.faq-question i {
	color: var(--accent);
	transition: transform 0.2s ease;
}

.faq-item.active .faq-question i {
	transform: rotate(180deg);
}

.faq-answer {
	padding: 0.5rem 1rem 1rem;
	color: var(--text-secondary);
	line-height: 1.6;
	display: none;
}

.faq-item.active .faq-answer {
	display: block;
}

.contact {
	background: var(--gray-900);
	color: var(--white);
	width: 100vw;
	position: relative;
	left: 50%;
	right: 50%;
	margin-left: -50vw;
	margin-right: -50vw;
}

.contact .section-title {
	color: var(--white);
}

.contact .section-subtitle {
	color: rgba(255,255,255,0.8);
}

.contact-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(250px,1fr));
	gap: 1.5rem;
	margin-top: 2rem;
}

.contact-item {
	text-align: center;
}

.contact-icon {
	width: 64px;
	height: 64px;
	background: var(--accent);
	border-radius: 16px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 1.5rem;
}

.contact-icon i {
	color: var(--white);
	font-size: 1.5rem;
}

.contact-title {
	font-size: 1.0625rem;
	font-weight: 600;
	margin-bottom: 0.5rem;
}

.contact-info {
	color: rgba(255,255,255,0.8);
	margin-bottom: 0.25rem;
}

.contact-cta {
	margin-top: 1.5rem;
	text-align: center;
}

.btn-white {
	background: var(--white);
	color: var(--primary);
	padding: 1rem 2.5rem;
	border-radius: 10px;
	text-decoration: none;
	font-weight: 600;
	font-size: 0.95rem;
	transition: all 0.3s cubic-bezier(0.4,0,0.2,1);
	display: inline-flex;
	align-items: center;
	gap: 0.75rem;
	letter-spacing: 0.025em;
	box-shadow: 0 4px 15px rgba(255,255,255,0.2);
	border: 2px solid transparent;
}

.btn-white:hover {
	transform: translateY(-3px);
	box-shadow: var(--shadow-xl);
	color: var(--accent);
}

.comparison-table {
	background: var(--white);
	border-radius: 12px;
	box-shadow: 0 10px 25px rgba(0,0,0,0.1);
	overflow: hidden;
	margin-top: 2rem;
	width: 100%;
}

.comparison-header {
	display: grid;
	grid-template-columns: 2fr 1.2fr 1.2fr;
	background: var(--primary);
	color: var(--white);
	font-weight: 600;
}

.comparison-header > div {
	padding: 1.5rem 1rem;
	text-align: center;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	min-height: 60px;
}

.comparison-header .comparison-feature {
	text-align: left;
	justify-content: flex-start;
	padding-left: 1.5rem;
}

.comparison-row {
	display: grid;
	grid-template-columns: 2fr 1.2fr 1.2fr;
	border-bottom: 1px solid var(--gray-200);
	align-items: stretch;
}

.comparison-row:last-child {
	border-bottom: none;
}

.comparison-row > div {
	padding: 1.25rem 1rem;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	text-align: left;
	min-height: 70px;
	word-wrap: break-word;
}

.comparison-feature {
	justify-content: flex-start !important;
	font-weight: 500;
	color: var(--text-primary);
	text-align: left !important;
	padding-left: 1.5rem !important;
}

.comparison-standard {
	color: var(--text-muted);
	font-size: 0.9rem;
	padding-left: 1.5rem;
}

.comparison-row i {
	padding-right: 5px;
}

.comparison-vutruso {
	background: rgba(16,185,129,0.05);
	color: var(--success);
	font-weight: 500;
	font-size: 0.9rem;
	padding-left: 1.5rem;
}

.text-success {
	color: var(--success);
}

.text-danger {
	color: var(--danger);
}

.testimonials-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(350px,1fr));
	gap: 1.5rem;
}

.testimonial-card {
	background: var(--white);
	border: 1px solid var(--gray-200);
	border-radius: 12px;
	padding: 2rem;
	transition: all 0.3s ease;
	position: relative;
}

.testimonial-card::before {
	content: '"';
	position: absolute;
	top: -10px;
	left: 2rem;
	font-size: 4rem;
	color: var(--accent);
	font-family: serif;
	line-height: 1;
}

.testimonial-card:hover {
	transform: translateY(-4px);
	box-shadow: 0 20px 25px -5px rgba(0,0,0,0.1);
	border-color: var(--accent);
}

.testimonial-content {
	color: var(--text-secondary);
	line-height: 1.7;
	margin-bottom: 2rem;
	font-style: italic;
	padding-top: 1rem;
}

.testimonial-author {
	display: flex;
	align-items: center;
	gap: 1rem;
}

.author-avatar {
	width: 50px;
	height: 50px;
	background: var(--accent);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	color: var(--white);
	font-size: 1.25rem;
}

.author-name {
	font-weight: 600;
	color: var(--primary);
	margin-bottom: 0.25rem;
}

.author-title {
	font-size: 0.875rem;
	color: var(--text-muted);
}

.process-timeline {
	position: relative;
	max-width: 800px;
	margin: 2rem auto;
}

.process-timeline::before {
	content: "";
	position: absolute;
	left: 30px;
	top: 50px;
	bottom: 50px;
	width: 2px;
	background: var(--gray-300);
}

.process-step {
	display: flex;
	align-items: flex-start;
	margin-bottom: 2rem;
	position: relative;
}

.step-number {
	width: 60px;
	height: 60px;
	background: var(--accent);
	color: var(--white);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 1.5rem;
	font-weight: 700;
	flex-shrink: 0;
	position: relative;
	z-index: 2;
}

.step-content {
	margin-left: 2rem;
	background: var(--white);
	border: 1px solid var(--gray-200);
	border-radius: 12px;
	padding: 1rem;
	flex: 1;
	box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.step-title {
	font-size: 1.25rem;
	font-weight: 600;
	color: var(--primary);
	margin-bottom: 1rem;
}

.step-description {
	color: var(--text-secondary);
	line-height: 1.6;
	margin-bottom: 1rem;
}

.step-time {
	font-size: 0.875rem;
	color: var(--accent);
	font-weight: 600;
	background: rgba(59,130,246,0.1);
	padding: 0.5rem 1rem;
	border-radius: 20px;
	display: inline-block;
}

.security-stats {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(250px,1fr));
	gap: 2rem;
	margin: 1rem 0;
}

.security-stat {
	text-align: center;
	background: var(--white);
	border: 1px solid var(--gray-200);
	border-radius: 12px;
	padding: 0.8rem;
	transition: all 0.3s ease;
}

.security-stat:hover {
	transform: translateY(-4px);
	box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.stat-icon {
	width: 64px;
	height: 64px;
	background: rgba(239,68,68,0.1);
	border-radius: 16px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 1.5rem;
}

.stat-icon i {
	color: var(--danger);
	font-size: 1.5rem;
}

.stat-number {
	font-size: 2rem;
	font-weight: 800;
	color: var(--danger);
	margin-bottom: 0.5rem;
}

.stat-label {
	color: var(--text-secondary);
	font-weight: 500;
}

.security-threats {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(476px,1fr));
	gap: 1.5rem;
	margin-top: 2.5rem;
}

.threat-item {
	background: var(--white);
	border: 1px solid var(--gray-200);
	border-radius: 8px;
	padding: 1.25rem;
	border-left: 4px solid var(--danger);
}

.threat-item h4 {
	color: var(--primary);
	margin-bottom: 1rem;
	font-size: 1rem;
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

.threat-item h4 i {
	color: var(--danger);
}

.threat-item p {
	color: var(--text-secondary);
	font-size: 0.9rem;
	line-height: 1.6;
	margin: 0;
	text-align: left;
}

.benchmark-results {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(400px,1fr));
	gap: 2.5rem;
	margin-top: 2.5rem;
}

.benchmark-card {
	background: var(--white);
	border: 1px solid var(--gray-200);
	border-radius: 16px;
	padding: 1.5rem;
	box-shadow: 0 4px 20px rgba(0,0,0,0.08);
	transition: all 0.3s ease;
}

.benchmark-card:hover {
	transform: translateY(-2px);
	box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.benchmark-header {
	text-align: center;
	margin-bottom: 1.5rem;
}

.benchmark-header h4 {
	color: var(--primary);
	font-size: 1.125rem;
	margin-bottom: 0.75rem;
	font-weight: 600;
}

.benchmark-winner {
	background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
	color: var(--white);
	padding: 0.5rem 1.25rem;
	border-radius: 25px;
	font-size: 0.8rem;
	font-weight: 600;
	display: inline-block;
	box-shadow: 0 2px 8px rgba(16,185,129,0.3);
	width: 100%;
}

.benchmark-comparison {
	display: flex;
	flex-direction: column;
	gap: 1rem;
}

.benchmark-bar {
	display: flex;
	align-items: center;
	gap: 0;
	margin-bottom: 0.75rem;
}

.bar-label {
	min-width: 146px;
	font-size: 0.875rem;
	font-weight: 600;
	color: var(--text-primary);
}

.bar-container {
	flex: 1;
	height: 32px;
	background: var(--gray-100);
	border-radius: 16px;
	position: relative;
	overflow: hidden;
	border: 1px solid var(--gray-200);
}

.bar-fill {
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	padding: 0 0.75rem;
	color: var(--white);
	font-weight: 600;
	font-size: 0.8rem;
	transition: width 2s cubic-bezier(0.4,0,0.2,1);
	border-radius: 16px;
	box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
	position: relative;
}

.bar-fill::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 50%;
	background: linear-gradient(to bottom, rgba(255,255,255,0.2), transparent);
	border-radius: 16px 16px 0 0;
}

.guarantee-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(300px,1fr));
	gap: 1.5rem;
}

.guarantee-card {
	background: var(--white);
	border: 1px solid var(--gray-200);
	border-radius: 12px;
	padding: 1.5rem;
	text-align: center;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.guarantee-card::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 4px;
	background: var(--success);
}

.guarantee-card:hover {
	transform: translateY(-4px);
	box-shadow: 0 20px 25px -5px rgba(0,0,0,0.1);
	border-color: var(--success);
}

.guarantee-icon {
	width: 64px;
	height: 64px;
	background: rgba(16,185,129,0.1);
	border-radius: 16px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 1rem;
}

.guarantee-icon i {
	color: var(--success);
	font-size: 1.5rem;
}

.guarantee-title {
	font-size: 1.25rem;
	font-weight: 600;
	color: var(--primary);
	margin-bottom: 1rem;
}

.guarantee-description {
	color: var(--text-secondary);
	line-height: 1.6;
	margin-bottom: 1rem;
}

.guarantee-badge {
	background: var(--success);
	color: var(--white);
	padding: 0.5rem 1rem;
	border-radius: 20px;
	font-size: 0.8rem;
	font-weight: 600;
	display: inline-block;
}

.emergency-services::before {
	content: "";
	position: absolute;
	inset: 0;
	background: linear-gradient( 45deg, transparent, rgba(255,255,255,0.1), transparent );
	animation: shimmer 2s infinite;
}

.emergency-icon {
	display: flex;
	justify-content: center;
	margin-bottom: 1rem;
	position: relative;
	z-index: 1;
}

.emergency-icon svg {
	width: 3.5rem;
	height: 3.5rem;
	color: #fbbf24;
	filter: drop-shadow(0 0 15px rgba(251,191,36,0.5));
	animation: pulse 2s infinite;
}

.emergency-title {
	font-size: 2rem;
	font-weight: 800;
	margin-bottom: 1rem;
	position: relative;
	z-index: 1;
}

.emergency-subtitle {
	color: #fecaca;
	margin-bottom: 2rem;
	font-size: 1.1rem;
	position: relative;
	z-index: 1;
}

.emergency-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(250px,1fr));
	gap: 1rem;
	margin-bottom: 2rem;
	position: relative;
	z-index: 1;
}

.emergency-item {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	font-size: 0.875rem;
	color: #fecaca;
	background: rgba(255,255,255,0.1);
	padding: 0.75rem;
	border-radius: 0.5rem;
	backdrop-filter: blur(5px);
}

.emergency-item svg {
	width: 1rem;
	height: 1rem;
	color: #fbbf24;
	flex-shrink: 0;
}

.emergency-alert {
	background: linear-gradient(135deg,#fef2f2,#fee2e2);
	border: 2px solid #fecaca;
	border-radius: 1rem;
	padding: 2rem;
	text-align: center;
	margin-bottom: 1rem;
	box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.alert-icon {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 1rem;
	margin-bottom: 1rem;
}

.alert-icon svg {
	width: 2.5rem;
	height: 2.5rem;
	color: #dc2626;
	animation: bounce 2s infinite;
}

.alert-icon h3 {
	font-size: 1.5rem;
	font-weight: 800;
	color: #b91c1c;
}

.emergency-alert p {
	color: #b91c1c;
	margin-bottom: 1.5rem;
	font-size: 1.1rem;
}

.alert-buttons {
	display: flex;
	justify-content: center;
	gap: 1rem;
	flex-wrap: wrap;
}

.alert-buttons svg {
	width: 88px;
}

.alert-buttons.btn {
	padding: 0.875rem 0.75rem;
}

.emergency-services {
	background: linear-gradient(135deg,#dc2626,#b91c1c);
	color: white;
	padding: 3rem;
	border-radius: 1.5rem;
	text-align: center;
	margin-bottom: 3rem;
	box-shadow: 0 20px 40px rgba(220,38,38,0.3);
	position: relative;
	overflow: hidden;
}

.emergency-cta {
	display: flex;
	justify-content: center;
	gap: 1rem;
	flex-wrap: wrap;
	position: relative;
	z-index: 1;
}

.btn {
	will-change: transform;
}

.btn-outline-white {
	background: transparent;
	color: white;
	border-color: white;
}

.btn {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	padding: 0.875rem 1.75rem;
	border-radius: 0.5rem;
	font-weight: 600;
	font-size: 1rem;
	text-decoration: none;
	border: 2px solid #ffffffb8;
	cursor: pointer;
	transition: all 0.2s ease;
	gap: 0.5rem;
}

.btn-outline-white:hover {
	background: white;
	color: #dc2626;
}

.btn-emergency {
	background: #dc2626;
	color: white;
	border-color: #dc2626;
}

.footer {
	background: linear-gradient(135deg,#111827,#1f2937);
	color: white;
	padding: 3rem 0 1.5rem;
	width: 100vw;
}

.footer-content {
	display: grid;
	grid-template-columns: repeat(4,1fr);
	gap: 2rem;
	margin-bottom: 3rem;
}

.footer-title {
	font-size: 1.75rem;
	font-weight: 800;
	margin-bottom: 1rem;
	background: linear-gradient(45deg,#3b82f6,#06b6d4);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.footer-subtitle {
	font-size: 1.125rem;
	font-weight: 700;
	margin-bottom: 1rem;
	color: #f3f4f6;
}

.footer-description {
	color: #9ca3af;
	line-height: 1.7;
	margin-bottom: 1.5rem;
	max-width: 400px;
}

.social-links {
	display: flex;
	gap: 1rem;
}

.social-links a {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 2.5rem;
	height: 2.5rem;
	background: linear-gradient(135deg,#374151,#4b5563);
	border-radius: 0.5rem;
	color: #9ca3af;
	transition: all 0.2s ease;
}

.social-links a:hover {
	background: linear-gradient(135deg,#3b82f6,#06b6d4);
	color: white;
	transform: translateY(-2px);
}

.social-links svg {
	width: 1.25rem;
	height: 1.25rem;
}

.footer-links {
	list-style: none;
}

.footer-links li {
	margin-bottom: 0.75rem;
}

.footer-links a {
	color: #9ca3af;
	text-decoration: none;
	transition: color 0.2s ease;
}

.footer-links a:hover {
	color: white;
}

.footer-contact .contact-detail {
	display: flex;
	align-items: center;
	gap: 0.75rem;
	margin-bottom: 1rem;
	color: #9ca3af;
}

.footer-contact svg {
	width: 1rem;
	height: 1rem;
	flex-shrink: 0;
}

.footer-bottom {
	border-top: 1px solid #374151;
	padding-top: 1rem;
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: wrap;
	gap: 1rem;
}

.footer-bottom p {
	color: #9ca3af;
}

.footer-legal {
	display: flex;
	gap: 2rem;
}

.footer-legal a {
	color: #9ca3af;
	text-decoration: none;
	transition: color 0.2s ease;
}

.footer-legal a:hover {
	color: white;
}

.scale-up {
	opacity: 0;
	transform: scale(0.95);
	transition: all 0.6s cubic-bezier(0.4,0,0.2,1);
}

.box-chat-luong div {
	background: linear-gradient(135deg, var(--success), #059669);
	color: white;
	padding: 2.5rem;
	border-radius: 16px;
}

.box-chat-luong h3 {
	font-size: 1.5rem;
}

.so-lieu-test {
	text-align: center;
	margin-top: 3rem;
}


/* Back to Top Button */
.back-to-top {
	position: fixed;
	bottom: 30px;
	right: 30px;
	z-index: 1000;
	opacity: 0;
	visibility: hidden;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	cursor: pointer;
	transform: translateY(20px);
}

.back-to-top.visible {
	opacity: 1;
	visibility: visible;
	transform: translateY(0);
}

.progress-circle {
	position: relative;
	width: 50px;
	height: 50px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 50%;
	box-shadow: var(--shadow-lg);
	transition: all 0.3s ease;
	backdrop-filter: blur(10px);
	border: 1px solid rgba(102, 126, 234, 0.1);
}

.progress-circle:hover {
	transform: scale(1.1);
	box-shadow: var(--shadow-xl);
	background: rgba(255, 255, 255, 1);
}

.progress-ring {
	position: absolute;
	top: 0;
	left: 0;
	transform: rotate(-90deg);
	transition: all 0.3s ease;
}

.progress-ring-circle {
	stroke-dasharray: 125.66; /* 2 * π * r = 2 * π * 20 */
	stroke-dashoffset: 125.66;
	transition: stroke-dashoffset 0.1s linear;
	stroke-linecap: round;
}

.progress-circle i {
	color: var(--accent);
	font-size: 18px;
	z-index: 1;
	transition: all 0.3s ease;
}

.progress-circle:hover i {
	color: var(--accent-hover);
	transform: translateY(-1px);
}



@keyframes textclip {
	0% {
		background-position: 0% center;
	}

	100% {
		background-position: 200% center;
	}
}

@supports not (-webkit-background-clip: text) {
	.logo-title a {
		color: #0076b6;
		background: none;
	}
}

@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(30px);
	}

	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@media (hover: none) and (pointer: coarse) {
	.mobile-toggle:hover {
		transform: none;
	}

	.mobile-toggle:active {
		transform: scale(0.95);
		transition-duration: 0.1s;
	}

	.solution-card:hover, .problem-card:hover, .pricing-card:hover {
		transform: none;
		box-shadow: none;
	}
}

@keyframes scaleIn {
	from {
		opacity: 0;
		transform: scale(0.9);
	}

	to {
		opacity: 1;
		transform: scale(1);
	}
}

@keyframes slideInLeft {
	from {
		opacity: 0;
		transform: translateX(-30px);
	}

	to {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes slideInRight {
	from {
		opacity: 0;
		transform: translateX(30px);
	}

	to {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes float {
	0%, 100% {
		transform: translate(0,0) rotate(0deg);
	}

	33% {
		transform: translate(30px,-30px) rotate(2deg);
	}

	66% {
		transform: translate(-20px,20px) rotate(-2deg);
	}
}

@keyframes drift {
	0% {
		background-position: 0 0, 40px 40px;
	}

	100% {
		background-position: 100px 100px, 140px 140px;
	}
}

@keyframes pulse {
	0%, 100% {
		transform: scale(1);
	}

	50% {
		transform: scale(1.1);
	}
}

@keyframes expand {
	0% {
		width: 0;
	}

	100% {
		width: 100%;
	}
}

@keyframes pulse-bg {
	0%, 100% {
		opacity: 1;
	}

	50% {
		opacity: 0.8;
	}
}

@keyframes pulse-glow {
	0%, 100% {
		box-shadow: 0 0 5px rgba(245,158,11,0.3);
	}

	50% {
		box-shadow: 0 0 20px rgba(245,158,11,0.6);
	}
}

@keyframes glow {
	0%, 100% {
		box-shadow: 0 0 10px rgba(220,38,38,0.5);
	}

	50% {
		box-shadow: 0 0 20px rgba(220,38,38,0.8);
	}
}

@keyframes bounce-number {
	0%, 100% {
		transform: scale(1);
	}

	50% {
		transform: scale(1.05);
	}
}

@keyframes cta-pulse {
	0%, 100% {
		transform: scale(1);
	}

	50% {
		transform: scale(1.02);
	}
}

@keyframes shine {
	0% {
		left: -100%;
	}

	100% {
		left: 100%;
	}
}

@keyframes countDown {
	0% {
		transform: scale(1);
	}

	100% {
		transform: scale(1.1);
	}
}

@keyframes progressPulse {
	0%, 100% {
		opacity: 1;
	}

	50% {
		opacity: 0.7;
	}
}

@keyframes float {
	0%, 100% {
		transform: translateY(0) rotate(0deg);
	}

	50% {
		transform: translateY(-20px) rotate(5deg);
	}
}

@keyframes badge-glow {
	0%, 100% {
		box-shadow: 0 0 10px rgba(220,38,38,0.5);
	}

	50% {
		box-shadow: 0 0 20px rgba(220,38,38,0.8);
	}
}

@keyframes cta-pulse {
	0%, 100% {
		transform: scale(1);
	}

	50% {
		transform: scale(1.05);
	}
}

@keyframes slideInFromBottom {
	0% {
		opacity: 0;
		transform: translateY(50px);
	}

	100% {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes shimmer {
	0% {
		transform: translateX(-100%);
	}

	100% {
		transform: translateX(100%);
	}
}

@keyframes pulse {
	0%, 100% {
		transform: scale(1);
	}

	50% {
		transform: scale(1.05);
	}
}

@keyframes bounce {
	0%, 20%, 53%, 80%, 100% {
		transform: translateY(0);
	}

	40%, 43% {
		transform: translateY(-10px);
	}

	70% {
		transform: translateY(-5px);
	}

	90% {
		transform: translateY(-2px);
	}
}


@media (max-width: 1024px) {
	.pricing-grid {
		grid-template-columns: repeat(2,1fr);
		gap: 1.5rem;
	}

	.pricing-card.popular {
		transform: none;
		grid-column: 1 / -1;
		max-width: 400px;
		margin: 0 auto;
	}

	.section-title {
		font-size: 1.5rem;
	}
}

@media (max-width: 768px) {
	p{
		font-size: 0.8rem;
	}
	.problem-title {
		font-size: 1.1rem;
		line-height: 1.5rem;
	}
	.hero-subtitle {
		font-size: 1rem;
	}

	.tech-grid {
		display: grid;
		grid-template-columns: repeat(2,1fr);
		gap: 0.5rem;
	}

	.box-dam-bao > div {
		padding: 2rem 1rem !important;
	}

	.pricing-wrapper {
		position: relative;
		margin-bottom: 2rem;
		padding-top: 0.5rem;
	}

	.box-dam-bao > div {
		padding: 1rem;
	}

	.nav-link::after {
		display: none;
	}

	.trust-indicators-integrated {
		flex-direction: column;
		gap: 1rem;
		text-align: center;
	}

	.trust-badges {
		justify-content: center;
		flex-wrap: wrap;
		gap: 1rem;
	}

	.social-proof-inline {
		justify-content: center;
	}

	.scarcity-content {
		flex-direction: column;
		gap: 1rem;
		text-align: center;
		padding: 10px 0;
	}

	.scarcity-text {
		margin: 0;
	}

	.scarcity-main {
		font-size: 1rem;
	}

	.scarcity-stats {
		flex-direction: row;
		gap: 1rem;
	}

	.main-offer h3 {
		font-size: 1.25rem;
	}

	.btn-scarcity {
		padding: 0.875rem 1.5rem;
		font-size: 1rem;
	}

	.hero h1 {
		font-size: 2.5rem;
	}

	.hero-cta {
		flex-direction: column;
		align-items: center;
	}

	.btn-primary, .btn-secondary {
		justify-content: center;
		font-size: 16px;
	}

	.social-proof {
		flex-direction: column;
	}

	.scarcity-icon {
		display: none;
	}

	.container {
		padding: 0 1.5rem;
	}

	.header-inner {
		height: 60px;
	}

	.nav-links {
		position: fixed;
		top: 60px;
		left: 0;
		right: 0;
		background: var(--white);
		flex-direction: column;
		padding: 1rem;
		box-shadow: var(--shadow-lg);
		transform: translateY(-150%);
		visibility: hidden;
		transition: transform 0.15s ease-out, visibility 0.15s ease-out;
		z-index: 1001;
		will-change: transform, visibility;
		contain: layout style paint;
		backface-visibility: hidden;
		perspective: 1000px;
	}

	.nav-links.active {
		transform: translateY(0);
		visibility: visible;
		display: flex;
		gap: 8px;
	}

	.mobile-toggle {
		display: flex !important;
		align-items: center;
		justify-content: center;
	}

	.section-title {
		font-size: 2rem;
	}

	.pricing-grid {
		grid-template-columns: repeat(3,1fr);
		gap: 0.5rem;
		padding: 0;
		padding-top: 10px;
	}

	.pricing-wrapper {
		overflow-x: scroll;
	}

	.pricing-card {
		padding: 0;
		width: 74vw;
	}

	.pricing-card.popular {
		transform: none;
		grid-column: auto;
	}

	.card-content {
		padding: 1.5rem;
	}

	.specs-list {
		grid-template-columns: 1fr;
	}

	.price-current {
		font-size: 2rem;
	}

	.comparison-header, .comparison-row {
		grid-template-columns: 1fr;
		gap: 0;
	}

	.comparison-header > div, .comparison-row > div {
		padding: 1rem 1.5rem;
		text-align: left;
		justify-content: flex-start;
		border-bottom: 1px solid var(--gray-200);
	}

	.comparison-header .comparison-feature, .comparison-row .comparison-feature {
		background: var(--gray-50);
		font-weight: 600;
		border-bottom: 2px solid rgba(16,185,129,0.1);
		color: var(--primary);
	}

	.comparison-row .comparison-standard {
		background: rgba(239,68,68,0.05);
		border-left: 4px solid var(--danger);
		margin: 0;
		padding-left: 1.5rem;
	}

	.comparison-row .comparison-vutruso {
		background: rgba(16,185,129,0.1);
		border-left: 4px solid var(--success);
		margin: 0;
		padding-left: 1.5rem;
		font-weight: 600;
	}

	.comparison-row:last-child .comparison-vutruso {
		border-bottom: none;
	}

	.testimonials-grid {
		grid-template-columns: 1fr;
	}

	.process-timeline::before {
		display: none;
	}

	.process-step {
		flex-direction: row;
		text-align: center;
	}

	.step-content {
		margin-left: 0;
		margin-top: 0;
		padding-top: 5px;
		text-align: left;
	}

	.security-stats {
		grid-template-columns: 1fr;
	}

	.security-threats {
		grid-template-columns: 1fr;
	}

	.benchmark-results {
		grid-template-columns: 1fr;
	}

	.benchmark-bar {
		flex-direction: column;
		align-items: stretch;
	}

	.bar-label {
		min-width: auto;
		margin-bottom: 0.5rem;
	}

	.guarantee-grid {
		display: grid;
		grid-template-columns: repeat(2,1fr);
		gap: 0.5rem;
	}

	.footer-content {
		grid-template-columns: 1fr;
		gap: 2rem;
	}

	.footer-bottom {
		flex-direction: column;
		text-align: center;
		gap: 1rem;
	}

	.footer-legal {
		justify-content: center;
	}

	.header {
		min-height: 60px;
		box-sizing: border-box;
	}

	.header.scrolled {
		min-height: 60px;
		transform: translateZ(0);
	}

	.nav-link.active:before {
		display: none;
	}

	.btn-white {
		padding: 1rem;
	}

	.alert-buttons svg {
		width: 30px;
	}

	.btn {
		padding: 0.875rem 1rem;
	}

	.faq-question {
		padding: 1rem;
		font-size: 1rem;
	}

	.faq-answer {
		padding: 0 10px;
	}

	.step-number {
		width: 35px;
		height: 35px;
		font-size: 1rem;
	}

	.plan-description, .card-header {
		margin-bottom: 0.5rem;
	}

	.main-cta {
		margin-top: 2rem;
	}

	.guarantee-title {
		font-size: 1rem;
	}

	.guarantee-card {
		padding: 1rem 0.5rem;
	}

	.problems-grid, .solutions-grid {
		display: grid;
		grid-template-columns: repeat(2,1fr);
		gap: 0.5rem;
	}

	.solution-title {
		font-size: 1rem;
	}

	.process-step {
		display: flex;
		align-items: center;
		margin-bottom: 1rem;
		position: relative;
		gap: 7px;
	}
}

@media (max-width: 650px) {

	.contact-cta>div {
		display: flex;
		gap: .5rem;
		justify-content: center;
		flex-wrap: nowrap;
		margin-top: 1.5rem;
		flex-direction: row;
	}

	.contact-grid {
		grid-template-columns: repeat(2, 1fr);
		gap: 0.5rem;
		margin-top: 2rem;
	}

	.contact-grid .phone {
		display: none;
	}

	.trust-item span {
		font-size: 0.8rem;
	}

	.stats-grid {
		grid-template-columns: repeat(2,1fr);
		gap: 1rem;
	}

	.trust-indicators {
		flex-wrap: wrap;
		flex-direction: row;
		display: grid;
		grid-template-columns: repeat(2,1fr);
		gap: 0.5rem;
		margin: 1rem 0;
	}

	.hero-cta .btn-primary, .hero-cta .btn-secondary {
		width: 100%;
		max-width: 100%;
	}

	.hero-cta {
		display: grid;
		grid-template-columns: 1fr;
		gap: 0.5rem;
		margin: 1rem 0;
	}

	.problems-grid, .solutions-grid {
		display: grid;
		grid-template-columns: repeat(2,1fr);
		gap: 0.5rem;
	}
}

@media (max-width: 480px) {
	.btn-scarcity {
		gap: 0.3rem;
	}

	.main-offer p {
		font-size: 1rem;
	}

	.trust-badges {
		grid-template-columns: repeat(3,1fr);
		display: grid;
		gap: 0.5rem;
	}

	.trust-badge {
		flex-direction: column;
		text-align: center;
		gap: 0.25rem;
	}

	body {
		padding: 0;
	}

	.section-title {
		font-size: 1.4rem;
	}

	.popular-badge {
		font-size: 0.75rem;
		padding: 0.4rem 1rem;
	}

	.container {
		padding: 0 1rem;
	}

	.hero {
		padding: 60px 0 40px;
	}

	.hero h1 {
		font-size: 1.75rem;
	}

	.section {
		padding: 1.5rem 0;
	}

	.hero-card, .problem-card, .solution-card {
		padding: .6rem;
	}

	.alert-buttons {
		display: flex;
		justify-content: center;
		gap: 1rem;
		flex-wrap: nowrap;
		flex-direction: column;
	}

	.scarcity-bar-container {
		display: none;
	}

	.nav {
		gap: 1rem;
	}

	.logo {
		font-size: 1rem;
	}

	.header-cta {
		padding: 0.5rem 0.75rem;
		font-size: 0.8rem;
	}

	.logo-title a span {
		font-size: 30px;
	}

	.logo-title i {
		display: none;
	}

	.alert-icon h3 {
		font-size: 1rem;
	}

	.back-to-top {
		bottom: 20px;
		right: 20px;
	}
	
}
