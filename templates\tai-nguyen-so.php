<?php 
session_start();
/*
Template Name: T<PERSON><PERSON>
*/

get_header(); ?>

<?php 
$arrs = array(
    "<EMAIL>",
    "vutruso.net"
);

// echo "<pre>";
// var_dump($arrs);
// echo "</pre>";

$error = "";
$good = "";

if(isset($_POST['ok'])){
    $email = $_POST['email'];
    foreach ($arrs as $k=>$v){

        if( $email != $v){
            $error = "Dữ liệu không tồn tại";
        }else{
            $good = $v;
            $_SESSION['name'] = $good;
        }
    
    }
    
}




?>

<?php  if(empty($_SESSION['name'])) { ?>

  <div class="login-1">
    <div class="container-fluid">
    
      <div class="row login-box">
      
        <div class="col-lg-6 align-self-center pad-0 form-section">

          <div class="form-inner">
          <h1 style="text-transform: uppercase;font-size: 27px;margin-bottom: 6%;font-family: 'Baloo Chettan 2'!important;">Tài nguyên số</h1>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
              
              Nhập địa chỉ email hoặc tên miền mà bạn đã làm việc với <strong>Vũ Trụ Số</strong> để xem tài nguyên.
              <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>

            <?php if($error!=Null){ ?>
              <div class="alert alert-danger alert-dismissible fade show" role="alert">
                  <strong>
              <?php echo $error; ?>
                  </strong>
                  <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php } ?>


            <form class="login-form" action="#" method="POST">

              <div class="form-group position-relative clearfix">
                <input name="name" type="text" class="form-control" placeholder="Họ tên cá nhân hoặc tên công ty (Không bắt buộc)">
              </div>
              <div class="form-group position-relative clearfix">
                <input name="email" type="text" class="form-control" placeholder="Địa chỉ email hoặc tên miền (Bắt buộc)" >

              </div>

              <div class="form-group clearfix">
                <button name="ok" type="submit" class="btn btn-primary btn-lg btn-theme">Xem tài nguyên</button>
              </div>

            </form>
            <div class="clearfix"></div>

          </div>
        </div>
        <div class="col-lg-6 bg-color-15 align-self-center pad-0 none-992 bg-img">
          <div class="photo">
            <img src="https://vutruso.com/wp-content/uploads/2021/11/vu-tru-so.webp" alt="logo" class="w-100 img-fluid">
          </div>
        </div>
      </div>
    </div>
  </div>

  <?php } else { ?>


    <div id="content" style="margin: 0 auto; overflow: hidden;margin-top: 1%;padding: 5px 10px;">
    <div class="container-fluid">
        <div class="row justify-content-center">

            <div class="main-content main-single-post col-lg-10 full-width">

                <h1 class="title title-0 title-1">
                    <i class="fa fa-plug" aria-hidden="true" style=" background: #d5e3ec; font-size: 33px; "></i>
                    <span itemprop="name"><?php the_title(); ?></span>
                </h1>

                <div class="vts-single-content">
                    <?php if(have_posts()) : while(have_posts()) : the_post() ?>						
                    <?php the_content(); ?>
                    <?php endwhile; ?>
                    <?php endif; ?>
                </div>
                    

            </div/><!-- col-lg-10 justify-content-center-->
        </div><!-- justify-content-center-->
        
    </div>
</div>

<?php } ?>




<style>
*,::after,::before{box-sizing:border-box;}
body{margin:0;font-family:var(--bs-font-sans-serif);font-size:1rem;font-weight:400;line-height:1.5;background-color:#fff;-webkit-text-size-adjust:100%;-webkit-tap-highlight-color:transparent;font-family:'Open Sans', sans-serif;color:#535353;}

strong{font-weight:bolder;}
a{color:#0d6efd;}
a:hover{color:#0a58ca;}
a:not([href]):not([class]),a:not([href]):not([class]):hover{color:inherit;text-decoration:none;}
img{vertical-align:middle;}
button{border-radius:0;}
button:focus:not(:focus-visible){outline:0;}
button,input,textarea{margin:0;font-family:inherit;font-size:inherit;line-height:inherit;}
button{text-transform:none;}
[type=button],[type=submit],button{-webkit-appearance:button;}
::-moz-focus-inner{padding:0;border-style:none;}
textarea{resize:vertical;}
.img-fluid{max-width:100%!important;height:auto;}
.container-fluid{width:100%;padding-right:var(--bs-gutter-x,.75rem);padding-left:var(--bs-gutter-x,.75rem);margin-right:auto;margin-left:auto;}
.form-control{display:block;width:100%;padding:.375rem .75rem;font-size:1rem;font-weight:400;line-height:1.5;color:#212529;background-color:#fff;background-clip:padding-box;border:1px solid #ced4da;-webkit-appearance:none;-moz-appearance:none;appearance:none;border-radius:.25rem;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out;}
.form-control:focus{color:#212529;background-color:#fff;border-color:#86b7fe;outline:0;box-shadow:none;}
.form-control::-moz-placeholder{color:#6c757d;opacity:1;}
.form-control::placeholder{color:#6c757d;opacity:1;}
.form-control:disabled{background-color:#e9ecef;opacity:1;}
.btn{display:inline-block;font-weight:400;line-height:1.5;color:#212529;text-align:center;text-decoration:none;vertical-align:middle;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;user-select:none;background-color:transparent;border:1px solid transparent;padding:.375rem .75rem;font-size:1rem;border-radius:.25rem;transition:color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;}
.btn:hover{color:#212529;}
.btn:focus{outline:0;box-shadow:none;}
.btn:disabled{pointer-events:none;opacity:.65;}
.btn-primary{color:#fff;background-color:#0d6efd;border-color:#0d6efd;}
.btn-primary:hover{color:#fff;background-color:#0b5ed7;border-color:#0a58ca;}
.btn-primary:focus{color:#fff;background-color:#0b5ed7;border-color:#0a58ca;box-shadow:0 0 0 .25rem rgba(49,132,253,.5);}
.btn-primary:active{color:#fff;background-color:#0a58ca;border-color:#0a53be;}
.btn-primary:active:focus{box-shadow:0 0 0 .25rem rgba(49,132,253,.5);}
.btn-primary:disabled{color:#fff;background-color:#0d6efd;border-color:#0d6efd;}
.btn-lg{padding:.5rem 1rem;font-size:1.25rem;border-radius:.3rem;}
.fade{transition:opacity .15s linear;}
.alert{position:relative;padding:1rem 1rem;margin-bottom:1rem;border:1px solid transparent;border-radius:.25rem;}
.alert-dismissible{padding-right:3rem;}
.alert-dismissible .btn-close{position:absolute;top:0;right:0;z-index:2;padding:1.25rem 1rem;}
.alert-success{color:#0f5132;background-color:#d1e7dd;border-color:#badbcc;}
.btn-close{box-sizing:content-box;width:1em;height:1em;padding:.25em .25em;color:#000;border:0;border-radius:.25rem;opacity:.5; background: transparent;}
.btn-close:hover{color:#000;text-decoration:none;opacity:.75;}
.btn-close:focus{outline:0;box-shadow:0 0 0 .25rem rgba(13,110,253,.25);opacity:1;}
.btn-close:disabled{pointer-events:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;opacity:.25;}
.clearfix::after{display:block;clear:both;content:"";}
.float-start{float:left!important;}
.position-relative{position:relative!important;}
.w-100{width:100%!important;}
.align-self-center{align-self:center!important;}

.login-box .alert{padding:10.5px 20px;font-size:13px;border:none!important;border-radius:3px;}
.login-box .alert-dismissible .btn-close{padding:13.5px 14px;background-size:8px;}
.login-box .alert-dismissible .btn-close:focus,.login-box .alert-dismissible .btn-close:active{box-shadow:none;}

.login-1{background:#f6f6f6;}
.login-1 .photo{padding:0px 15px;max-width:700px;margin:auto;}
.login-1 .form-section{min-height:100vh;position:relative;text-align:center;display:-webkit-box;display:-moz-box;display:-ms-flexbox;display:-webkit-flex;display:flex;justify-content:center;align-items:center;padding:15px 0;background:#f6f6f6;}

.login-1 a{text-decoration:none;}
.login-1 .form-inner{max-width:500px;width:100%;padding:0 15px;text-align:center;}
.login-1 .bg-img{top:0;bottom:0;min-height:100vh;text-align:left;opacity:1;position:relative;display:flex;justify-content:center;align-items:center;padding:30px 50px;background:#fff;}

.login-1 .form-section p{color:#535353;margin-bottom:0;text-align:center;font-size:15px;}
.login-1 .form-section p a{font-weight:500;color:#535353;}
.login-1 .form-section .form-group{margin-bottom:25px;width:100%;position:relative;}
.login-1 .form-section .form-control{padding:11px 20px 9px;font-size:16px;outline:none;height:50px;color:#616161;border-radius:3px;font-weight:500;border:1px solid transparent;background:#fff;}

.login-1 .btn-theme{position:relative;display:inline-block;border:none;outline:none!important;color:#ffffff;text-transform:capitalize;transition:all 0.3s linear;z-index:1;overflow:hidden;cursor:pointer;font-size:17px;font-weight:400;font-family:'Jost', sans-serif;border-radius:3px;width:100%;}
.login-1 .btn-theme:after{position:absolute;top:0;left:0;bottom:0;right:0;content:"";border-radius:3px;transform:perspective(200px) scaleX(0.1) rotateX(90deg) translateZ(-10px);transform-origin:bottom center;transition:transform 0.4s linear, transform 0.4s linear;z-index:-1;}
.login-1 .btn-theme:hover:after{transform:perspective(200px) scaleX(1.05) rotateX(0deg) translateZ(0);transition:transform 0.4s linear, transform 0.4s linear;}
.login-1 .btn-lg{padding:0 50px;line-height:50px;}
.login-1 .btn{box-shadow:none!important;}
.login-1 .btn-primary{background:#2ad4bc;border-color:#51d4bc;}
.login-1 .btn-primary:after{background:#1abfa8;}
.btn-theme:focus{box-shadow:none;}  

@media (max-width: 992px){
  .login-1 .bg-img{min-height:100%;border-radius:5px;}
  .login-1{background:#fff2f2;}
  .none-992{display:none!important;}
  .login-1 .login-box{max-width:500px;margin:0 auto;padding:0;}
  .login-1 .form-section{padding:50px 0;width:100%;}
}

@media (max-width: 768px){
  .login-1 .form-inner{padding:0;}
}

@media (prefers-reduced-motion:reduce){
  .btn{transition:none;}
  .fade{transition:none;}
  .form-control{transition:none;}
}

@media (min-width:992px){
  .col-lg-6{flex:0 0 auto;width:50%;}
}


.alert-danger {
    color: #842029;
    background-color: #f8d7da;
    border-color: #f5c2c7;
}


</style>


<?php get_footer(); ?>
<section class="cookie-bar" style="display: block;">
    <div class="cookie-notice container">
        <p class="cookie-para">Trang web của chúng tôi cũng có thể bao gồm cookie của các bên thứ ba như Google Analytics. Bằng cách sử dụng trang web này, bạn đồng ý với việc sử dụng cookie. <br>Chúng tôi đã cập nhật Chính sách quyền riêng tư, vui lòng nhấp vào nút phía dưới để kiểm tra Chính sách quyền riêng tư của chúng tôi.</p>
        <a href="javascript:;" class="cookie-btn">Tôi đồng ý</a>
        <a href="https://vutruso.com/chinh-sach-bao-mat/" class="cookie-btn secondary">Chính sách bảo mật</a>
    </div>
</section>

<script>window.addEventListener('DOMContentLoaded', function() {
jQuery(document).ready(function($) {
    // Function to read a cookie
    function readCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) == ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }

    // Function to create a cookie
    function createCookie(name, value, days) {
        var expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + value + expires + "; path=/";
    }

    // Show or hide the cookie bar based on the cookie
    if (readCookie("cookie_accepted") == "1") {
        $(".cookie-bar").hide();
    } else {
        $(".cookie-bar").show();
        $('body').addClass('cookie-space');
    }

    // Handle the cookie bar button click
    $('.cookie-btn').click(function() {
        $('body').removeClass('cookie-space');
        $('.cookie-bar').fadeOut();
        createCookie("cookie_accepted", 1, 365);
    });

    // Additional functions (optional)
    function getParameterByName(name, url) {
        if (!url) url = window.location.href;
        name = name.replace(/[\[\]]/g, "\\$&");
        var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
            results = regex.exec(url);
        if (!results) return null;
        if (!results[2]) return '';
        return decodeURIComponent(results[2].replace(/\+/g, " "));
    }

    jQuery(document).ready(function($) {
        var advMedium = getParameterByName('advm');
        if (advMedium != null) {
            $('input[name=advm]').val(advMedium);
            createCookie('advm', advMedium, 1);
        } else {
            advMedium = readCookie('advm');
            $('input[name=advm]').val(advMedium);
        }
        var nodeCount = document.getElementsByName('ft').length;
        for (var count = 0; count < nodeCount; count++) {
            document.getElementsByName('ft')[count].value = window.location.href;
        }
    });
});
});</script>

