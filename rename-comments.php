<?php
	if ( post_password_required() ) { return; }
?>
<div id="comment">
	<div class="title">
		<i class="fa fa-pencil-square"></i>
		<span><?php echo __('Viết nhận xét', 'vutruso'); ?></span>
	</div>
	<div class="formcontainer">

		<?php if( comments_open() ) { ?>
			<div class="newcomment">
				<?php

					$comment_args = array(
						'comment-notes'        => '',
						'comment_notes_after'  => '', // Xoa "You may use these
					    'comment_notes_before' => '', // Xoa "Your email address will not be published.
					    'title_reply_to'       => __( '<span>Nhập nội dung để trả lời %s</span>', 'vutruso'),
					    'title_reply_before'   => '',
					    'title_reply_after'    => '',
					    'title_reply'		   =>'',
					    //'title_reply'          => __( 'Leave a Reply', 'vutruso'),
					    'cancel_reply_link'    => __( 'H<PERSON>y trả lời', 'vutruso'),
						'fields' 			   => apply_filters( 'comment_form_default_fields', array(
						'author' 			   => '<input type="text" name="author" id="author" class="vts-comment" value="' . esc_attr( $commenter['comment_author'] ) . '" placeholder="'. __('Họ Tên *', 'vutruso').'">',
						
						'email'  			   => '<input type="email" name="email" id="email" value="' . esc_attr(  $commenter['comment_author_email'] ) . '" placeholder="'. __('Địa chỉ email *', 'vutruso').'" size="22">',
						)),
				    	'comment_field' 	   => '<textarea id="comment" name="comment" placeholder="'. __('Nội dung', 'vutruso').'"></textarea>',
				    	'submit_button'        => '<input name="submit" id="submit" type="submit" value="'. __('Đăng nhận xét', 'vutruso').'">',
					);
					comment_form($comment_args); ?>
			</div>
		<?php } else { ?><div class="comment-closed"><?php echo __('Bình luận đã bị khóa','vutruso'); ?></div><?php } ?>

	   	<?php if(have_comments()) : ?>
	      	<div class="VTSInner">
		      	<ol class="comment-list commentlist clearfix">
			        <?php
						wp_list_comments(array(
							'type'				=> 'all',
							'reply_text'  		=> __('Trả lời','vutruso'),
							'avatar_size'		=> 83,
							'callback' 			=> 'vts_comments'
						));
			        ?>
		      	</ol>
				<?php if ( ! comments_open() ) : ?>
					<p class="no-comments"><?php _e( 'Comments are closed.', 'vutruso' ); ?></p>
				<?php endif; ?>

				<?php if ( get_comment_pages_count() > 1 && get_option( 'page_comments' ) ) : ?>
				<div id="comment-nav-below" class="navigation comment-navigation" role="navigation">
					<h3 class="screen-reader-text"><?php _e( 'Comment navigation', 'vutruso' ); ?></h3>
					<div class="comment-page-btn">
						<?php previous_comments_link( __( '<i class="fa fa-angle-double-left" aria-hidden="true"></i> Trang trước đó', 'vutruso' ) ); ?>
						<?php next_comments_link( __( 'Trang kế tiếp <i class="fa fa-angle-double-right" aria-hidden="true"></i>', 'vutruso' ) ); ?>
					</div>
				</div>
				<!-- #comment-nav-below -->
				<?php endif; // Check for comment navigation. ?>

				
	    	</div>
	  	<?php endif; ?>

	</div>
</div>

<style>
.comment-list {
    position: relative;
    margin-top: 50px;
}
.comment-list.on:after{
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255,255,255,.8) url(img/icon/comment-loading.svg) no-repeat center;
}
.comment-awaiting-moderation{background: #4b6c94; padding: 5px 11px; border-radius: 3px; color: #fff;}
.comment-closed{background: #1b66b1; padding: 10px; margin-bottom: 2%; border-radius: 4px; color: #fff;}
#respond.title{display:block;overflow:hidden;line-height:50px;height:50px;background:#f5f5f5;color:#333;border:1px #f0f0f0 solid;}
#respond.title a{float:right;display:block;border-left:1px #e7e7e7 solid;font-size:19px;color:#4EB96E;height:50px;line-height:50px;width:50px;transition:background .3s;text-align:center;}
#respond.title a:hover{background:#e7e7e7;}
#respond.title span{float:left;display:block;line-height:50px;height:50px;overflow:hidden;width:585px;padding:0 15px;font-weight:700;font-size:14.82px;color:#333;}
#respond.title > i{display:block;float:left;height:50px;width:50px;line-height:50px;text-align:center;font-size:19px;color:#333;background:#fff;}
#content .maincontent > *{margin-bottom:10px;}
#comment .title span{text-align:left;width:615px;}
#respond{overflow:hidden;margin-bottom:8px;}
#respond .title a{color:#;}
#respond .title a#cancel-comment-reply-link{color:#999;}
#comment #commentform{padding:2px 0px;}
#comment #commentform textarea{float:left;width:60%;height:131px;border:1px #f1f1f1 solid;background:#fff;resize:none;padding:14px;font-size:14.67px;box-shadow:inset 0px -5px 0px 0px #f4f4f4;line-height: 23px;}
#comment #commentform input{float:left;width:39.555%;margin:0px 0px 2px 2px;padding:0px 6px;border:1px #f1f1f1 solid;font-size:16.67px;box-shadow:inset 0px -5px 0px 0px #f4f4f4;}
#comment #commentform input[type=submit]{background:#0069a3;box-shadow:none;display:block;color:#fff;border:none;font-weight:700;cursor:pointer;transition:background .3s;text-transform: capitalize;    border-bottom: 4px solid #4f8bad;}
#comment #commentform input[type=submit]:hover{background:#0075b5;}
#respond .form-submit{overflow: unset !important;padding-top: 0 !important;float: none !important; }
.comment-body .vts-comment-text{border:none!important;}
.vts-comment-text .commentmetadata{margin-left: 0!important}
.vts-comment-text .comment-edit-link{font-size: 12px}
.commentlist li .date {font-size: 11px;color: #999999;}
.commentlist li .author span {font-weight: bold;font-size: 16px;}
.commentlist li .avatar img { display: block; -webkit-border-radius: 1px; -moz-border-radius: 1px; border-radius: 1px; -webkit-box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(0, 0, 0, 0.1); -moz-box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(0, 0, 0, 0.1); box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(0, 0, 0, 0.1); }
.commentlist li .avatar{position: static !important;float: left;}
.commentlist li .vts-comment-text { position: relative; padding: 10px; margin-left: 99px; border: 1px solid #f0f0f0; border-bottom-color: #e8e8e8; box-shadow: 0 0px 1px rgba(0,0,0,0.25), /* The top layer shadow */ 0 7px 0 -4px #fff, /* The second layer */ 0 7px 1px -3px rgba(0,0,0,0.15), /* The second layer shadow */ 0 14px 0 -8px #fff, /* The third layer */ 0 14px 1px -7px rgba(0,0,0,0.15); }
.vts-comment-text:before { border: 10px solid #f3f3f3; position: absolute; top: 12px; left: -21px; content: ''; border-top-color: transparent; border-left-color: transparent; border-bottom-color: transparent; z-index: 100;    width: 8px;height: 0; }
.says{display: none}
.single .commentlist{padding:0;}
.single .commentlist li{list-style: none !important;}
.comment-list>li{margin-left: 0!important;}
.VTSInner li{/*background: #fff!important;*/border:none!important;    padding: 2px !important;}
.comment-reply-link:before { display: block; float: left; line-height: 20px; margin-right: 3px; content: "\f122"; font-family: FontAweSome; }
.commentlist li ul {list-style: none;margin: 0 0 0 80px!important;}
.vts-comment-text .comment-content{margin:0!important;}
.vts-comment-text .comment-respond small{font-size: smaller;float: left;position: absolute;}
.vts-comment-text .fn{text-transform: capitalize;}
.vts-comment-text .comment-content p{font-size: 14px!important}

@media screen and (max-width: 768px){
	#respond.title span{width:337px;}
	#comment #commentform textarea{width:100%;}
	#comment .title span{width:414px;}
	#comment #commentform input{width:100%;margin:0;}
	#comment #commentform input[type="submit"]{width:100%}
}
@media screen and (max-width: 480px){
	#respond.title a{display:none;}
}
</style>