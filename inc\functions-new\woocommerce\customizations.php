<?php
/**
 * VTS WooCommerce Customizations
 * 
 * WooCommerce customizations and modifications
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Remove the product description heading
 */
function vts_remove_product_description_heading() {
    add_filter('woocommerce_product_description_heading', '__return_false');
}
add_action('init', 'vts_remove_product_description_heading');

/**
 * Set maximum quantity to 1
 */
function vts_woocommerce_max_quantity($args, $product) {
    $args['max_value'] = 1;
    return $args;
}
add_filter('woocommerce_quantity_input_args', 'vts_woocommerce_max_quantity', 10, 2);

/**
 * Change "Add to Cart" button text
 */
function vts_custom_add_to_cart_text() {
    return __('Tải về', 'vutruso');
}
add_filter('woocommerce_product_single_add_to_cart_text', 'vts_custom_add_to_cart_text');

/**
 * Custom price display
 */
function vts_custom_price($price, $product) {
    if ('' == $product->get_price()) {
        if (!is_product()) {
            $price = '<button class="nhan-bao-gia demo">Liên hệ</button>';
        } else {
            $price = '<button id="bao-gia" class="demo">Liên hệ</button>';
        }
    } elseif (0 == $product->get_price()) {
        $price = '<button class="demo">Miễn phí</button>';
    }

    return $price;
}
add_filter('woocommerce_get_price_html', 'vts_custom_price', 100, 2);

/**
 * Change currency symbol to VNĐ
 */
function vts_change_currency_symbol($currency_symbol, $currency) {
    switch ($currency) {
        case 'VND': 
            $currency_symbol = 'VNĐ'; 
            break;
    }
    return $currency_symbol;
}
add_filter('woocommerce_currency_symbol', 'vts_change_currency_symbol', 10, 2);

/**
 * Change number of related products
 */
function vts_change_related_products_count($args) {
    $args['posts_per_page'] = 6;
    return $args;
}
add_filter('woocommerce_output_related_products_args', 'vts_change_related_products_count');

/**
 * Remove featured image duplicate on gallery single product slider
 */
function vts_remove_featured_image($html, $attachment_id) {
    global $post, $product;
    $featured_image = get_post_thumbnail_id($post->ID);
    if ($attachment_id == $featured_image) {
        $html = '';
    }
    return $html;
}
add_filter('woocommerce_single_product_image_thumbnail_html', 'vts_remove_featured_image', 10, 2);

/**
 * Change description tab name
 */
function vts_change_description_tab_name($tabs) {
    $tabs['description']['title'] = __('Thông tin chi tiết', 'vutruso');
    return $tabs;
}
add_filter('woocommerce_product_tabs', 'vts_change_description_tab_name');

/**
 * Free price display in cart
 */
function vts_filter_cart_item_price($price, $cart_item, $cart_item_key) {
    if ($cart_item['data']->price == 0) {
        $price = __('Miễn phí', 'vutruso');
    }
    return $price;
}
add_filter('woocommerce_cart_item_price', 'vts_filter_cart_item_price', 10, 3);

/**
 * Free subtotal display in cart
 */
function vts_filter_cart_item_subtotal($subtotal, $cart_item, $cart_item_key) {
    if ($cart_item['data']->price == 0) {
        $subtotal = __('Miễn phí', 'vutruso');
    }
    return $subtotal;
}
add_filter('woocommerce_cart_item_subtotal', 'vts_filter_cart_item_subtotal', 10, 3);

/**
 * Remove password strength check
 */
function vts_remove_password_strength() {
    wp_dequeue_script('wc-password-strength-meter');
}
add_action('wp_print_scripts', 'vts_remove_password_strength', 10);

/**
 * Disable confirm logout
 */
function vts_disable_wc_logout_confirmation() {
    global $wp;
    if (isset($wp->query_vars['customer-logout'])) {
        wp_redirect(str_replace('&amp;', '&', wp_logout_url(wc_get_page_permalink('myaccount'))));
        exit;
    }
}
add_action('template_redirect', 'vts_disable_wc_logout_confirmation');

/**
 * Remove order comment note
 */
function vts_remove_order_notes() {
    add_filter('woocommerce_enable_order_notes_field', '__return_false', 9999);
}
add_action('init', 'vts_remove_order_notes');

/**
 * Add phone column to orders page
 */
function vts_shop_order_columns($columns) {
    $new_columns = (is_array($columns)) ? $columns : [];
    $new_columns['phone'] = 'Số điện thoại';
    return $new_columns;
}
add_filter('manage_edit-shop_order_columns', 'vts_shop_order_columns');

/**
 * Display phone in orders column
 */
function vts_shop_order_posts_custom_column($column) {
    global $post, $the_order;

    if (empty($the_order) || $the_order->get_id() != $post->ID) {
        $the_order = wc_get_order($post->ID);
    }

    $billing_address = $the_order->get_address();
    if ($column == 'phone') {    
        echo (isset($billing_address['phone']) ? $billing_address['phone'] : '');
    }
}
add_action('manage_shop_order_posts_custom_column', 'vts_shop_order_posts_custom_column');
