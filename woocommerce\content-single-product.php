<?php

/**
 * The template for displaying product content in the single-product.php template
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/content-single-product.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * @package WooCommerce/Templates
 * @version 3.8.0
 */

defined( 'ABSPATH' ) || exit;
global $product;
?>

<div class="container-fluid">
    <div class="container vts-single-product">
        <div class="row">
            <div class="col-md-12 col-lg-12 col-sm-12 pr-3 pl-0">
                <div class="breadcrumb">
                    <?php if(function_exists("seopress_display_breadcrumbs")) { seopress_display_breadcrumbs(); } ?>
                </div>
            </div>

            <div class="col-md-12 col-lg-12 col-sm-12 p-0">
                <h1 itemprop="headline" class="vts-title-single-product" style="padding: 16px 0;"><?php the_title(); ?>
                </h1>
            </div>

            <div class="col-lg-12 col-sm-12 co-md-12">
                <?php 
                    /**
                     * Hook: woocommerce_before_single_product.
                     *
                     * @hooked wc_print_notices - 10
                     */
                    do_action( 'woocommerce_before_single_product' );

                    if ( post_password_required() ) {
                        echo get_the_password_form(); // WPCS: XSS ok.
                        return;
                    }
                ?>
            </div>

        </div><!-- end row -->

    </div>
</div>


<div id="content" class="container-fluid">
    <div class="container vts-single-product">

        <div class="row single-product-main">

            <div class="col-md-6 col-lg-7 col-sm-12 p-0 product-gallery">
                <div class="vts-gallery">
                    <?php
                        /**
                         * Hook: woocommerce_before_single_product_summary.
                         *
                         * @hooked woocommerce_show_product_sale_flash - 10
                         * @hooked woocommerce_show_product_images - 20
                         */
                        //do_action( 'woocommerce_before_single_product_summary' );
                    ?>

                    <a class="my-link" data-gall="gallery" href="<?php the_post_thumbnail_url('full'); ?>"
                        title="<?php the_title(); ?>">
                        <img class="no-lazy" role="presentation" alt="<?php the_title(); ?>"
                            src="<?php the_post_thumbnail_url('full'); ?>">
                    </a>


                </div>

                <?php
                    /**
                     * Hook: woocommerce_after_single_product_summary.
                     *
                     * @hooked woocommerce_output_product_data_tabs - 10
                     * @hooked woocommerce_upsell_display - 15
                     * @hooked woocommerce_output_related_products - 20
                     */
                    do_action( 'woocommerce_after_single_product_summary' );
                    do_action( 'woocommerce_after_single_product' );
                ?>

            </div>

            <div class="col-md-6 col-lg-5 col-sm-12 sidebar-single-product">
                <div class="buy-vts">
                    <div class="infomation">
                        <div class="demo-theme">
                            <?php $demo = get_post_meta($post->ID, 'custom_text_field_demo', true); ?>
                            <?php if($demo): ?>
                            <a rel="noopener noreferrer external nofollow" style="color:#fff;"
                                href="<?php echo $demo; ?>" target="_blank" class="btn btn-success"><i
                                    class="fa fa-paper-plane" aria-hidden="true"></i> Demo</a>
                            <?php endif; ?>
                        </div>




                        <?php
                            /**
                             * Hook: woocommerce_single_product_summary.
                             *
                             * @hooked woocommerce_template_single_title - 5
                             * @hooked woocommerce_template_single_rating - 10
                             * @hooked woocommerce_template_single_price - 10
                             * @hooked woocommerce_template_single_excerpt - 20
                             * @hooked woocommerce_template_single_add_to_cart - 30
                             * @hooked woocommerce_template_single_meta - 40
                             * @hooked woocommerce_template_single_sharing - 50
                             * @hooked WC_Structured_Data::generate_product_data() - 60
                             */
                            do_action( 'woocommerce_single_product_summary' );
                        ?>


<?php
// Get the product's categories
$product_categories = get_the_terms($product->get_id(), 'product_cat');

// Assume product is not in category 69 by default
$in_special_category = false;

// Check if the product has categories and if any is category ID 69
if ($product_categories && !is_wp_error($product_categories)) {
    foreach ($product_categories as $category) {
        if ($category->term_id == 69) {
            $in_special_category = true;
            break;
        }
    }
}

// If the product is not in category ID 69, display the notice
if (!$in_special_category) : ?>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .woocommerce-product-details__short-description p{
            font-size: 16px!important;
        }

        .download-section {
            max-width: 600px;
            margin: 0 auto;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            overflow: hidden;
            position: relative;
            margin-top: 10px;
        }

        .download-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: #ED9C28;
        }

        .features-grid {
            display: grid;
            gap: 6px;
            margin-bottom: 5px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            padding: 0px;
            background: #fff;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .feature-item:hover {
            background: #fafbfc;
        }

        .feature-icon {
            flex-shrink: 0;
            width: 15px;
            height: 15px;
            background: #ED9C28;
            border-radius: 50%;
            margin-right: 12px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .feature-icon::after {
            content: '✓';
            color: white;
            font-weight: 500;
            font-size: 10px;
        }

        .feature-text {
            flex: 1;
            font-size: 14px;
            line-height: 1.5;
            color: #444;
            font-weight: 400;
        }

        .feature-text a {
            color: #ED9C28;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s ease;
        }

        .feature-text a:hover {
            color: #B57820;
        }

        .cta-section {
            text-align: center;
            padding: 0px;
            background: transparent;
            border-top: 1px solid #f0f0f0;
            margin: 5px 0;
            padding-top: 5px;
        }
        .cta-section p{
            color: #fff !important;
            font-size: 14px !important;
            margin: 0 !important;
            padding: 0 !important;
            line-height: 20px !important;
        }
        .cta-button {
            display: inline-block;
            background: #ED9C28;
            color: white !important;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            font-size: 15px;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(237, 156, 40, 0.2);
        }

        .cta-button:hover {
            background: #FFB64D;
            box-shadow: 0 3px 8px rgba(237, 156, 40, 0.3);
            color: white !important;
            text-decoration: none;
        }

        .cta-button:active {
            transform: translateY(-1px);
        }

        .highlight-badge {
            display: inline-block;
            background: #ff6b6b;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 500;
            margin-left: 8px;
        }

        @media (max-width: 768px) {
            .download-section {
                margin: 10px;
                border-radius: 8px;
            }

            .section-header {
                padding: 20px;
            }

            .section-header h2 {
                font-size: 20px;
            }

            .features-container {
                padding: 20px 15px;
            }

            .feature-item {
                padding: 10px 12px;
            }

            .feature-text {
                font-size: 13px;
            }

            .cta-section {
                padding: 20px;
            }

            .cta-button {
                padding: 10px 20px;
                font-size: 14px;
            }
        }

        /* Loại bỏ animations phức tạp */
    </style>

    <div class="download-section">

        <div class="features-container">
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon"></div>
                    <div class="feature-text">
                        Tương thích với phiên bản WordPress mới nhất
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon"></div>
                    <div class="feature-text">
                        Tương thích với hầu hết các phiên bản PHP
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon"></div>
                    <div class="feature-text">
                        Hỗ trợ cài đặt thay đổi thông tin, màu sắc chủ đạo...
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon"></div>
                    <div class="feature-text">
                        Hỗ trợ cài đặt các plugin cần thiết
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon"></div>
                    <div class="feature-text">
                        Miễn phí <a href="https://vutruso.com/dich-vu-chuyen-host-wordpress/">hosting chất lượng cao</a>
                        <span class="highlight-badge">HOT</span>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon"></div>
                    <div class="feature-text">
                        Hỗ trợ tối ưu website theo <a href="https://vutruso.com/dich-vu-toi-uu-website-wordpress/">gói tối ưu WordPress</a>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon"></div>
                    <div class="feature-text">
                        Hỗ trợ fix bug trong 12 tháng
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon"></div>
                    <div class="feature-text">
                        Fix bug vĩnh viễn
                        <span class="highlight-badge">VIP</span>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon"></div>
                    <div class="feature-text">
                        Không phát sinh thêm chi phí
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon"></div>
                    <div class="feature-text">
                        Hỗ trợ mọi thắc mắc liên quan đến website
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon"></div>
                    <div class="feature-text">
                        Được sử dụng các plugin bản quyền miễn phí
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon"></div>
                    <div class="feature-text">
                        Và còn nhiều ưu đãi khác đang chờ đón bạn
                    </div>
                </div>
            </div>

            <div class="cta-section">
                <a href="https://zalo.me/0868017791" target="_blank" rel="noopener nofollow" class="cta-button">
                    <p>💬 Bạn còn thắc mắc?</p>
                    <p>Chat với chúng tôi qua Zalo</p>
                </a>
            </div>
        </div>
    </div>



<?php endif; ?>

<?php if ($in_special_category) : ?>
<style>
#content .theme-list .item .thumb {
    min-height: unset!important;
}
</style>
<?php endif;?>


                        <div id="form-bao-gia">
                            <div id="popup">
                                <div id="close">X</div>
                                <div class="modal fade in" style="display: block;">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h4 class="modal-title">Nhận báo giá mẫu giao diện</h4>
                                        </div>
                                        <div class="modal-body">
                                            <?php echo do_shortcode('[contact-form-7 id="402" title="Báo giá"]'); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>





    </div>


</div>


<style type="text/css" media="screen">
.vts-describe h2 {
    font-weight: bold;
    border-bottom: 1px solid #cfcfd3;
    color: #FFF;
    background: #245dac;
    border: none;
    border-radius: 4px;
    box-shadow: 0px 5px 0px #a0b6d478;
    transition: all 0.4s;
    -webkit-transition: all 0.4s;
    -moz-transition: all 0.4s;
    padding: .5em 1em .55em;
    -o-transition: 0.4s;
    font-size: 17px;
}

.sc_button.orange {
    color: #FFF;
    background: #ED9C28;
    border: none;
    border-radius: 4px;
    box-shadow: 0px 5px 0px #B57820;
    transition: all 0.4s;
    -webkit-transition: all 0.4s;
    -moz-transition: all 0.4s;
    -o-transition: 0.4s;
}

.sc_button.orange:hover {
    background: #FFB64D;
}

.sc_button.orange:active {
    background: #ED9C28;
}

a.sc_button {
    cursor: pointer;
    display: inline;
    display: inline-block;
    margin: 2px;
    outline: none;
    padding: .5em 1.75em .55em;
    text-align: center;
    text-decoration: none;
    text-shadow: 0 1px 1px rgba(0, 0, 0, .3);
    vertical-align: baseline;
}

a.sc_button,
a.sc_button:hover {
    color: #fff !important;
}

a.sc_button:hover {
    text-decoration: none;
}

.sc_button.large {
    font-size: 18px;
    padding: .5em 2em .55em;
}

.download_right {
    width: 290px;
    float: left;
    padding-left: 30px;
}

.download_right .item_details span {
    float: left;
    width: 20px;
    font-weight: normal;
    text-align: left;
}

.download_right p {
    clear: both;
    margin-bottom: 10px;
    font-size: smaller;
    font-weight: bold;    
    padding-left: 10px;
    border-bottom: 1px dotted #DBDBDB;
    padding-right: 10px;
    margin: 0px 0;
    font-size: 13px !important;
}
.item_details{
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    justify-content: center;
}
.download_right p:last-child{
    border-bottom: none;
}

.download_right {
    width: 100% !important;
    float: none;
    padding-left: 0;
}

.download_right .item_details span.yes {
    background: url(https://vutruso.com/wp-content/uploads/2025/04/check.png) top right no-repeat;
    text-indent: -99999px;
}

.download_right p.including {
    margin-top: 10px;
    font-weight: normal;
    border-bottom: 0px none;
}

.download_right p.including img {
    margin-right: 10px;
}

.sidebar-single-product {
    position: sticky;
    top: 10px;
    align-self: flex-start;
    max-height: calc(100vh - 30px);
    overflow-y: auto;
}

.product_meta {
    margin-top: 0;
}

.infomation > div {
    padding-bottom: 0;
}

.woocommerce-product-details__short-description{
    padding: 0!important;
}

.woocommerce-product-details__short-description ul {
    margin-bottom: 0;
}

.single-product-main {
    display: flex;
    flex-wrap: wrap;
}

.vts-tab>.single {
    padding: 0 20px;
}

.product_meta .posted_in {
    margin-top: 10px;
}

.fluid {
    display: flex;
    margin-top: 3%;
}

.fluid a {
    flex-grow: 1;
    margin-right: 0.25rem;
}

.fluid a:last-child {
    margin-right: 0rem;
}

.woocommerce-product-details__short-description li {
    color: #43627F;
    margin-top: 3px;
    margin-bottom: 0;
}

.vbox-content img {
    width: auto !important;
    height: auto !important;
}

.flex-viewport {
    border-radius: 4px;
}

.vbox-content img {
    border-radius: 4px;
}

.demo-theme a {
    border-radius: 4px;
}

.demo-theme {
    text-align: center;
}

.vbox-close {
    right: 10px !important;
    font-size: 41px !important;
    top: -11px !important;
}

.vbox-title {
    font-size: 14px !important;
}

.vbox-overlay {
    background: rgba(23, 23, 23, 0.85);
}

.single_add_to_cart_button {
    width: 48%;
    text-align: center;
    margin: 0 auto;
    border: 2px solid #22afcc;
    background: #149c90;
    padding: 0 30px;
    line-height: 39px;
    height: 39px;
    display: block;
    font-size: inherit;
    font-weight: normal;
    border-radius: 6px;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: 1rem;
}

.single_add_to_cart_button:before {
    content: '\f019';
    padding-right: 3px;
}

#content .buy-vts .price {
    margin: 3%;
}

.product_list_widget {
    border: 1px solid #ccc;
    padding: 8px;
    border-radius: 4px;
    overflow: hidden;
}

.product_list_widget li {
    float: left;
    clear: both;
    margin-bottom: 2%;
    border-bottom: 1px dashed #ccc;
    padding-bottom: 2%;
}

.product_list_widget img {
    width: 50%;
    float: left;
    overflow: hidden;
}

.product_list_widget span.product-title {
    padding-left: 3%;
    font-size: 17px;
    float: left;
    width: 50%;
    margin-bottom: 1%;
    line-height: 26px;
}

.product_list_widget>li>.amount {
    padding-left: 3%;
    font-size: 17px;
    margin-top: 7px;
    clear: both;
    display: inline-block;
}

.product_list_widget>li>del {
    padding-left: 3%;
    font-size: 16px;
}

.product_list_widget>li>ins {
    padding-left: 3%;
    font-size: 16px;
}

.product_list_widget li:last-child {
    border-bottom: none;
}

.product_list_widget>li>del>.amount {
    text-decoration: line-through;
}

.product_list_widget>li>button {
    margin-left: 3%;
    padding: 4px 5px;
    border-radius: 3px;
    font-size: 13px;
    margin-top: 1%;
    font-weight: normal;
}

.vts-describe .single h3 {
    line-height: 32px;
}

.vts-describe h2 {
    line-height: 32px;
}

.vts-describe li,
.vts-describe p {
    text-align: justify;
}

@media (max-width:600px) {

    .vts-tab>.single {
        padding: 0 10px;
    }
}
</style>



<script>
document.addEventListener("DOMContentLoaded", function() {
    new VenoBox({
        selector: ".my-link",
        numeration: true,
        infinigall: true,
        spinner: "bounce",
        fitView: false, // Đặt false để cho phép hình ảnh hiển thị kích thước đầy đủ (có thể zoom)
        navTouch: true, // Cho phép vuốt/kéo để di chuyển
        maxWidth: "100%", // Chiều rộng tối đa
        shareStyle: "bar",
        toolsBackground: "#1C1C1C",
        toolsColor: "#FFFFFF"
    });
});
</script>