header {
  display: block;
}

ul {
  margin-top: 0;
  margin-bottom: 1rem;
}

ul ul {
  margin-bottom: 0;
}

strong {
  font-weight: bolder;
}

a {
  color: #007bff;
  text-decoration: none;
  background-color: transparent;
}

a:hover {
  color: #0056b3;
  text-decoration: underline;
}

button {
  border-radius: 0;
}

button:focus:not(:focus-visible) {
  outline: 0;
}

button {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button {
  overflow: visible;
}

button {
  text-transform: none;
}

[type="button"],
button {
  -webkit-appearance: button;
}

[type="button"]:not(:disabled),
button:not(:disabled) {
  cursor: pointer;
}

.table {
  width: 100%;
  margin-bottom: 1rem;
  color: #212529;
}

.navbar {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0.5rem 1rem;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.px-1 {
  padding-right: 0.25rem !important;
}

.px-1 {
  padding-left: 0.25rem !important;
}

header #topbar {
  height: 55px;
  line-height: 55px;
  background: #fcfcfc;
  font-weight: 500;
}

header #topbar .user {
  overflow: hidden;
  float: left;
  font-size: 16.68px;
  color: #c82020;
}

header #topbar .contact {
  display: block;
  float: right;
}

header #topbar .contact .telephone {
    float: right;
    background: #0081c9;
    color: #fff;
    font-size: 16.67px;
    font-weight: 300;
    height: 35px;
    margin: 10px 5px 0 0;
    line-height: 35px;
    padding: 0 14px 0 0;
    border-radius: 0 31px 31px 0;
    border-left: 3px solid #007bff4a;
    background-image: linear-gradient(to right top, #156ad2, #185ece, #0066bf, #0086c6, #34a4ca);
    display: flex;
    align-items: center;
    flex-direction: row;
    flex-wrap: nowrap;
}

header #topbar .contact .telephone a {
  color: #fff;
}

header #topbar .contact .telephone i {
  display: block;
  float: left;
  width: 40px;
  height: 35px;
  border-radius: 18px;

}

header #topbar .contact .social {
  float: right;
  height: 35px;
  margin: 10px 0;
  line-height: 35px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-content: center;
  justify-content: center;
  align-items: center;
}

header #topbar .contact .social span {
  display: block;
  float: left;
  width: 35px;
  height: 35px;
  transition: border 0.3s;
  cursor: pointer;
  border: 1px #87abbe57 solid;
}

.nav-mobile span {
  border: 2px #6ca6c6 solid !important;
}

header #topbar .contact .social span i {
  display: block;
  width: 35px;
  height: 35px;
  font-size: 22px;
  text-align: center;
  line-height: 32px;
  transition: color 0.3s;
}

header #topbar .contact .social span.facebook:hover {
  border-color: #3b5999;
}

header #topbar .contact .social span:hover i {
  color: #3b5999;
}

header #menu {
  height: 60px;
  line-height: 60px;
  border-top: 1px #cfdff1 solid;
  border-bottom: 5px #dbe5e8 solid;
  background: #f6f6f6;
}

header #menu .menu {
  display: block;
  height: 55px;
}

header #menu .menu ul li {
  float: left;
  position: relative;
}

header #menu .menu ul li a {
  display: block;
  padding: 0 24px;
  line-height: 55px;
  height: 50px;
  border-right: 1px #eaeaea solid;
  font-size: 1rem;
  font-weight: 600;
  color: #005582;
  font-family: "Montserrat", sans-serif;
}

header #menu .menu ul li a:hover {
  color: #0081c9;
}

header #menu .menu ul li:hover a {
  height: 55px;
}

header #menu .menu ul li a:after {
  content: "";
  position: absolute;
  width: 11px;
  height: 11px;
  background: #a8c7e7;
  right: -4.5px;
  top: 22px;
  opacity: 0.5;
}

header #menu .menu ul li ul {
  display: none;
  position: absolute;
  top: 55px;
  left: 0;
  width: 395px;
  transition: all 0.3s;
  z-index: 999;
}

header #menu .menu ul li:hover ul {
  display: block;
}

header #menu .menu ul li ul li {
  display: block;
  float: none;
  padding: 0;
  box-sizing: initial;
}

header #menu .menu ul li ul li a {
  display: block;
  line-height: 36px;
  height: 0;
  background: #f7f7f7;
  color: #217bd4;
  border-right: 1px solid #caccce;
  border-left: 1px solid #caccce;
  border-bottom: 1px solid #d7dde3;
  transition: all 0.3s;
  text-transform: none;
  padding: 4px;
  font-weight: 400;
}

header #menu .menu ul li:hover ul li a {
  height: 44px;
}

header #menu .menu ul li ul li a:hover {
  color: #48494a;
}

header #menu .menu ul li ul li a:before {
  content: "\f054";
  font: normal normal normal 14px/1 FontAwesome;
  position: relative;
  float: left;
  line-height: 40px;
  height: 0;
  width: 35px;
  text-align: center;
  color: #b59b9b;
  /*border-right: 1px solid #caccce;*/
  font-size: 12px;
  margin-right: 2px;
  transition: all 0.3s;
}

header #menu .menu ul li ul li a:hover:before {
  color: #48494a;
}

header #menu .menu ul li:hover ul li a:before {
  height: 40px;
}

header #menu .menu ul li ul li a:after {
  display: none;
}

.logo > .logo-title {
  height: 55px;
}

#infoBlock .cell .icon:before,
.vtsSocial a:before {
  content: attr(data-icon);
  font-style: normal !important;
  font-weight: 400 !important;
  font-variant: normal !important;
  text-transform: none !important;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.innerContainer {
  max-width: 1268px;
  margin: auto;
  overflow: hidden;
  padding: 0 5px;
}

.vtsSocial {
  overflow: hidden;
}

.vtsSocial li {
  float: left;
  margin-left: 6px;
}

.vtsSocial li:first-child {
  margin-left: 0;
}

.vtsSocial a {
  display: block;
  width: 20px;
  height: 20px;
}

#infoBlock {
  background: #0d47a1;
  padding: 6px 0;
  color: #fff !important;
  clear: both;
}

#infoBlock .innerContainer {
  background-size: 6px 56px;
  padding-left: 15px;
}

#infoBlock .table {
  width: 100%;
  display: flex;
  color: #fff !important;
  margin: 0 !important;
}

#infoBlock .cell {
    display: table-cell;
    vertical-align: middle;
    padding: 2px 0px 2px 50px;
    background-size: 6px 56px;
    line-height: 15px;
    font-size: 14px;
    position: relative;
}

#infoBlock .cell.social {
  width: 27%;
}

#infoBlock .cell .icon {
  display: inline-block;
  height: 40px;
  margin-right: 10px;
  position: absolute;
  left: 10px;
  top: 50%;
  margin-top: -20px;
}

#infoBlock .cell .icon:before {
  font-size: 40px;
  vertical-align: middle;
}

#infoBlock .cell a {
  color: #fbbd00;
  display: inline-block;
  font-size: 15px;
}

#infoBlock .cell br + a,
#infoBlock .vtsSocial {
  margin-top: 5px;    margin-bottom: 0px;
}





@-webkit-keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.post-template-default .row {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
}

header .post-template-default .row {
      align-items: center;
}

.vts_posts-tabs {
  margin-bottom: 20px;
}

.vts_tabs {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0 0 15px 0;
  border-bottom: 1px solid #dee2e6;
}

.vts_tabs .tab-link {
  padding: 10px 15px;
  cursor: pointer;
  margin-bottom: -1px;
  background-color: transparent;
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
  transition: all 0.2s ease;
  color: #007bff;
}

.vts_tabs .tab-link:hover {
  border-color: #e9ecef #e9ecef #dee2e6;
  text-decoration: none;
}

.vts_tabs .tab-link.active.show {
  color: #495057;
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
}

.card-body {
  flex: 1 1 auto;
  padding: 1.25rem;
}

.tab-content > .tab-pane {
  display: none;
}

.tab-content > .active.show {
  display: block;
}

/* Style cho phần nội dung tab */
.vts_posts-tabs-related_posts {
  display: flex;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f3f3f3;
}

.vts_posts-tabs-related_posts > div {
  display: flex;
  width: 100%;
}

.search-thumbnail {
  width: 80px;
  height: 60px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  margin-right: 15px;
  flex-shrink: 0;
  border-radius: 4px;
}

.vts_posts-tabs-title {
  font-weight: 600;
  color: #333;
  text-decoration: none;
  margin-bottom: 5px;
  display: block;
}

.vts_posts-tabs-title span {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-size: 14px;
  line-height: 1.4;
}

.vts_posts-tabs-footer {
  font-size: 12px;
  color: #888;
  margin-top: 5px;
}

.vts_posts-tabs-post-author {
  color: #007bff;
  text-decoration: none;
}

/* Style cho widget */
.widget {
  margin-bottom: 2rem;
}

.widget-title {
  margin-bottom: 1rem;
  font-size: 18px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 2px solid #f3f3f3;
}


@media all and (max-width: 1100px) {
  #infoBlock {
    display: none;
    border-top: 1px solid #f1f1f1;
    padding: 0;
  }

  #infoBlock .innerContainer {
    background: rgba(0, 0, 0, 0.15);
    padding: 0;
  }

  #infoBlock .table,
  #infoBlock .row {
    display: block;
  }

  #infoBlock .cell {
    display: block;
    background: 0 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    width: 50% !important;
    padding: 10px 10px 10px 60px;
    min-height: 70px;
    font-size: 13px;
    line-height: 16px;
    float: left;
  }
}

.card-columns {
  display: flex;
}

td {
  padding-left: 0 !important;
}

#content .vts-detail {
  clear: both;
}

.cover-div:hover p {
  color: #fff;
}

.main-single-post li {
  font-size: 18px !important;
}

.main-single-post ol.breadcrumb li {
  font-size: 15px !important;
}


.main-single-post ol ul li {
  list-style-type: disc !important;
}

.infomation h3 {
  font-size: 20px;
  line-height: 29px;
  color: #43627f;
}

.infomation > div {
  padding: 10px;
}

.vts-describe h2 {
  font-size: 23px;
  margin-top: 20px;
  margin-bottom: 20px;
}

.vts-describe li {
  line-height: 30px;
}

.vts-describe h4 {
  font-size: 18px;
  line-height: 34px;
}

.vts-single-content > ol li {
  list-style-type: auto !important;
}

.single .vts-toc-container ol ol {
  margin-top: 8px;
}

.single-post pre code {
  font-size: 96%;
  color: unset;
  padding: 0;
  margin: 0;
  padding-top: 0;
}

table tbody tr:first-child td {
  font-weight: bold;
}

#vts-toc-article li ul li {
  list-style-type: circle !important;
}

.page-navigation .pagination li:nth-last-child(2) {
  display: none;
}

.main-content .title span {
  line-height: 28px !important;
}

body.custom-background {
  background-attachment: fixed !important;
}

.main-single-post > h2 {
  position: relative;
  color: #1c1c1c;
  width: fit-content;
}

.main-single-post > h2::after {
  position: absolute;
  content: "";
  background-color: #ffcd01;
  width: 100%;
  left: 0;
  border-radius: 0.375rem;
  z-index: -1;
  bottom: 0;
  height: 0.6rem;
}

.blog-sidebar {
  position: sticky;
  top: 6px;
  box-shadow: 0 0 6px #000;
  color: #fff;
}

.single-post pre strong {
  font-weight: normal;
}

.breadcrumb {
  padding: 5px;
  border-radius: 0 0 3px 3px;
  list-style-type: decimal;
  white-space: nowrap;
  display: block;
  overflow: hidden;
  height: 39px;
  overflow-x: scroll;
  background: transparent;
  padding-top: 13px;
}

#toggle-read-more:after {
  content: "";
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHdpZHRoPScyNCcgaGVpZ2h0PScyNCcgdmlld0JveD0nMCAwIDI0IDI0Jz48cGF0aCBkPSdNMTUuODggOS4yOSAxMiAxMy4xNyA4LjEyIDkuMjlhLjk5Ni45OTYgMCAxIDAtMS40MSAxLjQxbDQuNTkgNC41OWMuMzkuMzkgMS4wMi4zOSAxLjQxIDBsNC41OS00LjU5YS45OTYuOTk2IDAgMCAwIDAtMS40MWMtLjM5LS4zOC0xLjAzLS4zOS0xLjQyIDAnLz48L3N2Zz4=);
  position: absolute;
  right: 0;
  z-index: 99999;
  top: 3px;
  cursor: pointer;
  border-radius: 8px;
  float: left;
  background-position: center;
  background-size: 14px;
  background-repeat: no-repeat;
  width: 14px;
  height: 16px;
  margin-right: 6px;
}

.vts-single-content .show #toggle-read-more:after {
  content: "";
  transform: rotate(180deg);
}

.copy-button:before {
  content: "";
  cursor: pointer;
  border-radius: 8px;
  float: left;
  background-position: center;
  background-size: 12px;
  background-repeat: no-repeat;
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0NDggNTEyIj48IS0tIUZvbnQgQXdlc29tZSBGcmVlIDYuNi4wIGJ5IEBmb250YXdlc29tZSAtIGh0dHBzOi8vZm9udGF3ZXNvbWUuY29tIExpY2Vuc2UgLSBodHRwczovL2ZvbnRhd2Vzb21lLmNvbS9saWNlbnNlL2ZyZWUgQ29weXJpZ2h0IDIwMjQgRm9udGljb25zLCBJbmMuLS0+PHBhdGggZD0iTTIwOCAwTDMzMi4xIDBjMTIuNyAwIDI0LjkgNS4xIDMzLjkgMTQuMWw2Ny45IDY3LjljOSA5IDE0LjEgMjEuMiAxNC4xIDMzLjlMNDQ4IDMzNmMwIDI2LjUtMjEuNSA0OC00OCA0OGwtMTkyIDBjLTI2LjUgMC00OC0yMS41LTQ4LTQ4bDAtMjg4YzAtMjYuNSAyMS41LTQ4IDQ4LTQ4ek00OCAxMjhsODAgMCAwIDY0LTY0IDAgMCAyNTYgMTkyIDAgMC0zMiA2NCAwIDAgNDhjMCAyNi41LTIxLjUgNDgtNDggNDhMNDggNTEyYy0yNi41IDAtNDgtMjEuNS00OC00OEwwIDE3NmMwLTI2LjUgMjEuNS00OCA0OC00OHoiLz48L3N2Zz4=);
  width: 14px;
  height: 16px;
  margin-right: 2px;
}

pre:before {
  content: "\2022 \2022 \2022";
  position: absolute;
  top: 8px;
  left: 0;
  color: #b1c9d1;
  width: 100%;
  font-size: 2.5rem;
  margin: 0;
  line-height: 0;
  padding: 1px 0 12px;
  text-indent: 5px;
  letter-spacing: -4px;
  border-bottom: 1px solid #c1d3d952;
}

.single-post pre strong {
  color: #c7254e;
}

.single-post pre button {
  align-items: center;
  cursor: pointer;
  justify-content: center;
  position: absolute;
  right: 0;
  top: 2px;
  width: 2.7rem;
  z-index: 10;
  color: #333;
  font-size: 12px;
  padding-bottom: 0;
  padding-top: 0;
  border: none;
  background: transparent;
}

.copy-notification.active {
  display: block;
  align-items: center;
  cursor: pointer;
  justify-content: center;
  position: absolute;
  right: 0;
  top: 2px;
  width: 3.5rem;
  z-index: 10;
  color: #333;
  font-size: 12px;
  padding-bottom: 0;
  padding-top: 0;
  border: none;
  background: transparent;
}

.post-template-full-width #content > .container-fluid {
  max-width: 80% !important;
  margin: 0 auto;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}


.container .full-width {
    flex: 0 0 70.333333%;
    max-width: 100%;
}

.post-template-full-width #toc_container {
  width: 100%;
}

.single-post .author svg {
  margin-right: 4px;
}

.vts-single-content img:after {
  content: "Click vào ảnh để phóng to";
  display: block;
  font-size: 12px;
  color: #555;
  text-align: center;
  margin-top: 5px;
}

@media (max-width: 767px) {
  .telegram {
    display: none !important;
  }
}

.blog-archive img {
  max-height: 200px;
}

.infoBlock i {
  border-radius: 8px;
  float: left;
  width: 30px;
  height: 30px;
  background-color: #fcb600;
  background-position: center;
  background-size: 20px;
  background-repeat: no-repeat;
}

.infoBlock .contactphone i {
  background-image: url("data:image/svg+xml;base64,CjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiBjbGFzcz0iaWNvbiBpY29uLXRhYmxlciBpY29uLXRhYmxlci1waG9uZS1jYWxsIiB3aWR0aD0iNDQiIGhlaWdodD0iNDQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZT0iIzJjM2U1MCIgZmlsbD0ibm9uZSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj4KICA8cGF0aCBzdHJva2U9Im5vbmUiIGQ9Ik0wIDBoMjR2MjRIMHoiIGZpbGw9Im5vbmUiLz4KICA8cGF0aCBkPSJNNSA0aDRsMiA1bC0yLjUgMS41YTExIDExIDAgMCAwIDUgNWwxLjUgLTIuNWw1IDJ2NGEyIDIgMCAwIDEgLTIgMmExNiAxNiAwIDAgMSAtMTUgLTE1YTIgMiAwIDAgMSAyIC0yIiAvPgogIDxwYXRoIGQ9Ik0xNSA3YTIgMiAwIDAgMSAyIDIiIC8+CiAgPHBhdGggZD0iTTE1IDNhNiA2IDAgMCAxIDYgNiIgLz4KPC9zdmc+Cg==");
}

.infoBlock .contactmail i {
  background-image: url("data:image/svg+xml;base64,CjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiBjbGFzcz0iaWNvbiBpY29uLXRhYmxlciBpY29uLXRhYmxlci1tYWlsLXN0YXIiIHdpZHRoPSI0NCIgaGVpZ2h0PSI0NCIgdmlld0JveD0iMCAwIDI0IDI0IiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlPSIjMmMzZTUwIiBmaWxsPSJub25lIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPgogIDxwYXRoIHN0cm9rZT0ibm9uZSIgZD0iTTAgMGgyNHYyNEgweiIgZmlsbD0ibm9uZSIvPgogIDxwYXRoIGQ9Ik0xMCAxOWgtNWEyIDIgMCAwIDEgLTIgLTJ2LTEwYTIgMiAwIDAgMSAyIC0oaDE0YTIgMiAwIDAgMSAyIDJ2NC41IiAvPgogIDxwYXRoIGQ9Ik0zIDdsOSA2bDkgLTYiIC8+CiAgPHBhdGggZD0iTTE3LjggMjAuODE3bC0yLjE3MiAxLjEzOGEuMzkyIC4zOTIgMCAwIDEgLS41NjggLS40MWwuNDE1IC0yLjQxMWwtMS43NTcgLTEuNzA3YS4zODkgLjM4OSAwIDAgMSAuMjE3IC0uNjY1bDIuNDI4IC0uMzUybDEuMDg2IC0yLjE5M2EuMzkyIC4zOTIgMCAwIDEgLjcwMiAwbDEuMDg2IDIuMTkzbDIuNDI4IC4zNTJhLjM5IC4zOSAwIDAgMSAuMjE3IC42NjVsLTEuNzU3IDEuNzA3bC40MTQgMi40MWEuMzkgLjM5IDAgMCAxIC0uNTY3IC40MTFsLTIuMTcyIC0xLjEzOHoiIC8+Cjwvc3ZnPgo=");
}

.infoBlock .address i {
  background-image: url("data:image/svg+xml;base64,CjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiBjbGFzcz0iaWNvbiBpY29uLXRhYmxlciBpY29uLXRhYmxlci1tYXAtcGluLXNoYXJlIiB3aWR0aD0iMjkiIGhlaWdodD0iMjkiIHZpZXdCb3g9IjAgMCAyNCAyNCIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZT0iIzJjM2U1MCIgZmlsbD0ibm9uZSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj4KICA8cGF0aCBzdHJva2U9Im5vbmUiIGQ9Ik0wIDBoMjR2MjRIMHoiIGZpbGw9Im5vbmUiLz4KICA8cGF0aCBkPSJNOSAxMWEzIDMgMCAxIDAgNiAwYTMgMyAwIDAgMCAtNiAwIiAvPgogIDxwYXRoIGQ9Ik0xMi4wMiAyMS40ODVhMS45OTYgMS45OTYgMCAwIDEgLTEuNDMzIC0uNTg1bC00LjI0NCAtNC4yNDNhOCA4IDAgMSAxIDEzLjQwMyAtMy42NTEiIC8+CiAgPHBhdGggZD0iTTE2IDIybDUgLTUiIC8+CiAgPHBhdGggZD0iTTIxIDIxLjV2LTQuNWgtNC41IiAvPgo8L3N2Zz4K");
}

.infoBlock .mst i {
  background-image: url("data:image/svg+xml;base64,CjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiBmaWxsPSJub25lIiB2aWV3Qm94PSIwIDAgMjQgMjQiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2U9ImN1cnJlbnRDb2xvciIgY2xhc3M9InctNiBoLTYiPgogIDxwYXRoIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgZD0iTTMuNzUgNC44NzVjMC0uNjIxLjUwNC0xLjEyNSAxLjEyNS0xLjEyNWg0LjVjLjYyMSAwIDEuMTI1LjUwNCAxLjEyNSAxLjEyNXY0LjVjMCAuNjIxLS41MDQgMS4xMjUtMS4xMjUgMS4xMjVoLTQuNUExLjEyNSAxLjEyNSAwIDAgMSAzLjc1IDkuMzc1di00LjVaTTMuNzUgMTQuNjI1YzAtLjYyMS41MDQtMS4xMjUgMS4xMjUtMS4xMjVoNC41Yy42MjEgMCAxLjEyNS41MDQgMS4xMjUgMS4xMjV2NC41YzAgLjYyMS0uNTA0IDEuMTI1LTEuMTI1IDEuMTI1aC00LjVhMS4xMjUgMS4xMjUgMCAwIDEtMS4xMjUtMS4xMjV2LTQuNVpNMTMuNSA0Ljg3NWMwLS42MjEuNTA0LTEuMTI1IDEuMTI1LTEuMTI1aDQuNWMuNjIxIDAgMS4xMjUuNTA0IDEuMTI1IDEuMTI1djQuNWMwIC42MjEtLjUwNCAxLjEyNS0xLjEyNSAxLjEyNWgtNC41QTEuMTI1IDEuMTI1IDAgMCAxIDEzLjUgOS4zNzV2LTQuNVoiLz4KICA8cGF0aCBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGQ9Ik02Ljc1IDYuNzVoLjc1di43NWgtLjc1di0uNzVaTTYuNzUgMTYuNWguNzV2Ljc1aC0uNzV2LS43NVpNMTYuNSA2Ljc1aC43NXYuNzVoLS43NXYtLjc1Wk0xMy41IDEzLjVoLjc1di43NWgtLjc1di0uNzVaTTEzLjUgMTkuNWguNzV2Ljc1aC0uNzV2LS43NVpNMTkuNSAxMy41aC43NXYuNzVoLS43NXYtLjc1Wk0xOS41IDE5LjVoLjc1di43NWgtLjc1di0uNzVaTTE2LjUgMTYuNWguNzV2Ljc1aC0uNzV2LS43NVoiLz4KPC9zdmc+Cg==");
}

header #menu .menu ul li:hover ul li a:before {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22none%22%20viewBox%3D%220%200%2024%2024%22%20stroke-width%3D%221.5%22%20stroke%3D%22%23217bd4%22%20class%3D%22w-6%20h-6%22%3E%20%3Cpath%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%20d%3D%22m8.25%204.5%207.5%207.5-7.5%207.5%22%20%2F%3E%3C%2Fsvg%3E");
  width: 22px;
  height: 15px;
  display: block;
  content: "";
  background-repeat: no-repeat;
  background-position: center;
  float: left;
  top: 9px;
}

#hotline .fa-phone:before {
  display: none;
}


footer .footerCols .title:before {
  width: 25px;
  height: 20px;
  display: block;
  content: "";
  background-repeat: no-repeat;
  background-position: center;
  float: left;
}

footer .footerCols .dichvu .title:before {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2225%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22none%22%20viewBox%3D%220%200%2024%2024%22%20stroke-width%3D%221.5%22%20stroke%3D%22%23fff%22%20class%3D%22w-6%20h-6%22%3E%3Cpath%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%20d%3D%22M9%2012.75%2011.25%2015%2015%209.75m-3-7.036A11.959%2011.959%200%200%201%203.598%206%2011.99%2011.99%200%200%200%203%209.749c0%205.592%203.824%2010.29%209%2011.623%205.176-1.332%209-6.03%209-11.622%200-1.31-.21-2.571-.598-3.751h-.152c-3.196%200-6.1-1.248-8.25-3.285Z%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E");
}

footer .footerCols .themes .title:before {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20class%3D%22icon%20icon-tabler%20icon-tabler-bulb%22%20width%3D%2225%22%20height%3D%2220%22%20viewBox%3D%220%200%2024%2024%22%20stroke-width%3D%221.5%22%20stroke%3D%22%23fff%22%20fill%3D%22none%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%3E%20%3Cpath%20stroke%3D%22none%22%20d%3D%22M0%200h24v24H0z%22%20fill%3D%22none%22%2F%3E%20%3Cpath%20d%3D%22M3%2012h1m8%20-9v1m8%208h1m-15.4%20-6.4l.7%20.7m12.1%20-.7l-.7%20.7%22%20%2F%3E%20%3Cpath%20d%3D%22M9%2016a5%205%200%201%201%206%200a3.5%203.5%200%200%200%20-1%203a2%202%200%200%201%20-4%200a3.5%203.5%200%200%200%20-1%20-3%22%20%2F%3E%20%3Cpath%20d%3D%22M9.7%2017l4.6%200%22%20%2F%3E%3C%2Fsvg%3E");
}

footer .footerCols .contact .title:before {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2225%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22none%22%20viewBox%3D%220%200%2024%2024%22%20stroke-width%3D%221.5%22%20stroke%3D%22%23fff%22%20class%3D%22w-6%20h-6%22%3E%20%3Cpath%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%20d%3D%22M13.19%208.688a4.5%204.5%200%200%201%201.242%207.244l-4.5%204.5a4.5%204.5%200%200%201-6.364-6.364l1.757-1.757m13.35-.622%201.757-1.757a4.5%204.5%200%200%200-6.364-6.364l-4.5%204.5a4.5%204.5%200%200%200%201.242%207.244%22%20%2F%3E%3C%2Fsvg%3E");
}

footer .footerCols .menu a:before {
  line-height: 20px;
  margin-right: 3px;
  font-size: 12px;
  display: block;
  content: "";
  color: #fff;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22none%22%20viewBox%3D%220%200%2024%2024%22%20stroke-width%3D%221.5%22%20stroke%3D%22%23fff%22%20class%3D%22w-6%20h-6%22%3E%20%3Cpath%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%20d%3D%22m5.25%204.5%207.5%207.5-7.5%207.5m6-15%207.5%207.5-7.5%207.5%22%20%2F%3E%3C%2Fsvg%3E");
  background-repeat: no-repeat;
  background-position: center;
  float: left;
  width: 12px;
  height: 20px;
}

::-webkit-scrollbar {
  width: 2px;
  height: 3px;
}

.main-single-post ol li {
  font-size: 18px;
  list-style-type: decimal !important;
}

.single-post ol.breadcrumb {
  list-style-type: decimal;
  white-space: nowrap;
  display: block;
  overflow: hidden;
  height: 39px;
  overflow-x: scroll;
  background: transparent;
  padding-top: 10px;
}

.post-tags a {
  text-transform: none;
  font-size: 11px;
}

ul#menu-lien-ket-nhanh a {
  text-transform: unset !important;
}

.blog-archive {
  min-height: 440px;
}

.page img {
  border-radius: 6px;
}

.menu-item-35799 > a {
  border-right: 0 !important;
}

.menu-item-35799 > a:after {
  content: none !important;
}

.page .vts_time {
  top: 0;
  position: absolute !important;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(1px, 1px, 1px, 1px);
  padding-bottom: 10px;
}

.page .vts_time span {
  padding-right: 10px;
}

.page-template-full-width .main-content .title span {
  padding-left: 0;
}

.breadcrumb {
  padding: 5px;
  border-radius: 0 0 3px 3px;
}

/*TOC*/
#vts-toc-container {
  width: 100%;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  padding: 10px 0;
  padding-top: 0;
}

#vts-toc-article a {
  display: inline;
  text-decoration: none;
  margin-left: 0;
}

.title_lvl {
  margin-right: 5px;
  font-weight: 700;
  display: none;
}

.title_lvl1 {
  font-weight: 700;
  display: block;
}

.title_lvl2 {
  font-weight: 500;
  margin-left: 15px;
  display: block;
}

.title_lvl3 {
  font-weight: 400;
  margin-left: 30px;
  display: block;
}

.title_lvl4 {
  font-weight: 300;
  margin-left: 45px;
  display: block;
}

.title_lvl5 {
  font-weight: 200;
  margin-left: 60px;
  display: block;
}

#vts-toc-article ul li {
  list-style: none !important;
  margin-left: 0;
}

#vts-toc-container ol > li {
  list-style-type: square !important;
  margin-left: 10px;
}

#vts-toc-container ol ul {
  padding: 10px;
  margin-left: 30px;
  margin-bottom: 0;
  padding-bottom: 0;
}

.read-more-btn {
  max-width: 100%;
  margin: 0 auto;
  margin-left: 20px;
  width: auto;
  display: flex;
  transition: all 0.15s ease;
  font-size: 0.8rem;
  background-color: #f0f0f3;
  box-shadow: -10px -10px 30px #fff, 1px 3px 10px #aeaec0;
  border: none;
  cursor: pointer;
  padding: 0.2rem 1.6rem;
  color: #5f5f5e;
  text-align: left;
  outline: none;
  position: relative;
  gap: 6rem;
  align-items: flex-end;
  margin-bottom: -2px;
  margin-top: 5px;
  padding-left: 10px;
  border-radius: 5px;
}

.read-more-btn:hover {
  background-color: #0076b6;
  color: #fff;
}

#vts-toc-article {
  max-height: 79px;
  overflow: hidden;
  transition: max-height 0.5s ease-out;
  margin-bottom: 0;
  padding: 0 10px;
}

#vts-toc-article.expanded {
  max-height: 1000px;
  transition: max-height 0.5s ease-in;
}

.show #vts-toc-article {
  max-height: unset;
}

#vts-toc-container {
  position: relative;
}

.vts-single-content .show-button::before {
  content: "";
  background: linear-gradient(
    to bottom,
    rgb(255 255 255 / 28%) 0%,
    #ffffff 50%,
    #9c9c9c57 100%
  );
  position: absolute;
  bottom: 0px;
  width: 100%;
  height: 56.8px;
  transition: opactiy 500ms ease, visibility 500ms ease;
  border-radius: 8px;
}

.show::before {
  background: none !important;
}

@media screen and (max-width: 767px) {
  #content p,
  .text-block p {
    text-align: justify;
  }

  #content p {
    text-align: justify;
  }

  .vts-single-content {
    padding: 0;
  }

  footer .footerBottom a {
    font-size: 1em !important;
  }

  footer .footerBottom li {
    margin: 6px 1px;
  }

  .has-background-light img,
  .hero-chuyenhost img {
    max-width: 100% !important;
    margin-top: 23% !important;
  }

  .button {
    white-space: unset !important;
    height: auto !important;
  }

  .chuyenghiep,
  div.hero-chuyenhost {
    padding: 2rem 0rem 1rem !important;
  }

  .button.is-rounded {
    margin-top: 10px;
  }

  .button2 {
    margin-top: 10px;
  }

  .post-template-full-width #content > .container-fluid {
    padding-right: 0px !important;
    padding-left: 0px !important;
  }
}

.vts-describe .single h3 {
  font-size: 20px;
  color: #095c89;
  margin-top: 15px;
  margin-bottom: 15px;
}

.table td,
.table th {
  vertical-align: middle !important;
  line-height: 24px;
  font-weight: normal;
  font-size: 16px;
}

@media all and (max-width: 768px) {
  #infoBlock .cell {
    width: 100% !important;
  }
}

#search-btn {
  border-radius: 2px;
  font-size: 22px;
  color: #e0eff6;
  transition: color 0.3s;
  float: left;
  border: 1px #e0eff6 solid;
  width: 35px;
  height: 35px;
  margin: 0 5px;
}

.fa-search:before {
  display: block;
  width: 32px;
  height: 32px;
  font-size: 22px;
  text-align: center;
  line-height: 32px;
  color: #6ca6c6;
  transition: color 0.3s;
}

#search-btn:hover {
  background-color: #d7e7f0;
  cursor: pointer;
  border-radius: 7px;
}

.navbar-toggle .icon-bar:last-child {
  width: 22px;
  margin-left: 6px;
  margin-top: 3px;
}

.navbar-toggle .icon-bar:nth-child(2) {
  margin-left: 4px;
  margin-top: 3px;
}

.navbar-toggle .icon-bar + .icon-bar {
  margin-top: 6px;
}

.navbar .navbar-toggle {
  display: block;
  border: none;
}

.sr-only {
  position: absolute;
  width: 1px !important;
  height: 1px !important;
  padding: 0;
  margin: -1px !important;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0 !important;
}

.navbar-toggle:focus {
  outline: 0;
}

.navbar-toggle .icon-bar {
  display: block !important;
  width: 32px !important;
  height: 2px !important;
  border-radius: 0;
}

.navbar-inverse {
  border-color: #080808;
}

.navbar-wrapper {
  padding-top: 0;
  padding-bottom: 0;
  position: relative;
  top: 0;
  right: 0;
  left: 0;
}

.navbar {
  webkit-transition: background 0.5s ease-in-out, padding 0.2s ease-in-out;
  -moz-transition: background 0.5s ease-in-out, padding 0.2s ease-in-out;
  transition: background 0.5s ease-in-out, padding 0.2s ease-in-out;
  z-index: 1000;
  padding: 0 !important;
  border-radius: 0;
  padding-left: 0;
}

.navbar-toggle {
  cursor: pointer;
  position: relative !important;
  line-height: 0;
  float: right;
  margin: 0 !important;
  width: 32px;
  padding: 5px 0 !important;
  border: 0;
  background: 0 0;
  top: 0;
  right: 0;
}

.navbar-inverse .navbar-toggle:focus,
.navbar-inverse .navbar-toggle:hover {
  background-color: transparent !important;
}

.push_menu {
  z-index: 99999 !important;
  float: right;
  overflow: hidden;
}

.push_menu .push-nav {
  transition: all 0.5s ease-out;
}

.mobile-show .navbar-inverse .navbar-toggle {
  border-color: transparent;
}

@media only screen and (max-width: 1394px) {
  .navbar .navbar-toggle {
    display: block !important;
    border: none;
  }
}

@media only screen and (max-width: 1200px) {
  .navbar-inverse .navbar-toggle {
    border-color: transparent !important;
  }

  .navbar {
    margin-bottom: 0;
  }
}

.menu-item-35799 > a {
  border-right: 0 !important;
}

.menu-item-35799 > a:after {
  content: none !important;
}

.logo-title span {
  font-family: "Baloo Chettan 2" !important;
  font-size: 33px;
  color: #0076b6;
  font-weight: 700;
}

.only-desktop span {
  border: 1px #d9e4ea solid;
  border-radius: 5px;
}

.only-desktop i {
  color: #4891b9;
  opacity: 0.8;
}

.logo-title strong {
  display: none;
}

.innerContainer {
  max-width: 1286px;
  margin: auto;
  overflow: hidden;
  padding: 0 5px;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

#infoBlock {
  background-image: linear-gradient(
    to right top,
    #0d47a1,
    #174ba3,
    #1e4fa5,
    #2553a6,
    #2b57a8,
    #1d61ae,
    #0d6bb2,
    #0075b6,
    #0086b8,
    #0095b5,
    #1ca2af,
    #4eaea8
  );
}

.sub-menu li:last-child a {
  padding-bottom: 10px;
  border-bottom: 3px solid #ccc;
}

.logo-mobile {
  display: none;
}

@media all and (max-width: 650px) {

footer .footerCols {
    padding-right: 0;
}

  header #topbar .facebook {
    margin-right: 6px;
  }

  #search-btn {
    color: #e0eff6;
    border: none;
  }

  header #topbar .contact {
    padding: 0;
  }

  .footerCols .textwidget {
    padding: 10px;
  }

  header #topbar .contact .telephone {
    display: none !important;
  }

  .contact .telephone {
    display: none;
  }
}

@media screen and (max-width: 1024px) {
  header #topbar .user {
    line-height: 20px;
    display: none;
    float: left;
    font-size: 16.68px;
    color: #c82020;
  }

  header #topbar .contact .telephone,
  #menu {
    display: none;
  }

  .logo-mobile {
    display: block !important;
    float: left;
  }

  .logo-mobile .logo-title span {
    font-size: 27px !important;
  }
}

@media screen and (max-width: 768px) {
  .only-desktop {
    display: none;
  }

  .post-template-full-width .related-main > div {
    padding: 0 !important;
    margin: 0 !important;
    padding-left: 0 !important;
  }
.print{display: none}

}

#infoBlock .cell {
  float: left;
}

.logo-mobile span {
  font-family: Montserrat;
}

@media print {
  .menu-item-41160,
  #topbar .user,
  #linkzalo,
  #hotline,
  .footertop,
  .post_info,
  #topbar .contact,
  .logo-title,
  .logo {
    display: none !important;
  }

  .logo,
  .menu {
    float: left;
  }
}

/* Các quy tắc chung cho nội dung bài viết */
.single-post .post-content,
.post-template-default .post-content,
.single .post-content {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  overflow: visible !important;
  height: auto !important;
  max-height: none !important;
  width: 100% !important;
  max-width: 100% !important;
}

@media (max-width: 767px) {
  .single-post .post-content,
  .post-template-default .post-content,
  .single .post-content,
  article,
  .main-single-post,
  .main-single-page {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    overflow: visible !important;
    width: 100% !important;
    padding-left: 0px !important;
    padding-right: 0px !important;
  }

  .container-fluid {
    width: 100% !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
    overflow: visible !important;
  }

  img,
  .wp-post-image,
  .attachment-post-thumbnail {
    max-width: 100% !important;
    height: auto !important;
  }

  p,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  ul,
  ol,
  blockquote,
  figure,
  table {
    width: 100% !important;
    max-width: 100% !important;
    overflow-wrap: break-word !important;
    word-break: break-word !important;
  }

  .d-none,
  .d-md-block,
  .d-lg-block,
  .hidden-xs,
  .hidden-sm,
  .hidden-md,
  .visible-lg,
  .visible-md,
  .visible-sm,
  .visible-xs {
    display: block !important;
  }

  .entry-content,
  .post-body,
  .content-area,
  .site-content {
    display: block !important;
    visibility: visible !important;
  }

  .position-static,
  .position-relative,
  .position-absolute,
  .position-fixed,
  .position-sticky {
    position: static !important;
  }
}

.breadcrumb-item + .breadcrumb-item::before {
  float: left;
  padding-right: 0.5rem;
  color: #6c757d;
  content: "/";
  padding-left: 0.5rem;
}

.single-post .single_info_author {
  margin-top: 7px;
      display: flex
}

pre {
  word-wrap: break-word;
  white-space: pre-wrap;
}

.copy-button {
  position: absolute;
  top: 1px;
  right: 3px;
  cursor: pointer;
  color: #000000;
  background: #9ba8ba;
  padding: 1px 8px;
  font-size: 11px;
}

.copy-button:hover {
  color: #000;
}

.under-single-post .label {
  padding: 0;
  font-size: 16px;
  color: #111;
}

.copy-text {
    margin-bottom: 10px;
    margin-top: 10px;
    position: relative;
    padding: 0;
    background: #fff;
    border-radius: 5px;
    display: flex
}

.copy-text input.text {
  padding: 10px;
  font-size: 16px;
  color: #555;
  outline: none;
  border: 1px solid #1976d242;
}

.copy-text button {
  padding: 10px;
  background: #5784f5;
  color: #fff;
  font-size: 16px;
  border: none;
  outline: none;
  border-radius: 5px;
  cursor: pointer;
  margin-left: 6px;
}

.copy-text button:active {
  background: #809ce2;
}

.copy-text button:before {
  content: "Copied";
  position: absolute;
  top: -45px;
  right: 0px;
  background: #5c81dc;
  padding: 8px 10px;
  border-radius: 20px;
  font-size: 15px;
  display: none;
}

.copy-text button:after {
  content: "";
  position: absolute;
  top: -20px;
  right: 25px;
  width: 10px;
  height: 10px;
  background: #5c81dc;
  transform: rotate(45deg);
  display: none;
}

.under-single-post .da-cp {
  display: none;
}

.under-single-post .active .da-cp {
  display: block;
  position: absolute;
  top: -14px;
  right: 45px;
  color: #fcb600;
}

/* Cho trình duyệt Webkit (Chrome, Safari, Edge mới) */
.single-post ol.breadcrumb::-webkit-scrollbar {
  height: 1px;
 /* Chiều cao của thanh cuộn */;
}

.single-post ol.breadcrumb::-webkit-scrollbar-track {
  background: transparent;
 /* Nền của thanh cuộn */;
}

.single-post ol.breadcrumb::-webkit-scrollbar-thumb {
  border-radius: 20px;
 /* Bo tròn thanh cuộn */;
}

/*Header custom */

.vts-describe ul {
  list-style: none;
  list-style-type: disc;
  margin-left: 20px;
  font-size: 18px;
  line-height: 27px;
  color: #43627f;
}

.mobile-active {
  display: block;
}

.icon-active:after {
  font-family: "FontAwesome";
  text-decoration: inherit;
  font-size: 22px;
  float: right;
  content: "\f068" !important;
  font-weight: 400;
}

.vts-group {
  clear: both;
}

.pswp {
  display: none;
}

.logo-title span {
  font-family: "Baloo Chettan 2" !important;
  font-size: 33px;
  color: #0076b6;
  font-weight: 700;
}

.home-hero-image img {
  object-fit: cover;
  position: relative;
  width: 100vw;
}

.only-desktop span {
  border: 1px #d9e4ea solid;
  border-radius: 5px;
}

.only-desktop i {
  color: #4891b9;
  opacity: 0.8;
}

@keyframes animate {
  0% {
    background-position: -500%;
  }

  100% {
    background-position: 500%;
  }
}

.archive-product .title span {
  font-weight: 700;
  font-size: 14.82px;
  color: #333;
}

.archive-product .title {
  display: block;
  overflow: hidden;
  background: #ffffff;
  color: #333;
  border: 1px #fbfcfc solid;
  font-weight: 700;
  border-radius: 4px;
  height: 46px;
  line-height: 48px;
  -webkit-box-shadow: 0 1px 3px rgba(17, 21, 25, 0.42);
  -moz-box-shadow: 0 1px 3px rgba(17, 21, 25, 0.42);
  box-shadow: 0 1px 3px rgb(222, 220, 220);
  text-transform: uppercase;
  margin: 0px;
  margin-bottom: 2%;
  padding: 0 14px;
}

.bank-right h3 {
  padding: 10px;
}

.vts-pd {
  padding: 3%;
}

.meta-blog .archive-date {
  display: none;
}

.footerCols .desc {
  line-height: 24px;
  font-size: 15px;
  border-radius: 6px;
  color: #fff;
  margin: 10px 0;
  margin-top: 4%;
  padding: 8px;
}

#content .theme-list .item .thumb {
  min-height: 279px;
}

.vtsSocial a span {
  display: none;
}

.logo-title strong {
  display: none;
}

.vts-single-content {
  clear: both;
}

#menu > .container-fluid > .innerContainer {
  overflow: unset !important;
  float: left;
}

.EmbedContainer {
  padding-bottom: 52.25%;
  position: relative;
  height: 0;
}

.EmbedContainer iframe,
.EmbedContainer embed,
.EmbedContainer object,
.EmbedContainer video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.single-post .main-content .title {
  display: block;
  overflow: hidden;
  background: transparent;
  color: #333;
  font-weight: 400;
  border-radius: 0;
  height: auto;
  line-height: 24px;
  -webkit-box-shadow: none;
  -moz-box-shadow: no;
  box-shadow: none;
  text-transform: capitalize;
  margin: 0;
  font-size: 15px;
  padding: 0;
  padding-top: 10px;
}

#form-bao-gia .fade:not(.show),
#form-yeu-cau .fade:not(.show) {
  opacity: 1;
}

.innerContainer {
  max-width: 1286px;
  margin: auto;
  overflow: hidden;
  padding: 0 5px;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

.theme-list img {
  max-width: 100%;
}

ul.faq {
  margin-bottom: 2%;
}

#infoBlock {
  background-image: linear-gradient(
    to right top,
    #0d47a1,
    #174ba3,
    #1e4fa5,
    #2553a6,
    #2b57a8,
    #1d61ae,
    #0d6bb2,
    #0075b6,
    #0086b8,
    #0095b5,
    #1ca2af,
    #4eaea8
  );
}

.sub-menu li:last-child a {
  padding-bottom: 10px;
  border-bottom: 3px solid #ccc;
}

body {
  background: #f7f7f7;
  font: 14px "Montserrat", sans-serif;
  color: #2e2e2e;
}

.secondHeading span {
  font-size: 15px;
  text-transform: uppercase;
}

.secondHeading {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px dotted #ddd;
  color: #156594;
  line-height: 1.4em;
}

.secondHeading .icon {
  width: 20px;
  height: 20px;
  float: left;
  margin-right: 5px;
}

#single #related {
  margin-bottom: 20px;
}

.secondHeading .icon.vts-icon {
  background-position: 0 -100px;
}

.vts-tab > .single {
  padding: 10px;
}

.logo-mobile {
  display: none;
}

.footerCols > .row {
  max-width: 100% !important;
}

/*Home page hero*/
.vts-hero {
  background-image: linear-gradient(
    to right bottom,
    #184ca3,
    #1c62ae,
    #3064b0,
    #c1c17f,
    #8ca6b7
  );
}

.vts-hero h2,
.vts-hero h1 {
  font-weight: 200;
  letter-spacing: -1px;
  color: #fff;
  font-family: "Baloo Chettan 2";
  float: left;
  font-size: 42px;
  line-height: 58px;
}

.vts-hero h2 {
  color: #ffffff;
  padding-left: 0;
  clear: both;
  overflow: hidden;
  margin-bottom: 19px;
  font-size: 31px;
  font-weight: bold;
}

.vts-hero .des {
  font-size: 17px;
  line-height: 1.7;
  font-weight: 400;
  padding: 0.7rem 0;
  color: #ffffff;
  overflow: hidden;
  clear: both;
  float: left;
}

.vts-bt {
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
  overflow: hidden;
  float: left;
  background-color: rgb(255, 255, 255);
  color: rgb(0, 119, 204);
  border-color: rgb(27, 169, 245);
}

.vts-bt:hover {
  color: #fff;
  background-color: #255aed;
  border-color: #0062cc;
}

.breadcrumb-btn {
  padding-top: 3%;
}

svg:not(:root) {
  overflow: hidden;
}

.hero-services {
  clear: both;
}

/*Home page cung cap dich vu*/
.img-responsive {
  display: block;
  width: 100%;
  height: auto;
}

.common-title {
  color: #262525;
  font-size: 30px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 30px;
}

.blue-border {
  position: relative;
  padding-bottom: 8px;
}

.common-desc {
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  color: #757575;
  margin-bottom: 0px;
}

.common-padding {
  padding: 30px 0;
}

.services .common-title {
  margin-bottom: 10px;
  font-size: 30px;
}

.services .col-md-3 {
  margin-bottom: 50px;
}

.services .col-wrap {
  position: relative;
  padding: 25px 10px 40px;
  border: 1px solid #c3caff;
  border-radius: 10px;
 /*height:100%;*/
  transition: all 0.3s ease-in-out;
}

.services .img-wrap {
  width: 60px;
  height: 60px;
  margin: 0 auto 15px;
  position: relative;
  top: 0;
  transition: all 0.3s ease-in-out;
}

.services h2 {
  font-size: 16px;
  font-weight: 700;
  color: #262525;
  text-align: center;
}

.services .readmore {
  position: absolute;
  font-weight: 700;
  font-size: 16px;
  display: inline-block;
  border-radius: 8px;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  height: 40px;
  line-height: 40px;
  text-align: center;
  width: 105px;
  background: rgb(0, 117, 219);
  color: #fff;
}

.services .col-wrap:hover {
  background: #d7dcff;
}

.services .col-wrap:hover .img-wrap {
  top: -5px;
}

.services .col-wrap:hover .readmore {
  background-color: #3751ff;
  color: #fff;
}

.back-to-top {
  width: 50px;
  position: fixed;
  bottom: -100px;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  text-decoration: none;
  opacity: 0;
  pointer-events: none;
  transition: all 0.4s ease;
  z-index: 999999;
  background: transparent;
  padding: 10px;
}

.back-to-top svg {
  transform: scale(1);
  height: 40px;
  width: 40px;
  color: #333;
}

.back-to-top.active-to-top {
  bottom: 16px;
  pointer-events: auto;
  opacity: 1;
}

html {
  scroll-behavior: smooth;
}

.back-to-top div:hover {
  animation: backToTop 1s ease-in-out infinite alternate;
}

@keyframes backToTop {
  0% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }

  100% {
    transform: translateY(0);
  }
}

body.custom-background {
  background-color: #ffffff;
  background-image: url("https://vutruso.com/wp-content/uploads/2020/02/curve-banner2.png");
  background-position: left top;
  background-size: auto;
  background-repeat: repeat;
  background-attachment: scroll;
}

.single-post ol.breadcrumb li {
  margin: 0 10px!important;
}

.term-plugin-wordpress .theme-list .item .thumb {
  min-height: unset!important;
}

ol.breadcrumb li {
  list-style-type: none!important;
  float: left;
}

ol.breadcrumb li:first-child {
  margin-left: 0;
  float: left;
}

.single-post ol.breadcrumb {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;    padding-left: 0;
}

.single-post ol.breadcrumb li {
  margin-right: 0;
  position: relative;
  padding: 0 2px;
}

.single-post ol.breadcrumb li {
  margin-right: 0;
  position: relative;
  padding: 0 2px;
}

.single-post .breadcrumb-item + .breadcrumb-item::before {
  float: left;
  padding-right: 0;
  color: #6c757d;
  content: "/";
  padding-left: 0;
  position: absolute;
  top: 0;
  left: -12px;
}

.logo-title {
  display: inline-block;
  min-width: max-content;
  overflow: visible;
}

.logo-title a {
  background: linear-gradient(30deg, #1865b0 0%, #1268b1 25%, #3a0ca3 50%, #4361ee 75%, #4cc9f0 100%);
  background-size: 200% auto;
  color: #0076b6;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
  text-decoration: none;
  white-space: nowrap;
  animation: textclip 2s linear infinite alternate-reverse;
}

.logo-title a span {
  font-family: 'Baloo Chettan 2', cursive !important;
  font-size: 33px;
  font-weight: 700;
}

@keyframes textclip {
  0% {
    background-position: 0% center;
  }

  100% {
    background-position: 200% center;
  }
}

@supports not (-webkit-background-clip: text) {
  .logo-title a {
    color: #0076b6;
    background: none;
  }
}

.single-product .related > h2 {
  text-transform: uppercase;
  border-bottom: 1px solid #f7f7f7;
  font-weight: bold;
  padding-bottom: 10px;
  line-height: 20px;
  font-weight: bold;
  border-bottom: 1px solid #cfcfd3;
  color: #FFF;
  background: #245dac;
  border: none;
  border-radius: 4px;
  box-shadow: 0px 5px 0px #a0b6d478;
  transition: all 0.4s;
  -webkit-transition: all 0.4s;
  -moz-transition: all 0.4s;
  padding: .5em 1em .55em;
  -o-transition: 0.4s;
  font-size: 17px;
  margin-bottom: 20px;
  margin-top: 22px;
  float: left;
  width: 100%;
}

footer .footerCols ul {
  margin-bottom: 0;
}

footer .footerCols .title {
  margin-bottom: 1px!important;
}
/* Hiệu ứng hover cải tiến cho footer */
footer .footerCols .menu a {
  border-radius: 8px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  background: rgba(255,255,255,0.05);
  backdrop-filter: blur(10px);
  text-decoration: none!important;
  width: 99%;
}

footer .footerCols .menu a:hover {
  color: #fff;
  box-shadow: none;
  border-left: 4px solid #4eaea8;
}

/* Hiệu ứng shine */
footer .footerCols .menu a::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
  z-index: 1;
}

footer .footerCols .menu a span {
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

footer .footerCols .menu a:hover span {
  text-shadow: 0 0 8px rgba(255,255,255,0.3);
}

ol.breadcrumb li:first-child {
  float: left;
  margin-left: 0 !important;
  padding-left: 0 !important;
}

.main-single-post>nav,
.vts-single-content>nav {
    border-bottom: 1px solid #c0c0c052;
}

@media all and (max-width:660px) {
  .post-template-default .row {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    max-width: 100%;
  }

  .vutruso_author_box_content {
    display: flex;
    flex-direction: column;
  }

  .related-archive {
    max-width: 93%;
  }

  .single-product .breadcrumb {
    height: 32px;
  }
}

@media screen and (max-width: 540px) {
  .footermenu-main .catMenu ul {
    display: none;
  }

  .footermenu-main .active ul {
    display: block;
  }

  .hero-services a {
    width: 100%;
    text-align: left;
  }
}

.vts-hero .des {
  margin-bottom: 2%;
}



@media all and (max-width: 650px) {
  .vts-theme-wp .maintitle {
    font-size: 18px !important;
    line-height: 18px !important;
    padding-top: 10px !important;
  }

  #content .maintitle span {
    display: block !important;
    margin-top: 5% !important;
    font-size: 21px;
    padding: 10px;
    height: 40px;
  }

  .all_theme1 {
    width: 100% !important;
  }

  h2.section-title {
    font-size: 21px !important;
    text-transform: capitalize !important;
    font-weight: 400 !important;
  }

  .story-block .story-text h3 {
    font-size: 16px !important;
  }

  .text-block h2 {
    font-size: 1.2rem !important;
  }

  .h-let-s-talk-shop,
  .services .common-title {
    font-size: 1.2rem !important;
  }

  .vts-hero h2 {
    font-size: 1.7em;
    display: none;
  }

  .lead-button-bt {
    margin-bottom: 10px;
  }

  .vts-hero .des {
    font-size: 1.2em;
  }

  header #topbar .facebook {
    margin-right: 6px;
  }

  #search-btn {
    color: #e0eff6;
    border: none;
  }

  header #topbar .contact {
    padding: 0;
    width: 100%!important;
  }

  .mobile-show .navbar-push {
    padding: 30px 28px;
  }
}

@media all and (max-width: 480px) {
  header #topbar .contact .telephone, /*#search-btn,*/ .social .fb {
    display: none !important;
  }
}

@media all and (max-width: 360px) {
  .contact .telephone {
    display: none;
  }
}

@media screen and (max-width: 1024px) {
  header #topbar .user {
    line-height: 20px;
    display: none;
    float: left;
    font-size: 16.68px;
    color: #c82020;
  }

  header #topbar .contact .telephone,
  #menu {
    display: none;
  }

  .logo-mobile {
    display: block !important;
    float: left;
  }

  .logo-mobile .logo-title span {
    font-size: 27px !important;
  }
}

@media screen and (max-width: 768px) {
  .only-desktop,
  .single_info_author,
  #home-contacts {
    display: none;
  }

  .bank {
    width: 100%;
    margin: 5px 0;
    max-width: 100%;
    border-radius: 7px;
  }

  .bank-left,
  .bank-right {
    width: 100%;
  }

  .sidebar-single-product,
  #content .buy-vts {
    padding: 0 !important;
    margin-top: 4%;
  }

  .sidebar-single-product {
    margin-top: 1%;
  }

  #popup {
    width: 94%;
  }
}

#infoBlock .cell {
  float: left;
}
/*single*/
.related-post,
.under-single-post {
  overflow: hidden;
}

.single-post .post_info .post_info_contnet > div {
  font-weight: 300;
  letter-spacing: 0.2px;
}

.single-post .post_info {
  color: #6c6e7a;
  width: 100%;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  margin-bottom: 2%;
}

.single-post .post_info .author {
  color: #9c9e9e;
  margin-right: 2%;    
  display: flex;
  align-items: center;
}

.vts-share-buttons {
  float: left;
}

.vts-share-buttons .blog-post-read {
  font-weight: 300 !important;
  font-size: 14px;
  float: right;
}

.post_info_contnet {
  padding-left: 0;
}

.single_info_author span a {
  color: #9c9e9e;
}

table,
tbody,
thead,
tr,
td {
  margin: 0;
  padding: 0;
  border: 0;
  vertical-align: baseline;
  font-size: 100%;
  font-family: inherit;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

table {
  max-width: 100%;
  width: 100%;
  background-color: transparent;
}

td {
  background: #fff;
  padding: 10px 15px;
  border: 1px solid #e4e4e4;
  border-right: 0;
  border-left: 0;
}

tr:nth-child(odd) td {
  background: #fbfcfc;
}

table thead {
  font-weight: bold;
}

.vts-single-content table {
  font-size: 17px;
}

@media (max-width: 1200px) {
  .vts-single-content table {
    display: block;
    overflow-x: auto;
    -webkit-hyphens: none;
    -ms-hyphens: none;
    hyphens: none;
    -webkit-overflow-scrolling: touch;
  }

  .vts-single-content table tbody {
    display: table;
    table-layout: initial !important;
    width: 100%;
  }
}

@media (max-width: 540px) {
  .vts-single-content table {
    font-size: 15px;
  }

  #toc_container {
    margin: 0;
    margin-bottom: 6%;
  }

  .vts-share-buttons {
    float: left;
    margin: 0;
    padding: 6px 0 10px 0;
  }

  .vts-share-buttons .blog-post-read {
    float: left;
  }
}

@media (max-width: 768px) {
  .single-post .main-content .title {
    display: block;
    overflow: hidden;
    background: #fff;
    color: #333;
    border: 1px #fbfcfc solid;
    font-weight: normal;
    border-radius: 4px;
    height: auto;
    line-height: 24px;
    -webkit-box-shadow: none;
    -moz-box-shadow: no;
    box-shadow: none;
    text-transform: capitalize;
    margin: 0;
    margin-bottom: 2%;
    font-size: 15px;
  }

  .main-content .title span {
    display: block;
    line-height: 24px;
    height: auto;
    overflow: hidden;
    font-weight: 700;
    font-size: 14.82px;
    color: #333;
    text-transform: capitalize;
    padding: 0;
  }

  .vts-share-buttons,
  .main-content .title > i {
    display: none;
  }

  .single-post .post_info {
    padding-bottom: 0;
    padding-left: 1%;
  }

  .vts-hero h2,
  .vts-hero h1 {
    font-size: 27px;
  }
}

.logo-mobile span {
  font-family: Montserrat;
}

.main-single-page li {
  list-style-type: disc;
  margin-left: 20px;
  list-style-position: inside;
  line-height: 1.5em;
  margin-bottom: 10px;
  font-size: 15px;
}

footer .footerCols .contact .infoBlock .item.address .icon:before {
  content: "\f279";
  font-family: "FontAwesome";
}

::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #d0e1e4;
}

::-moz-placeholder {
  /* Firefox 19+ */
  color: #d0e1e4;
}

:-ms-input-placeholder {
  /* IE 10+ */
  color: #d0e1e4;
}

:-moz-placeholder {
  /* Firefox 18- */
  color: #d0e1e4;
}

.breadcrumb li::after {
  margin: 0 !important;
}

@media print {
  .vts-single-share,
  .related-post,
  .related-main,
  .menu-item-home,
  #topbar .contact,
  .logo-title,
  .blog-content-sidebar,
  .logo,
  .footerBottom .catMenu,
  #comment,
  #menu-main-menu .menu,
  #wp-nt-vts-wrapper,
  .vts-share-buttons span,
  .footerCols .themes {
    display: none !important;
  }

  .vts-icon,
  .logo,
  .menu {
    float: left;
  }

  footer {
    background: #0051ab;
  }

  .footerCols .contact {
    width: 100%;
  }

  .footerCols,
  footer {
    background: #0051ab;
  }
}



/*Float contact*/
.fcta-zalo-ben-trong-nut svg path {
  fill: #fff;
}

.fcta-zalo-vi-tri-nut {
  position: fixed;
  bottom: 90px;
  left: 20px;
  z-index: 999;
}

.fcta-zalo-nen-nut {
  box-shadow: 0 1px 6px rgba(0, 0, 0, .06), 0 2px 32px rgba(0, 0, 0, .16);
}

.fcta-zalo-nen-nut {
  width: 60px;
  height: 60px;
  text-align: center;
  color: #fff;
  background: #0068ff;
  border-radius: 50%;
  position: relative;
}

.fcta-zalo-nen-nut::after,
.fcta-zalo-nen-nut::before {
  content: "";
  position: absolute;
  border: 1px solid #0068ff;
  background: #0068ff80;
  z-index: -1;
  left: -20px;
  right: -20px;
  top: -20px;
  bottom: -20px;
  border-radius: 50%;
  animation: zoom 1.9s linear infinite;
}

.fcta-zalo-nen-nut::after {
  animation-delay: .4s;
}

.fcta-zalo-ben-trong-nut {
  transition: all 1s;
}

.fcta-zalo-ben-trong-nut {
  position: absolute;
  text-align: center;
  width: 50%;
  height: 70%;
  left: 15px;
  bottom: 19px;
  line-height: 72px;
  font-size: 25px;
  opacity: 1;
}

.fcta-zalo-nen-nut:hover .fcta-zalo-ben-trong-nut,
.fcta-zalo-text {
  opacity: 0;
}

.fcta-zalo-text {
  position: absolute;
  top: 10px;
  text-transform: uppercase;
  font-size: 13px;
  font-weight: 700;
  transform: scaleX(-1);
  transition: all .5s;
  line-height: 1.5;
}

.fcta-zalo-nen-nut:hover .fcta-zalo-text {
  transform: scaleX(1);
  opacity: 1;
}

@media only screen and (max-width:768px) {
  .fcta-zalo-vi-tri-nut {
    bottom: 76px;
  }
}

::selection {
  background: #2a809c;
  color: #fff;
}

@keyframes zoom {
  0% {
    transform: scale(.5);
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  to {
    opacity: 0;
    transform: scale(1);
  }
}

.animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

.bounce {
  -webkit-animation-name: bounce;
  animation-name: bounce;
  -webkit-transform-origin: center bottom;
  transform-origin: center bottom;
}

.animated.infinite {
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}

#hotline strong {
  font-size: 14px;
  line-height: 24px;
  display: block;
}

#hotline span {
  font-size: 11px;
  display: block;
  letter-spacing: 3px;
  line-height: 12px;
}

#hotline a {
  color: inherit;
  display: block;
  position: relative;
  padding: 2px 0px 2px 36px;
}

#hotline .animated {
  border-radius: 50%;
  height: 36px;
  width: 36px;
  line-height: 36px;
  position: absolute;
  top: 4px;
  left: 0px;
  font-size: 34px;
  text-align: center;
  text-shadow: 1px -2px 0px rgb(0 0 0 / 20%);
}

#hotline {
  border-radius: 22px;
  background: #fcb600;
  color: #fff;
  padding: 2px 15px 2px 10px;
  position: fixed;
  bottom: 20px;
  left: 5px;
  z-index: 10;
  box-shadow: 0 1px 6px rgb(0 0 0 / 20%);
}

@media (max-width: 549px) {
  #hotline {
    left: 5px;
    bottom: 10px;
    padding-right: 10px;
  }

  #hotline a {
    padding-left: 32px;
  }

  #hotline .animated {
    font-size: 28px;
    top: 7px;
    height: 28px;
    width: 28px;
    line-height: 28px;
  }

  #hotline span {
    font-size: 10px;
  }

  #hotline strong {
    font-size: 16px;
    line-height: 20px;
  }
}

@keyframes bounce {
  0%, 100%, 20%, 53%, 80% {
    -webkit-transition-timing-function: cubic-bezier(0.215, .61, .355, 1);
    transition-timing-function: cubic-bezier(0.215, .61, .355, 1);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  40%, 43% {
    -webkit-transition-timing-function: cubic-bezier(0.755, .050, .855, .060);
    transition-timing-function: cubic-bezier(0.755, .050, .855, .060);
    -webkit-transform: translate3d(0, -30px, 0);
    transform: translate3d(0, -30px, 0);
  }

  70% {
    -webkit-transition-timing-function: cubic-bezier(0.755, .050, .855, .060);
    transition-timing-function: cubic-bezier(0.755, .050, .855, .060);
    -webkit-transform: translate3d(0, -15px, 0);
    transform: translate3d(0, -15px, 0);
  }

  90% {
    -webkit-transform: translate3d(0, -4px, 0);
    transform: translate3d(0, -4px, 0);
  }
}

.thumb {
  height: 266px;
  overflow: hidden;
  position: relative;
}

.image-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.thumb img {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  transition: top 3s ease;
}

.thumb:hover img {
  top: max(calc(-100% + 266px), -100%);
}

.archive-product {
  max-width: 1300px;
}

#content .theme-list .item .thumb {
  min-height: 439px;
}

.table-content td {
  padding-left: 10px !important;
}



/* Tabs sidebar */
.vts_tabs li {
  display: inline-block;
  margin: 0 0 -1px;
  padding: 15px 30px 10px;
  text-align: center;
  border: 1px solid transparent;
  font-size: 14px;
  font-weight: 600;
  font-style: normal;
  font-stretch: normal;
  line-height: 1.36;
  letter-spacing: .1px;
  color: #373636;
  cursor: pointer;
  width: 48%;
}

.vts_tabs .active {
  border-bottom: 1px solid #4786ff;
  color: #4786ff;
}

.post_sidebar {
  float: left;
}

.blog-sidebar .widget {
  margin: 10px 0;
}

.blog-sidebar .widget:first-child {
  margin-top: 0;
}

.post_sidebar aside {
  margin-bottom: 40px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.posts_index .blog-content-sidebar {
  float: left;
  width: 390px;
  max-width: 100%;
}

.search-thumbnail {
  width: 135px;
  height: 76px;
  border-radius: 5px;
  background-color: #f9faff;
  background-size: cover;
  background-position: center;
  box-sizing: border-box;
  background-repeat: no-repeat;
}

.widget_theme_vtspopulartags {
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 0 5px 20px rgba(229, 230, 232, .4);
  padding: 20px;
  margin-bottom: 60px;
}

.widget_theme_vtspopulartags .widget-title {
  font-size: 24px;
  font-weight: 800;
  text-align: center;
  color: #0076b6;
}

.vts_popular-tags .tags-body {
  font-size: 0;
  margin: -5px;
  padding-top: 20px;
}

.vts_popular-tags .tags-body .tag-item {
  height: 30px;
  display: inline-block;
  background: #4786ff;
  border-radius: 15px;
  padding: 7px 10px;
  box-sizing: border-box;
  line-height: 16px;
  font-size: 14px;
  margin: 4px;
}

.vts_popular-tags .tags-body .tag-item:hover {
  background: #246ffe;
}

.vts_popular-tags .tags-body .tag-item a {
  color: #fff;
}

.widget_theme_vtspoststabs {
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 0 5px 20px rgba(229, 230, 232, .4);
}

.vts_posts-tabs-related_posts {
  border-bottom: 1px solid rgba(0, 0, 0, .05);
  padding: 20px 0;
}

.vts_posts-tabs-related_posts:first-child {
  padding-top: 0;
}

.vts_posts-tabs-related_posts:last-child {
  border-bottom: 0;
  padding-bottom: 0;
}

.tabs-body {
  position: relative;
}

.vts_posts-tabs-related_posts>div {
  display: flex;
}

.vts_posts-tabs-related_posts>div>div {
  width: 100%;
}

.vts_posts-tabs-related_posts div.vts_posts-tabs-footer {
  font-size: 10px;
  font-weight: 400;
  font-style: normal;
  font-stretch: normal;
  line-height: 1.4;
  letter-spacing: .1px;
  text-align: left;
  display: flex;
  margin-top: 4px;
  justify-content: space-between;
}

.vts_posts-tabs-related_posts a.vts_posts-tabs-title {
  font-size: 14px;
  font-weight: 700;
  font-style: normal;
  font-stretch: normal;
  letter-spacing: .1px;
  text-align: left;
  color: #323a45;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  height: 58px;
  text-decoration: underline;
}

.vts_posts-tabs-related_posts div .search-thumbnail {
  margin-right: 15px;
}







@media screen and (max-width:768px) {


.footermenu .catMenu {
    max-height: 0;
    opacity: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out, opacity 0.2s ease-out;
    will-change: max-height, opacity;
    transform: translateZ(0); /* Enable hardware acceleration */
}

.footermenu.active .catMenu {
    max-height: none; /* Will be set by JavaScript */
    opacity: 1;
}

.footermenu .title {
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation; /* Optimize for touch */
    position: relative;
    transition: background-color 0.2s ease;
}

.footermenu .title:hover,
.footermenu .title:focus {
    background-color: rgba(255, 255, 255, 0.1);
}

.footermenu .title:focus {
    outline-offset: 2px;
}

/* Icon indicator cho trạng thái menu */
.footermenu .title::after {
    content: '';
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 8px solid #fff;
    transition: transform 0.3s ease;
    will-change: transform;
}

.footermenu.active .title::after {
    transform: translateY(-50%) rotate(180deg);
}

    .footermenu .title {
        -webkit-tap-highlight-color: rgba(0,0,0,0);
        tap-highlight-color: rgba(0,0,0,0);
    }
    
    .footermenu .catMenu {
        transition: max-height 0.25s ease-out, opacity 0.15s ease-out;
    }

/* Preload animation states */
.footermenu .catMenu ul {
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Reduce paint complexity */
.footermenu .menu a {
    contain: layout style paint;
}

/* Critical rendering optimization */
.footermenu {
    contain: layout;
}

@media (prefers-reduced-motion: reduce) {
    .footermenu .catMenu,
    .footermenu .title::after {
        transition: none;
    }
}


  .search-thumbnail {
    max-width: 100%;
  }

  .post_sidebar aside {
    float: none !important;
    width: 100% !important;
    margin: 0 0 20px !important;
  }

  .widget_theme_vtspopulartags h3 {
    font-size: 20px !important;
    line-height: 27px;
  }

  .vts_tabs .active {
    border-bottom: none;
    color: #4786ff;
  }

  .nav>li>a {
    margin-right: 0;
  }

  .vts_tabs li {
    padding: 15px 10px 10px;
    text-align: left;
  }

  .vts_posts-tabs-related_posts a.vts_posts-tabs-title {
    height: 76px;
  }
}

@media only screen and (max-width:767px) {
  .post_sidebar aside {
    margin: 0 0 20px !important;
    box-shadow: none;
    padding: 20px 0;
  }
.footerCols > .row {
    max-width: 100% !important;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 0 15px;
}
    .footermenu {
        margin-bottom: 15px;
    }

}

