<?php
/*
 * Template Name: Full width
 * Template Post Type: post, page
 */
?>
<?php global $post; ?>
<?php get_header(); ?>
<style>
    pre {
    word-wrap: break-word;
    white-space: pre-wrap;
}
.main-single-post nav {
    margin-bottom: 7px;
}
.page-template-full-width .main-content .title span {
    padding-left: 0;
}
.copy-button {
    position: absolute;
    top: 1px;
    right: 3px;
    cursor: pointer;
    color: #000000;
    background: #9ba8ba;
    padding: 1px 8px;
    font-size: 11px;
    font-size: 11px;
}

.copy-button:hover{
  color: #000;
}

.post-template-full-width #toc_container {
    width: 100%;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
    padding: 0px 20px 10px 20px;
}
.main-content .title {
    border-radius: 8px;
    margin-bottom: 10px;
        margin-top: 0 !important;
    padding-top: 0 !important;
}
.main-single-post ol li {
margin-left: 0;
}
</style>


<div id="content" style="margin: 0 auto; overflow: hidden;margin-top: 0%;padding: 5px 10px;">
    <div class="container">
        <div class="row justify-content-center">
        
            <div class="main-content main-single-post col-lg-10 full-width">

            <?php
                // Get the first category of the post
                $get_cat = get_the_category();
                
                // Check if there is at least one category
                if (!empty($get_cat)) {
                    $first_cat = $get_cat[0];
                    $category_name = $first_cat->cat_name;
                    $category_link = get_category_link( $first_cat->cat_ID );
                } else {
                    // Default values if no category is found
                    $category_name = 'Uncategorized';
                    $category_link = home_url(); // or any fallback URL you prefer
                }
            ?>

            <nav aria-label="breadcrumb">
                <ol class="breadcrumb" itemscope itemtype="https://schema.org/BreadcrumbList">
                    <!-- Home Link -->
                    <li class="breadcrumb-item" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                        <a itemid="https://vutruso.com/" aria-label="Home" id="homepage" itemscope itemtype="http://schema.org/WebPage" itemprop="item" href="<?php echo home_url(); ?>">
                            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5.32927 12.1179V10.3342C5.32927 9.87887 5.70108 9.50976 6.15974 9.50976H7.83634C8.05659 9.50976 8.26783 9.59662 8.42357 9.75123C8.57931 9.90584 8.66681 10.1155 8.66681 10.3342V12.1179C8.66542 12.3072 8.74019 12.4892 8.87453 12.6235C9.00888 12.7579 9.19168 12.8334 9.38237 12.8334H10.5262C11.0604 12.8348 11.5733 12.6251 11.9515 12.2506C12.3297 11.876 12.5423 11.3675 12.5423 10.8371V5.75575C12.5423 5.32735 12.351 4.92099 12.02 4.64614L8.12883 1.561C7.45195 1.02008 6.48213 1.03754 5.82546 1.60249L2.02307 4.64614C1.67642 4.91289 1.46922 5.32045 1.45898 5.75575V10.832C1.45898 11.9373 2.36162 12.8334 3.47509 12.8334H4.59282C4.98887 12.8334 5.31073 12.5162 5.3136 12.123L5.32927 12.1179Z" stroke="#666666" stroke-width="1.5"></path></svg><span itemprop="name"> Home</span>
                        </a>
                        <meta itemprop="position" content="1">
                    </li>

                    <?php if(is_single() && !empty($get_cat)) : ?>
                    <!-- Category Link -->
                    <li class="breadcrumb-item" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                        <a title="Xem tất cả bài viết trong <?php echo esc_html( $category_name ); ?>" itemscope itemtype="http://schema.org/WebPage" itemprop="item" href="<?php echo esc_url( $category_link ); ?>"  itemid="<?php echo esc_url( $category_link ); ?>" id="category-<?php echo esc_attr( $first_cat->cat_ID ); ?>">
                            <span itemprop="name"><?php echo esc_html( $category_name ); ?></span>
                        </a>
                        <meta itemprop="position" content="2">
                    </li>
                    <?php endif; ?>
                    
                    <!-- Current Post Title -->
                    <li class="breadcrumb-item active" aria-current="page" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                        <span itemprop="name"><?php the_title(); ?></span>
                        <meta itemprop="position" content="<?php echo is_single() && !empty($get_cat) ? '3' : '2'; ?>">
                    </li>
                </ol>
            </nav>



                <h1 itemprop="headline" class="title title-0 title-1">
                    <span itemprop="name"><?php the_title(); ?></span>
                </h1>

                <script type="application/ld+json">
                {
                    "@context": "http://schema.org/",
                    "@type": "NewsArticle",
                    "publisher": {
                        "@type": "Organization",
                        "name": "Vũ Trụ Số",
                        "logo": {
                            "@type": "ImageObject",
                            "url": "https://vutruso.com/wp-content/uploads/2022/05/logo.png",
                            "width": 172,
                            "height": 172
                        }
                    },
                    "author": {
                        "@type": "Person",
                        "name": "<?php the_author(); ?>",
                        "url": "<?php echo get_author_posts_url(get_the_author_meta('ID')); ?>"
                    },
                    "datePublished": "<?php echo get_the_date('d-m-Y'); ?>",
                    "headline": "<?php the_title(); ?>",
                    "dateModified": "<?php echo get_the_modified_date('c'); ?>",
                    "description": "<?php echo get_the_excerpt(); ?>",
                    "mainEntityOfPage": "<?php the_permalink(); ?>",
                    "image": [
                        "<?php if ( has_post_thumbnail() ) { echo get_the_post_thumbnail_url(get_the_ID(), 'full'); } ?>"
                    ]
                }
                </script>


                <?php if(is_single()) : ?>
                    

                    <div class="post_info clear col-lg-12 col-sm-12" style="padding-left: 0; ">
                        <div class="single_info_author">

                            <span class="category author">
                            <svg width="14" height="14" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6"> <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12.75V12A2.25 2.25 0 0 1 4.5 9.75h15A2.25 2.25 0 0 1 21.75 12v.75m-8.69-6.44-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z" /> </svg>

                                <?php
                                    // $get_cat        = get_the_category();
                                    // $first_cat      = $get_cat[0];
                                    // $category_name  = $first_cat->cat_name;
                                    // $category_link  = get_category_link( $first_cat->cat_ID ); 


                                    $published_date = get_the_date('d/m/Y');
                                    $modified_date = get_the_modified_date('d/m/Y');
                                    
                                ?>
                                <a href="<?php echo esc_url( $category_link ); ?>"
                                    title="<?php echo esc_attr( $category_name ); ?>">
                                    <?php echo esc_html( $category_name ); ?>
                                </a>
                            </span>

                            <span class="author">
                            <svg width="14" height="14" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z" />
</svg>

                                <?php echo $published_date; ?>
                            </span>

                            <?php if ($modified_date !== $published_date) : ?>
                            <span class="author">
                            <svg width="14" height="14" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99" />
</svg>

                                <?php echo $modified_date; ?>
                            </span>
                            <?php endif; ?>

                            <span class="print">
                            <svg width="14" height="14" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M6.72 13.829c-.24.03-.48.062-.72.096m.72-.096a42.415 42.415 0 0 1 10.56 0m-10.56 0L6.34 18m10.94-4.171c.24.03.48.062.72.096m-.72-.096L17.66 18m0 0 .229 2.523a1.125 1.125 0 0 1-1.12 1.227H7.231c-.662 0-1.18-.568-1.12-1.227L6.34 18m11.318 0h1.091A2.25 2.25 0 0 0 21 15.75V9.456c0-1.081-.768-2.015-1.837-2.175a48.055 48.055 0 0 0-1.913-.247M6.34 18H5.25A2.25 2.25 0 0 1 3 15.75V9.456c0-1.081.768-2.015 1.837-2.175a48.041 48.041 0 0 1 1.913-.247m10.5 0a48.536 48.536 0 0 0-10.5 0m10.5 0V3.375c0-.621-.504-1.125-1.125-1.125h-8.25c-.621 0-1.125.504-1.125 1.125v3.659M18 10.5h.008v.008H18V10.5Zm-3 0h.008v.008H15V10.5Z" />
</svg>

                                <a style="font-weight: normal;" class="print-link" href="javascript:window.print()"> In
                                    trang này</a>
                            </span>

                        </div>

                        <!-- <div class="vts-share-buttons col-lg-2 col-sm-12">
                            <span class="blog-post-read"><i class="fa fa-line-chart" aria-hidden="true"></i>
                                <?php //vts_setPostViews(get_the_ID()); ?>
                                <?php //echo vts_getPostViews(get_the_ID()); ?>
                            </span>
                        </div> -->

                    </div>
                    <!--end post_info-->
                <!--end post_info-->
                <?php endif; ?>

                <div class="vts-single-content">

                    <?php echo do_shortcode('[vts_toc]'); ?>

                    <?php if(have_posts()) : while(have_posts()) : the_post() ?>
                    <?php the_content(); ?>
                    <?php endwhile; ?>
                    <?php endif; ?>




                    <?php if(is_single()){?>




                    <div class="under-single-post">

                        <?php if(has_tag()){ ?>
                        <div class="tags" itemprop="keywords ">
                            <strong><?php echo _e('Từ khóa', 'vutruso'); ?></strong>
                            <?php the_tags('<mark itemprop="keywords">', '</mark><mark itemprop="keywords">' , '</mark>'); ?>
                        </div>
                        <?php } ?>

                    </div><!-- end under-single-post -->

                    <?php 
                    $fb = get_the_author_meta('facebook'); 
                    $twitter= get_the_author_meta('twitter'); 
                     $linkedin= get_the_author_meta('linkedin'); 
                     $github= get_the_author_meta('github'); 
                    $pinterest= get_the_author_meta('pinterest'); 


                    $datas[] = $fb;
                    $datas[] = 'https://twitter.com/'.$twitter;
                    $datas[] = $linkedin;
                    $datas[] = $github;
                    $datas[] = $pinterest;

                    $data_clean = array_filter($datas);  

                    ?>

                    <div class="vutruso_author_box">
                        <div class="vutruso_author_box_content">
                            <div class="vutruso_author_box_avatar">
                                <?php echo get_avatar( get_the_author_meta( 'ID' ), 70 ); ?>
                            </div>

                            <?php $twitter= get_the_author_meta('twitter'); ?>
                            <?php $linkedin= get_the_author_meta('linkedin'); ?>
                            <?php $github= get_the_author_meta('github'); ?>

                            <div class="vutruso_author_box_meta">
                                <div class="vutruso_author_box_name">
                                    <?php the_author_posts_link(); ?>
                                    <div class="vutruso_author_social_links">

                                        <?php if($twitter): ?>
                                        <a aria-label="twitter" rel="external nofollow noopener" target="_blank" class="twitter"
                                            href="https://www.twitter.com/<?php echo $twitter; ?>">
                                            <i class="fa fa-twitter"></i>
                                        </a>
                                        <?php endif; ?>

                                        <?php if($linkedin): ?>
                                        <a aria-label="linkedin" rel="external nofollow noopener" target="_blank" class="linkedin"
                                            href="<?php echo $linkedin; ?>">
                                            <i class="fa fa-linkedin" aria-hidden="true"></i>
                                        </a>
                                        <?php endif; ?>

                                        <?php if($github): ?>
                                        <a aria-label="github" rel="external nofollow noopener" target="_blank" class="github"
                                            href="<?php echo $github; ?>">
                                            <i class="fa fa-github"></i>
                                        </a>
                                        <?php endif; ?>

                                    </div>
                                </div>
                                <p><?php 
                                            $authorDesc = the_author_meta('description'); 
                                            echo $authorDesc; 
                                            ?>
                                </p>

                            </div>
                        </div>
                    </div>

                    <?php $mota = get_the_author_meta('description'); ?>
                    <script type="application/ld+json">
                    { 
                    "@context": "https://schema.org/",
                    "@type": "Person", "name": "<?php the_author(); ?>",
                    "image": { "@type": "ImageObject", 
                    "url": "<?php $user = wp_get_current_user(); if ( $user ) : echo esc_url( get_avatar_url( $user->ID ) ); endif; ?>", "height": "600", "width": "600" }, 
                    "url": "<?php echo esc_url( get_author_posts_url( get_the_author_meta( 'ID' ) ) ); ?>",
                    "jobTitle": "<?php echo get_the_author_meta('jobTitle'); ?>",
                    "email": "<?php echo get_the_author_meta('user_email'); ?>",
                    "description": "<?php echo preg_replace("/\r|\n/", "", $mota); ?>",
                    "gender": "<?php echo get_the_author_meta('s_box'); ?>",
                    "address": "<?php echo get_the_author_meta('diachi'); ?>",
                    "birthDate": "<?php echo get_the_author_meta('namsinh'); ?>",
                    "birthPlace": "<?php echo get_the_author_meta('birthPlace'); ?>",
                    "sameAs": [<?php $last_key = array_key_last($data_clean);
                                    foreach ($data_clean as $key => $value) { if ($key == $last_key) { echo "\"$value\""; } else { echo "\"$value\","; } } ?>],
                    "knowsLanguage": [ "vi-VN", "en-US" ] }
                    </script>




                    <?php } ?>
                </div><!-- end vts-single-content -->


            </div /><!-- col-lg-10 justify-content-center-->

                    <div class="related-post">
                        <?php 
                        $related = vts_related_posts( get_the_ID(), 8 );
                        if ( $related->have_posts() ) : ?>

                        <div class="secondHeading">
                            <div class="icon vts-icon"><i class="fa fa-random" aria-hidden="true"></i></div>
                            <span><?php echo __('Bài viết liên quan', 'vutruso') ?></span>
                        </div>

                        <div class="related-main row" style="padding: 0 15px;">
                            <?php while ( $related->have_posts() ) : $related->the_post(); ?>

                            <div class="col-lg-3 col-md-6 col-sm-6">
                                <article class="blog-archive related-archive">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php if(has_post_thumbnail()) { ?>
                                        <?php the_post_thumbnail('archive_thumb', array('title' => get_the_title(), 'alt' => get_the_title(), 'itemprop' => 'image')); ?><?php } else { ?>
                                        <img height="340"
                                            src="<?php echo get_template_directory_uri(); ?>/img/nothumb.jpg"
                                            alt="<?php the_title(); ?>" />
                                        <?php } ?>
                                    </a>

                                    <div class="content related-singlepost">
                                        <div class="related-post-content">
                                            <div class="meta-blog">
                                                <div class="cat-blog">
                                                    <?php
                                                            $get_cat        = get_the_category();
                                                            $first_cat      = $get_cat[0];
                                                            $category_name  = $first_cat->cat_name;
                                                            $category_link  = get_category_link( $first_cat->cat_ID ); ?>
                                                    <a href="<?php echo esc_url( $category_link ); ?>"
                                                        title="<?php echo esc_attr( $category_name ); ?>">
                                                        <?php echo esc_html( $category_name ); ?>
                                                    </a>
                                                </div>
                                                <span><?php //the_date('d-m-Y'); ?></span>
                                            </div>
                                            <h2 class="title-blog">
                                                <a title="<?php the_title(); ?>"
                                                    href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                            </h2>
                                        </div>
                                    </div>

                                </article>
                            </div><!-- end loop -->

                            <?php endwhile; ?>
                            <?php wp_reset_postdata(); ?>
                            <?php endif; ?>
                        </div><!-- related-main -->
                    </div /><!-- end related post -->
            
        </div><!-- justify-content-center-->

    </div>
</div>

<script type="text/javascript">
document.addEventListener("DOMContentLoaded", function() {
  const preTags = document.querySelectorAll('pre');

  preTags.forEach(function(preTag) {
    // Create a copy button element
    const copyButton = document.createElement("span");
    copyButton.innerText = "Copy";
    copyButton.classList.add("copy-button");
    copyButton.style.cursor = "pointer"; // Added for better UX to show it's clickable

    // Append the copy button to the <pre> tag
    preTag.appendChild(copyButton);

    // Add click event listener to the copy button
    copyButton.addEventListener("click", () => {
      // Create a range and select the text inside the <pre> tag
      const range = document.createRange();
      range.selectNodeContents(preTag);

      // Modify range to exclude the copy button
      range.setEnd(preTag, preTag.childNodes.length - 1);

      window.getSelection().removeAllRanges();
      window.getSelection().addRange(range);

      try {
        // Copy the selected text to the clipboard
        const successful = document.execCommand("copy");
        if (successful) {
          // Alert the user that the text has been copied
          copyButton.innerText = "Copied!";
          setTimeout(function() {
            copyButton.innerText = "Copy";
          }, 2000);
        } else {
          throw new Error('Copy command was unsuccessful.');
        }
      } catch (err) {
        console.error("Unable to copy text:", err);
        copyButton.innerText = "Copy";
      } finally {
        // Deselect the text
        window.getSelection().removeAllRanges();
      }
    });
  });
});
</script>



<?php get_footer(); ?>