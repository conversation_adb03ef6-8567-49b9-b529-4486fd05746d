<?php
/**
 * VTS SEO Schema
 * 
 * Schema markup and SEO optimizations
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Note: vts_add_itemprop_menu() is already defined in core/menus.php

/**
 * Add itemprop="image" to img tags
 */
function vts_add_itemprop_image_markup($content) {
    // Replace the instance with the itemprop image markup
    $string = '<img';
    $replace = '<img itemprop="image"';
    $content = str_replace($string, $replace, $content);
    return $content;
}
add_filter('the_content', 'vts_add_itemprop_image_markup', 2);

/**
 * Add itemprop="url" to anchor tags
 */
function vts_add_itemprop_url_markup($content) {
    // Replace the instance with the itemprop url markup
    $string = '<a';
    $replace = '<a itemprop="url"';
    $content = str_replace($string, $replace, $content);
    return $content;
}
add_filter('the_content', 'vts_add_itemprop_url_markup', 2);

/**
 * Add alt tag to WordPress Gravatar images
 */
function vts_gravatar_alt($vtsGravatar) {
    if (have_comments()) {
        $alt = get_comment_author();
    } else {
        $alt = get_the_author_meta('display_name');
    }
    $vtsGravatar = str_replace('alt=\'\'', 'alt=\'Avatar for ' . $alt . '\'', $vtsGravatar);
    return $vtsGravatar;
}
add_filter('get_avatar', 'vts_gravatar_alt');

/**
 * Set SEOPress social OG locale
 */
function vts_social_og_locale($html) { 
    // You can add here all your conditions as if is_page(), is_category() etc.. 
    $html = '<meta property="og:locale" content="vi_VN" />';
    return $html;
}
add_filter('seopress_social_og_locale', 'vts_social_og_locale');

/**
 * Add canonical tags for paged content
 */
function vts_add_canonical_tags() {
    if (is_paged()) {
        $canonical = get_pagenum_link(1);
        echo '<link rel="canonical" href="' . esc_url($canonical) . '" />';
    }
}
add_action('wp_head', 'vts_add_canonical_tags');
