<?php get_header(); ?>
<div class="innerContainer">
  <section class="news posts-grid layout-medium" style="margin-top: 2%;">
    <div class="text-left">
        <h1 class="title-search">
          <i class="fa fa-tags" aria-hidden="true"></i> 
            <PERSON>h sách bài viết của tác giả: <strong><?php echo $display_name = get_the_author_meta('display_name');?></strong>
        </h1>
        <?php
            $author_id = get_the_author_meta('ID');
            $author_name = get_the_author_meta('display_name', $author_id);
            $author_description = get_the_author_meta('description', $author_id);
            $author_avatar = get_avatar($author_id, 70); // 96 is the size of the avatar

            // Get social media URLs
            $author_facebook = get_user_meta($author_id, 'facebook', true);
            $author_twitter = get_user_meta($author_id, 'twitter', true);
            $author_linkedin = get_user_meta($author_id, 'linkedin', true);

        ?>
    </div>



<div class="user-profil-area">
  <div class="profil-header" style="background: linear-gradient(#BD1E36 0%, #FF0A37 100%);">
    <div class="profil-avatar">
      <div class="user-avatar-userpanel">
        <?php echo $author_avatar; ?>
      </div>
      <div class="profil-name">
        <div class="profil-name-content">
          <div class="user-insidename">
            <?php echo $author_name; ?>
          </div>
          <span class="user-statu">Đã xác thực</span>
        </div>
      </div>
      <div class="profil-state"><?php echo $author_description; ?></div>
      <!-- <div class="profil-state" style="margin-top:7px;"> Số bài viết: 419</div> -->
    </div>
  </div>
  <div class="profil-bottom-stick">
    <ul>
      <li>
        <a href="<?php echo $author_facebook; ?>">
          <i class="fa fa-facebook-f"></i> Facebook</a>
      </li>
      <li><a href="https://www.linkedin.com/company/vutruso/"><i class="fa fa-linkedin"></i> Linkedin</a></li>
    </ul>
  </div>
</div>


<style>
.user-profil-area{width:100%;display:inline-block;box-shadow:0px 0px 115px -10px rgba(169,169,169,.32);border-bottom-left-radius:6px;border-bottom-right-radius:6px;margin-bottom:20px;}
.profil-header{background:linear-gradient(to bottom,#6606bd 0%,#5a93ff 100%);width:100%;display:inline-block;position:relative;height:160px;border-top-left-radius:6px;border-top-right-radius:6px;}
.user-avatar-userpanel{displaY:inline-block;float:left;position:relative;}
.profil-avatar{display:inline-block;border-radius:50%;position:absolute;bottom:-52px;left:20px;z-index:4;}
.profil-avatar img{width:188px;height:188px;border-radius:50%;float:left;margin-right:20px;box-shadow:-10px 8px 70px 18px rgba(0,0,0,.1);}
.profil-name{display:inline-block;color:#fff;font-family:'Open Sans',sans-serif;font-size:25px;font-weight:700;margin-top:20px;}
.user-insidename{display:inline-block;float:left;}
.user-statu{font-size:9px;padding:8px;padding-top:5px;padding-bottom:5px;background-color:rgb(197 255 0/51%);text-transform:uppercase;border-radius:4px;display:inline-block;float:left;margin-top:7px;margin-left:10px;color:rgba(255,255,255,.64);}
.profil-state {
    color: rgb(255 255 255);
    line-height: 20px;
    font-size: 14px;    padding-right: 15px;
}
.profil-bottom-stick{width:100%;background-color:#fff;display:inline-block;position:relative;}
.profil-bottom-stick ul{margin:0;padding:0;float:right;width:calc(100% - 218px);margin-top:-2px;}
.profil-bottom-stick ul li{float:left;margin:0;padding:0;list-style:none;display:inline-block;position:relative;z-index:5;}
.profil-bottom-stick ul li a{padding:10px;display:inline-block;font-size:13px;color:#444;text-decoration:none;}
.profil-bottom-stick ul li a i{width:25px;height:25px;line-height:23px;text-align:center;border:1px solid #dfdfdf;border-radius:50%;margin-right:5px;}
.profil-bottom-stick ul li:nth-child(1) a i{color:#4363a2;border:1px solid #4363a2;}
.profil-bottom-stick ul li:nth-child(2) a i{color:#60b5f1;border:1px solid #60b5f1;}
.profil-bottom-stick ul li:nth-child(3) a i{color:#dd4b39;border:1px solid #dd4b39;}
.profil-bottom-stick ul li:nth-child(4) a i{color:#007bb6;border:1px solid #007bb6;}
.profil-bottom-stick ul:after{background:linear-gradient(to right,rgba(16,16,16,0) 0%,#101010 50%,#101010 100%);}

@media screen and (max-width:800px){
  .profil-header{height:auto;display:inline-block;width:100%;text-align:center;padding-top:20px;}
  .profil-avatar{position:relative;bottom:auto;top:auto;width:100%;left:auto;text-align:center;}
  .profil-avatar img{margin:0;}
  .user-avatar-userpanel{display:inline-block;float:inherit;}
  .profil-name{width:100%;text-align:center;}
  .profil-name-content{display:inline-block;}
  .profil-bottom-stick{text-align:center;}
  .profil-bottom-stick ul{float:inherit;width:auto;display:inline-block;margin-top:5px;}
}


@media screen and (max-width:600px){
  .profil-state {
      padding: 10px 20px;
      text-align: justify;
  }
  .profil-avatar img {;
        width: 150px;
        height: 150px;
    }


}

  </style>

    <?php while(have_posts()) : the_post(); ?>
    <article <?php post_class('item post-item'); ?>>
      <div class="post-wrapper">
        <div class="post-media post-grid-media">
          <a href="<?php the_permalink(); ?>">
            <?php if(has_post_thumbnail()){ the_post_thumbnail('medium', array('class'=>'pic', 'title' => get_the_title(), 'alt' => get_the_title(), 'itemprop' => 'image')); } else { ?>
                  <img height="340" src="<?php echo get_template_directory_uri(); ?>/img/nothumb-rc_big.jpg" alt="<?php the_title(); ?>" />
            <?php } ?>
          </a>
        </div>
        <div class="post-info">
          <h3 class="post-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
          <div class="post-content"><p class="excerpt"><?php echo get_the_excerpt(); ?></p></div>
          <div class="post-info-footer">
            <div class="post-meta-wrapper">
              <div class="post-meta">
                <div class="post-meta-item post-author">
                    <span class="post-author-avatar"><?php echo get_avatar( get_the_author_meta( 'ID' ), 64 ); ?></span>
                    <span class="post-author-name"><?php the_author(); ?></span>                      
                </div>
              </div>
              <div class="post-info-footer-divider"></div>
            </div>
            <div class="read-more-wrapper"><a href="<?php the_permalink(); ?>" class="read-more">Đọc chi tiết</a></div>
          </div>
        </div>

    </article>
    <?php endwhile; ?><?php vts_page_navi(); ?>

		

      </section>
		</div>

<style>
.post-author .post-author-avatar{
  width: 60px;
    height: 60px;
    z-index: 0;
    float: left;
    margin-right: 20px;position: relative;
    text-align: right;
    display: inline-block;
}
.post-meta-wrapper .post-author .post-author-avatar img {
  -webkit-transition: all .8s ease-in-out;
    -moz-transition: all .8s ease-in-out;
    transition: all .8s ease-in-out;
    -webkit-clip-path: polygon(50% 0%,100% 50%,50% 100%,0% 50%);
    clip-path: polygon(50% 0%,100% 50%,50% 100%,0% 50%);
    border: 1px solid transparent;
    color: #fff;
    background-color: #adb5bd;
    align-items: center;
    justify-content: center;
    width: 64px;
    height: 64px;
    position: relative;
    text-align: right;
    display: inline-block;
}
.post-author .post-author-avatar:after {
    width: 79%;
    height: 79%;
    content: "";
    position: absolute;
    top: 8px;
    left: 2px;
    transform: rotate(45deg);
    z-index: -1;
    border-radius: 4px;
    border: 2px solid #ff00009c;
}


  header #menu .menu ul li ul {
    display: none;
    position: absolute;
    top: 55px;
    left: -41px;
    width: 400px;
    transition: all .3s;
    z-index: 999;
}
  .title-search{
    font-weight: 700;
    border-radius: 4px;
    line-height: 24px;
    padding-left: 17px;
    -webkit-box-shadow: 0 1px 15px rgba(17,21,25,.42);
    -moz-box-shadow: 0 1px 15px rgba(17,21,25,.42);
    box-shadow: 0 1px 15px rgba(17,21,25,.42);
    text-transform: uppercase;
    margin-bottom: 1%;
    padding: 5px 10px;
    font-size: 13px;
  }

  .page-navigation{margin: 10px 0;display: block;overflow: hidden;}
  .pagination li{float:left}
  .pagination .current, .pagination li a {
    color: #fff;
    display: block;
    float: left;
    padding: 1px 16px !important;
    border-radius: 2px;
    text-align: center;
    margin: 0 5px 3px 0 !important;
    transition: all .3s;
    font-weight: 700;
    border: 0 !important;
    background: #227cd4;
    font-size: 18px;
  }

.pagination .current {background: #4b6f84;border-radius: 0 !important;}
.excerpt {line-height: 20px;font-size: 15px;}
.news.posts-grid .item .post-wrapper .post-info .post-title,
.news.posts-grid .item .post-wrapper .post-info .post-title a,
.news.posts-grid .item .post-wrapper .post-info .read-more-wrapper .read-more,
.news.posts-grid .item .post-wrapper .post-author .post-author-name a {
  color: #142b5f;
  font-family: initial;
  line-height:27px;
}
.post-meta-item:before {background-color: #142b5f;}
.post-meta-wrapper > * a:not(.read-more):before {color: rgba(31, 90, 188, 0.7);}
.news.posts-grid
  .item
  .post-wrapper
  .post-info
  .read-more-wrapper
  .read-more:before {
  color: rgb(64, 166, 255);
}
.layout-medium .item .post-content:after {background-color: rgb(64, 166, 255);}
.post-meta-wrapper .post-author .post-author-avatar img {background-color: rgba(64, 166, 255, 0.25);}
.news.posts-grid
  .item
  .post-wrapper
  .post-info
  .post-info-footer
  .post-info-footer-divider {
  background-color: rgba(26, 57, 127, 0.2);
}
.post-meta-wrapper {
  font-size: 15px;
  line-height: 19px;
  display: -webkit-box;
  display: -moz-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -moz-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-justify-content: normal;
  -moz-justify-content: normal;
  -ms-justify-content: normal;
  justify-content: normal;
  -webkit-align-items: flex-start;
  -moz-align-items: flex-start;
  -ms-align-items: flex-start;
  align-items: flex-start;
}
.post-meta-wrapper .post-meta-item {position: relative;margin: 5px 14px 0 0;}
.post-meta-wrapper .post-meta-item:not(:first-child) {padding: 0 0 0 20px;}
.post-meta-wrapper .post-meta-item:not(:first-child):before {
  content: "";
  position: absolute;
  display: block;
  width: 5px;
  height: 5px;
  -webkit-border-radius: 50%;
  border-radius: 50%;
  left: 0;
  right: auto;
  top: 6px;
  opacity: 0.25;
}
.post-meta-wrapper .post-meta-item a {color: inherit;}
.post-author .post-author-avatar {display: inline-block;vertical-align: top;margin: -8px 15px 0 0;}
.post-author .post-author-avatar a {display: block;}
.post-author .post-author-avatar .avatar {display: block;-webkit-border-radius: 50%;border-radius: 50%;}
.post-author .post-author-name {display: inline-block;vertical-align: top;line-height: 19px;}
.post-author .post-author-name a {line-height: 19px;}
.item .post-wrapper .post-media .pic { line-height: 0; font-size: 0; display: block; margin: 0 auto;}
.item .post-wrapper .post-media .pic img {display: block;}
.news.posts-grid.layout-medium
  .item:not(.related-item)
  .post-wrapper
  .post-media
  .pic
  img {
  -webkit-border-radius: 15px;
  border-radius: 15px;
}
.news.posts-grid.layout-medium
  .item:not(.related-item)
  .post-wrapper
  .post-info {
  position: relative;
}
.news.posts-grid.layout-medium
  .item:not(.related-item)
  .post-wrapper
  .post-info
  .post-title {
  font-size: 24px;
  font-weight: 800;
  word-break: break-word;
}
.news.posts-grid.layout-medium
  .item:not(.related-item)
  .post-wrapper
  .post-info
  .post-content {
  margin-top: 20px;
}
.news.posts-grid.layout-medium
  .item:not(.related-item)
  .post-wrapper
  .post-info
  .post-content:empty {
  margin-top: 0 !important;
}
.news.posts-grid.layout-medium
  .item:not(.related-item)
  .post-wrapper
  .post-info
  .post-info-footer {
  margin: 30px 0 0;
  display: -webkit-box;
  display: -moz-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -moz-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-wrap: nowrap;
  -moz-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-justify-content: space-between;
  -moz-justify-content: space-between;
  -ms-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: flex-start;
  -moz-align-items: flex-start;
  -ms-align-items: flex-start;
  align-items: flex-start;
}
.news.posts-grid.layout-medium
  .item:not(.related-item)
  .post-wrapper
  .post-info
  .post-info-footer
  .post-meta-wrapper {
  display: -webkit-box;
  display: -moz-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -moz-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-wrap: nowrap;
  -moz-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-justify-content: space-between;
  -moz-justify-content: space-between;
  -ms-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: flex-start;
  -moz-align-items: flex-start;
  -ms-align-items: flex-start;
  align-items: flex-start;
  width: 100%;
}
.news.posts-grid.layout-medium
  .item:not(.related-item)
  .post-wrapper
  .post-info
  .post-info-footer
  .post-meta-wrapper
  .post-meta {
  max-width: calc(100% - 70px);
  display: -webkit-box;
  display: -moz-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -moz-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-justify-content: flex-start;
  -moz-justify-content: flex-start;
  -ms-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: flex-start;
  -moz-align-items: flex-start;
  -ms-align-items: flex-start;
  align-items: flex-start;
  -webkit-flex-shrink: 0;
  -moz-flex-shrink: 0;
  -ms-flex-shrink: 0;
  flex-shrink: 0;
}
.news.posts-grid.layout-medium
  .item:not(.related-item)
  .post-wrapper
  .post-info
  .post-info-footer
  .post-meta-wrapper
  .post-info-footer-divider {
  min-width: 30px;
  width: 100%;
  height: 1px;
  margin: 13px 20px 10px 16px;
}
.news.posts-grid.layout-medium
  .item:not(.related-item)
  .post-wrapper
  .post-info
  .post-info-footer
  .read-more-wrapper {
  -webkit-flex-shrink: 0;
  -moz-flex-shrink: 0;
  -ms-flex-shrink: 0;
  flex-shrink: 0;
  margin: 2px 0 0 13px;
  text-align: right;
}
.news.posts-grid.layout-medium
  .item:not(.related-item)
  .post-wrapper
  .post-info
  .post-content:after {
  content: "";
  display: table;
  clear: both;
}
.news.posts-grid.layout-medium .item {
    border-radius: 4px;
    height: auto;
    padding-left: 17px;
    -webkit-box-shadow: 0 1px 15px rgba(17,21,25,.42);
    -moz-box-shadow: 0 1px 15px rgba(17,21,25,.42);
    box-shadow: 0 1px 15px rgba(17,21,25,.42);
    clear: both;
    margin: 1% 0;
}
.news.posts-grid.layout-medium .item .post-wrapper {
  display: -webkit-box;
  display: -moz-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -moz-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-wrap: nowrap;
  -moz-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-justify-content: flex-start;
  -moz-justify-content: flex-start;
  -ms-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  align-items: center;
  -webkit-border-radius: 30px;
  border-radius: 30px;
padding: 20px 9px 20px 6px;
}
.news.posts-grid.layout-medium .item .post-wrapper .post-media {
  padding: 0;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-flex-shrink: 0;
  -moz-flex-shrink: 0;
  -ms-flex-shrink: 0;
  flex-shrink: 0;
  width: 25%;
}
.news.posts-grid.layout-medium .item .post-wrapper .post-info {
  margin: 0;
  width: 100%;
  padding-left: 12px;
}
.news.posts-grid.layout-medium .item .post-wrapper .post-info > *:first-child {
  margin-top: 0;
}



.read-more {
  display: inline-block;
  vertical-align: top;
  position: relative;
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
}
.read-more:before {
  content: "\f061";
  position: relative;
  margin: -1px 12px 0 0;
  font: 400 normal 15px/24px "fontawesome";
  display: inline-block;
  vertical-align: top;
  left: 0;
  -webkit-transition: left 0.3s, color 0.3s;
  transition: left 0.3s, color 0.3s;
}
.pic {
  position: relative;
  font-size: 0px;
  line-height: 0px;
}

@media screen and (min-width: 1367px),
  screen and (min-width: 1200px) and (any-hover: hover),
  screen and (min-width: 1200px) and (min--moz-device-pixel-ratio: 0),
  screen and (min-width: 1200px) and (-ms-high-contrast: none),
  screen and (min-width: 1200px) and (-ms-high-contrast: active) {
  .post-meta-wrapper > * a:not(.read-more):hover,
  .news.posts-grid .item .post-wrapper .post-author .post-author-name a:hover,
  .news.posts-grid
    .item
    .post-wrapper
    .post-info
    .read-more-wrapper
    .read-more:hover:before {
    color: rgb(31, 90, 188);
  }
  .news.posts-grid
    .item
    .post-wrapper
    .post-info
    .read-more-wrapper
    .read-more:hover {
    color: rgb(31, 90, 188) !important;
  }
  a:hover,
  .news.posts-grid .item .post-wrapper .post-info .post-title a:hover,
  .news.posts-grid
    .item
    .post-wrapper
    .post-info
    .read-more-wrapper
    .read-more:hover {
    color: rgb(64, 166, 255);
  }
}

@media screen and (max-width: 767px) {
  .page-navigation{margin: 0;}
  ..post-info .post-info-footer, .post-info-footer{display: none!important}
  ul.pagination {
    margin-bottom: 17px;
    padding-top: 6px;
}
  .news.posts-grid.layout-medium .item:not(.related-item) {
    width: auto;
    margin-bottom: 24px;
  }
  .news.posts-grid.layout-medium .item:not(.related-item) .post-wrapper {
    display: block;
    text-align: left;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-media {
    width: 100%;
    float: none;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-info {
    width: auto;
    margin: 0 !important;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-info
    .post-title {
    font-size: 18px;
    margin: 9px 0 0;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-info
    .post-info-footer {
    display: block;
    margin: 4px 0 0;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-info
    .post-info-footer
    .post-info-footer-divider {
    display: none;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-info
    .post-info-footer
    .post-meta-wrapper {
    max-width: none;
    display: block;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-info
    .post-info-footer
    .post-meta-wrapper
    .post-author
    .post-author-avatar {
    margin: -8px 7px 0 0;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-info
    .post-info-footer
    .post-meta-item {
    display: inline-block;
    vertical-align: top;
    margin: 5px 10px 0 0;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-info
    .post-info-footer
    .post-meta-item:not(:first-child) {
    padding: 0 0 0 14px;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-info
    .post-info-footer
    .read-more-wrapper {
    text-align: right;
    margin: 8px 0 0;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-info
    .post-info-footer
    .read-more-wrapper
    .read-more:before {
    display: none;
  }
  .news.posts-grid.layout-medium .item:not(.related-item) {
    margin: 0 0 24px;
    padding: 0;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-media {
    padding: 0;
  }
  .page-navigation ul{
    margin-left: auto;
    margin-right: auto;
    clear: both;
    width: 50%;
    display: block;
    overflow: hidden;
  }
  .page-navigation{
    margin-left: auto;
    margin-right: auto;
    clear: both;
    float: left;
    width: 100%;
    display: block;
  }   
  
}
</style>


<?php get_footer(); ?>