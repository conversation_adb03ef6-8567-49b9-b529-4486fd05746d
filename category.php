<?php
/**
 * Category template file
 *
 * @package WordPress
 * @subpackage VuTruSo
 */

get_header();

// Lấy thông tin danh mục
$category = get_queried_object();
$cat_id = $category->term_id;
$cat_name = single_cat_title('', false);

// Lấy thông tin page hiện tại
$paged = (get_query_var('paged')) ? get_query_var('paged') : 1;

// Hiển thị bài nổi bật chỉ ở trang đầu tiên
$featured_post_id = 0;
if (1 == $paged) {
    // Query cho bài nổi bật
    $featured_args = array(
        'posts_per_page' => 1,
        'cat' => $cat_id,
        'orderby' => 'rand',
        'ignore_sticky_posts' => true,
        'no_found_rows' => true,
        'update_post_meta_cache' => false,
        'update_post_term_cache' => false
    );
    
    $featured_query = new WP_Query($featured_args);
    
    if ($featured_query->have_posts()) {
        ?>
        <div class="container-fluid bgcolor">
            <div class="innerContainer">
                <h1 itemprop="headline" class="dark super"><span class="underline"><?php echo esc_html($cat_name); ?></span></h1>

                <?php while($featured_query->have_posts()) : $featured_query->the_post(); 
                    $featured_post_id = get_the_ID(); // Lưu ID bài đã hiển thị
                ?>
                <div class="row">
                    <div class="col-md-6 col-lg-7 col-sm-12 feature-blog">
                        <div class="content">
                            <h2 itemprop="headline"><?php the_title(); ?></h2>
                            <?php the_excerpt(); ?>
                            <a class="doc-tiep" href="<?php the_permalink(); ?>"><?php esc_html_e('Đọc tiếp', 'vutruso'); ?>
                                <i class="fa fa-terminal" aria-hidden="true"></i></a>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-5 col-sm-12 feature-image">
                        <?php if(has_post_thumbnail()) { ?>
                        <img style="max-height: 400px; border-radius: 8px;" loading="lazy"
                            src="<?php the_post_thumbnail_url(); ?>" alt="<?php the_title(); ?>">
                        <?php } else { ?>
                        <img style="max-height: 400px; border-radius: 8px;" loading="lazy" height="340"
                            src="<?php echo get_template_directory_uri(); ?>/screenshot.jpg" alt="<?php the_title(); ?>" />
                        <?php } ?>
                    </div>
                </div>
                <?php endwhile; ?>
            </div>
        </div>
        <?php
        wp_reset_postdata();
    } else {
        // Không tìm thấy bài viết nổi bật
        ?>
        <div class="container-fluid bgcolor">
            <div class="innerContainer">
                <h1 itemprop="headline" class="dark super"><span class="underline"><?php echo esc_html($cat_name); ?></span></h1>
                <h2 style="color: #fff; font-size: 18px;">
                    <?php _e('Xin lỗi, Không có bài viết nào trong chuyên mục này cả!', 'vutruso'); ?></h2>
            </div>
        </div>
        <?php
    }
}

// Tùy chỉnh main query nếu có bài nổi bật
if ($featured_post_id > 0) {
    global $wp_query;
    $excluded_posts = $wp_query->get('post__not_in');
    if (!is_array($excluded_posts)) {
        $excluded_posts = array();
    }
    $excluded_posts[] = $featured_post_id;
    $wp_query->set('post__not_in', $excluded_posts);
    $wp_query->get_posts();
}
?>

<div class="container" style="margin-top:15px">
    <div class="row">
        <?php if (have_posts()): ?>
            <?php while(have_posts()) : the_post(); ?>
            <div <?php post_class('col-lg-4 col-md-6 col-sm-12'); ?>>
                <article class="blog-archive">
                    <a href="<?php the_permalink(); ?>">

                        <div class="image-container">
                            <?php if(has_post_thumbnail()) { ?>  
                            <?php the_post_thumbnail('full', array('title' => get_the_title(), 'alt' => get_the_title(), 'itemprop' => 'image')); ?>
                            <?php } else { ?>
                            <img loading="lazy" height="340" src="<?php echo get_template_directory_uri(); ?>/screenshot.jpg"
                                alt="<?php the_title(); ?>" />
                            <?php } ?>
                        </div>
                    </a>
                    <div class="content">
                        <div class="related-post-content">
                            <div class="meta-blog">
                                <div class="cat-blog">
                                    <span style="color: #156594;">
                                        <?php 
                                        $post_tags = get_the_tags();
                                        if ($post_tags) : 
                                        ?>
                                        <div class="post-tags">
                                            <?php 
                                            $tag_count = 0;
                                            foreach ($post_tags as $tag) : 
                                                if ($tag_count >= 2) break; 
                                            ?>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15"
                                                viewBox="0 0 24 24" style="fill: rgba(0, 0, 0, 1);transform: ;msFilter:;">
                                                <path d="M11.707 2.293A.997.997 0 0 0 11 2H6a.997.997 0 0 0-.707.293l-3 3A.996.996 0 0 0 2 6v5c0 .266.105.52.293.707l10 10a.997.997 0 0 0 1.414 0l8-8a.999.999 0 0 0 0-1.414l-10-10zM13 19.586l-9-9V6.414L6.414 4h4.172l9 9L13 19.586z"></path>
                                                <circle cx="8.353" cy="8.353" r="1.647"></circle>
                                            </svg>
                                            <a href="<?php echo esc_url(get_tag_link($tag->term_id)); ?>"><?php echo esc_html($tag->name); ?></a>
                                            <?php 
                                            $tag_count++;
                                            endforeach; 
                                            ?>
                                        </div>
                                        <?php endif; ?>
                                    </span>
                                </div>
                                <span class="archive-date"><?php echo get_the_date('d-m-Y'); ?></span>
                            </div>
                            <h2 class="title-blog">
                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                            </h2>
                            <div class="excerpt-blog">
                                <?php echo get_excerpt(200); ?>
                            </div>
                        </div>
                    </div>
                </article>
            </div>
            <?php endwhile; ?>

            <div class="col-sm-12">
                <?php 
                if (function_exists("vts_page_navi")) {
                    vts_page_navi();
                } else {
                    the_posts_pagination(array(
                        'prev_text' => __('&laquo; Previous', 'vutruso'),
                        'next_text' => __('Next &raquo;', 'vutruso'),
                    ));
                }
                ?>
            </div>
        <?php else: ?>
            <?php if ($paged > 1): // Chỉ hiển thị thông báo nếu không phải trang đầu tiên ?>
            <div class="col-sm-12">
                <div class="no-posts">
                    <h2><?php _e('Không tìm thấy bài viết', 'vutruso'); ?></h2>
                    <p><?php _e('Không còn bài viết nào khác trong chuyên mục này.', 'vutruso'); ?></p>
                </div>
            </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>


<style>
h1.dark {
    color: #0076b6;
    font-weight: 600;
    height: auto;
    margin: .5em 0 .6em;
    width: auto;
}

h1 .underline {
    background-image: linear-gradient(#fd0, #fd0);
    background-position: 0 85%;
    background-size: 100% 6px;
}

h1.super {
    font-size: 34px;
    font-weight: 800;
    line-height: 32px;
    margin-bottom: 7%;
}

h1.super .underline {
    background-position: 0 86%;
    background-size: 100% 9px;
}

h1.dark .underline {
    background-image: linear-gradient(#fd0, #fd0);
}

.underline {
    background-repeat: no-repeat;
    font-family: inherit;
    font-weight: inherit;
    height: inherit;
    margin: inherit;
    padding: inherit;
    width: inherit;
}

.bgcolor {
    padding-top: 53px !important;
}

.feature-blog h2 {
    color: #fff;
}

        .blog-archive:hover {
            transform: translate(0, -5px);
        }
        
        /* Container ảnh với aspect ratio cố định */
        .blog-archive .image-container {
            position: relative;
            width: 100%;
            /* Tạo aspect ratio 16:9 */
            padding-bottom: 56.25%; /* 9/16 * 100% */
            overflow: hidden;
            background: #f8f9fa;
        }
        
        /* Ảnh responsive với object-fit */
        .blog-archive .image-container img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            /* object-fit giúp ảnh không bị méo */
            object-fit: cover;
            object-position: center;
            transition: transform 0.3s ease;
        }
        
        .blog-archive:hover .image-container img {
            transform: scale(1.05);
        }
        
        .blog-archive .content {
            padding: 12px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        




</style>
<?php get_footer(); ?>