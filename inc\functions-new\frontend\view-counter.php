<?php
/**
 * VTS View Counter
 * 
 * Post view counter functionality
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Set post views with caching optimization
 */
function vts_setPostViews($postID) {
    $count_key = 'post_views_count';
    $cache_key = 'post_views_' . $postID;
    
    // Get from cache first
    $count = wp_cache_get($cache_key, 'post_views');
    
    if (false === $count) {
        $count = get_post_meta($postID, $count_key, true);
        $count = empty($count) ? 0 : (int)$count;
    }
    
    $count++;
    
    // Cache for 5 minutes
    wp_cache_set($cache_key, $count, 'post_views', 300);
    
    // Update DB every 10 views or after 5 minutes
    if ($count % 10 == 0) {
        update_post_meta($postID, $count_key, $count);
    }
}

/**
 * Get post views with formatting
 */
function vts_getPostViews($postID) {
    $count_key = 'post_views_count';
    $count = get_post_meta($postID, $count_key, true);
    
    if ($count == '') {
        delete_post_meta($postID, $count_key);
        add_post_meta($postID, $count_key, '0');
        return "0 View";
    }
    
    if ($count > 1000) {
        return round($count / 1000, 1) . 'k';
    } else {
        return $count . '';
    }
}
