<?php
/**
 * VTS Frontend Scripts & Styles
 * 
 * Enqueue and manage frontend assets
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Optimized scripts and styles enqueue function
 */
function vts_optimized_scripts() {
    
    // ========== ENQUEUE STYLES & SCRIPTS (Priority 10) ==========
    
    // Enqueue CSS files
    wp_enqueue_style('vts-fontawesome', get_template_directory_uri() . '/css/font-awesome.min.css');
    wp_enqueue_style('layout-style', get_template_directory_uri() . '/assets/css/layout.css', [], '1.0.4');
    wp_enqueue_style('vts-style', get_stylesheet_uri() . '?v=1.5');
    wp_enqueue_style('vts-woo', get_template_directory_uri() . '/assets/css/woo.css', [], '1.0.3');
    wp_enqueue_style('vts-mobile-menu', get_template_directory_uri() . '/assets/css/mobile.css');
    wp_enqueue_style('custom-style', get_template_directory_uri() . '/assets/css/custom.css', [], '1.1.9');

    // Enqueue JS files
    wp_enqueue_script('jquery');
    wp_enqueue_script('vts-custom', get_template_directory_uri() . '/js/custom.js?v=1.1', [], '', true);

    // Conditional styles for home page
    if (is_home() || is_front_page()) {
        wp_enqueue_style('vts-home-styles', get_template_directory_uri() . '/css/home.css', [], '1.0.0');
    }

    // ========== REMOVE DEFAULT WORDPRESS STYLES (Priority 100) ==========
    
    vts_remove_default_styles();
    vts_conditional_dequeue();
}

/**
 * Remove default WordPress styles
 */
function vts_remove_default_styles() {
    // Remove classic theme styles
    wp_dequeue_style('classic-theme-styles');
    wp_deregister_style('classic-theme-styles');
    
    // Remove global styles
    wp_dequeue_style('global-styles');
    wp_deregister_style('global-styles');

    // Remove WooCommerce inline CSS
    add_filter('woocommerce_enqueue_styles', '__return_empty_array');
    wp_dequeue_style('woocommerce-inline');
    remove_action('wp_head', 'woocommerce_dark_mode_inline_css');

    // Remove SEOPress styles
    wp_dequeue_style('wpseopress-local-business-style');
    wp_dequeue_style('wpseopress-table-of-contents-style');
    
    // Remove SEOPress filters
    add_filter('seopress_faq_block_inline_css', '__return_false');
    add_action('seopress_pro_breadcrumbs_css', '__return_false');

    // Remove dashboard icons
    wp_dequeue_style('dashicons');

    // Remove WooCommerce blocks styles
    $wc_block_styles = [
        'wc-blocks-vendors-style', 'wc-blocks-style', 'wc-blocks-style-product-sale-badge',
        'wc-blocks-style-product-search', 'wc-blocks-style-product-sku',
        'wc-blocks-style-product-stock-indicator', 'wc-blocks-style-product-summary',
        'wc-blocks-style-product-title', 'wc-blocks-style-rating-filter',
        'wc-blocks-style-reviews-by-category', 'wc-blocks-style-reviews-by-product',
        'wc-blocks-style-product-details', 'wc-blocks-style-single-product',
        'wc-blocks-style-stock-filter', 'wc-blocks-style-cart', 'wc-blocks-style-checkout',
        'wc-blocks-style-mini-cart-contents', 'bsearch-style'
    ];
    
    foreach ($wc_block_styles as $style) {
        wp_dequeue_style($style);
    }
}

/**
 * Conditional dequeue based on page context
 */
function vts_conditional_dequeue() {
    // WooCommerce conditional dequeue
    if (function_exists('is_woocommerce')) {
        if (!is_woocommerce() && !is_cart() && !is_checkout()) {
            // Dequeue WooCommerce styles
            $woo_styles = [
                'woocommerce-general', 'woocommerce-layout', 'woocommerce-smallscreen',
                'woocommerce_frontend_styles', 'woocommerce_fancybox_styles',
                'woocommerce_chosen_styles', 'woocommerce_prettyPhoto_css'
            ];
            foreach ($woo_styles as $style) {
                wp_dequeue_style($style);
            }

            // Dequeue WooCommerce scripts
            $woo_scripts = [
                'wc_price_slider', 'wc-single-product', 'wc-add-to-cart', 'wc-checkout',
                'wc-add-to-cart-variation', 'wc-cart', 'wc-chosen', 'woocommerce',
                'prettyPhoto', 'prettyPhoto-init', 'jquery-blockui', 'jquery-placeholder',
                'fancybox', 'jqueryui'
            ];
            foreach ($woo_scripts as $script) {
                wp_dequeue_script($script);
            }
        }
    }

    // Page specific dequeue
    if (!is_page('28')) {
        wp_dequeue_script('wc-cart-fragments');
        wp_dequeue_script('wc-cart-fragments-js-extra');
    }

    if (is_page('2684')) {
        wp_dequeue_style('vts-woo-css');
    }

    // Single post condition
    if (!is_single()) {
        wp_dequeue_script('toc-front');
    }

    // Home/Front page conditions
    if (is_home() || is_front_page()) {
        $home_scripts = ['venobox-start', 'venobox-wp'];
        $home_styles = ['venobox-wp', 'vts-woo'];
        
        foreach ($home_scripts as $script) {
            wp_dequeue_script($script);
        }
        foreach ($home_styles as $style) {
            wp_dequeue_style($style);
        }
    }
}

// Single action hook with appropriate priority
add_action('wp_enqueue_scripts', 'vts_optimized_scripts', 999);
