<?php

/* Sidebar Widget
*===============================================================*/
function vts_sidebar_widgets(){
    register_sidebar(array(
            'name'          =>  __('Right Sidebar' , 'vutruso'),
            'id'            =>  ('right_sidebar'),
            'description'   =>  __('Hiển thị các tiện ích ở bên phải website (sidebar)' , 'vutruso'),
            'before_widget' => '',
            'after_widget'  => '',
            'before_title'  => '',
            'after_title'   => ''
        ));

    register_sidebar( array(
        'name'          => esc_html_x( 'Footer - Column 1', 'widget area', 'vutruso' ),
        'id'            => 'footer1',
        'description'   => esc_html__( 'First column on footer.', 'vutruso' ),
        'before_widget' => '',
        'after_widget'  => '',
        'before_title'  => '<div class="title">',
        'after_title'   => '</div>',
    ) );

    register_sidebar( array(
        'name'          => esc_html_x( 'Footer - Column 2', 'widget area', 'vutruso' ),
        'id'            => 'footer2',
        'description'   => esc_html__( 'Second column on footer.', 'vutruso' ),
        'before_widget' => '',
        'after_widget'  => '',
        'before_title'  => '<div class="title">',
        'after_title'   => '</div>',
    ) );

    register_sidebar( array(
        'name'          => esc_html_x( 'Footer - Column 3', 'widget area', 'vutruso' ),
        'id'            => 'footer3',
        'description'   => esc_html__( 'Third column on footer.', 'vutruso' ),
        'before_widget' => '',
        'after_widget'  => '',
        'before_title'  => '<div class="title">',
        'after_title'   => '</div>',
    ) );

    register_sidebar( array(
        'name'          => esc_html_x( 'Footer - Column 4', 'widget area', 'vutruso' ),
        'id'            => 'footer4',
        'description'   => esc_html__( 'Fourth column on footer.', 'vutruso' ),
        'before_widget' => '',
        'after_widget'  => '',
        'before_title'  => '',
        'after_title'   => '',
    ) );    

    register_sidebar( array(
        'name'          => esc_html_x( 'Footer - Column 5', 'widget area', 'vutruso' ),
        'id'            => 'footer5',
        'description'   => esc_html__( '5 Column on footer.', 'vutruso' ),
        'before_widget' => '',
        'after_widget'  => '',
        'before_title'  => '',
        'after_title'   => '',
    ) );

    register_sidebar( array(
        'name'          => esc_html_x( 'Footer - Column 6', 'widget area', 'vutruso' ),
        'id'            => 'footer6',
        'description'   => esc_html__( '6 column on footer.', 'vutruso' ),
        'before_widget' => '',
        'after_widget'  => '',
        'before_title'  => '<div class="title">',
        'after_title'   => '</div>',
    ) );

    register_sidebar( array(
        'name'          => esc_html_x( 'Single Product', 'widget area', 'vutruso' ),
        'id'            => 'product_sidebar',
        'description'   => esc_html__( 'Right sidebar on single product', 'vutruso' ),
        'before_widget' => '<div class="buy-vts">',
        'after_widget'  => '</div>',
        'before_title'  => '<span class="vts-option">',
        'after_title'   => '</span>',
    ) );    

}
add_action('init', 'vts_sidebar_widgets');