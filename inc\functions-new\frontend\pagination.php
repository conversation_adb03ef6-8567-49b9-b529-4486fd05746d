<?php
/**
 * VTS Pagination Functions
 * 
 * Custom pagination functionality
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * VTS Page Navigation
 */
function vts_page_navi() {
    global $wp_query;
    $big = 999999999;
    $paginate_links = paginate_links([
        'base' => str_replace($big, '%#%', html_entity_decode(get_pagenum_link($big))),
        'current' => max(1, get_query_var('paged')),
        'total' => $wp_query->max_num_pages,
        'mid_size' => 2,
        'prev_next' => true,
        'prev_text' => __('&laquo;', 'vutruso'),
        'next_text' => __('&raquo;', 'vutruso'),
        'type' => 'list',
    ]);

    if (is_string($paginate_links)) {
        $paginate_links = str_replace("<ul class='page-numbers'>", "<ul class='pagination'>", $paginate_links);
        $paginate_links = str_replace('<li><span class="page-numbers dots">', "<li><a href='#'>", $paginate_links);
        $paginate_links = str_replace("<li><span class='page-numbers current'>", "<li class='current'>", $paginate_links);
        $paginate_links = str_replace('</span>', '</a>', $paginate_links);
        $paginate_links = str_replace("<li><a href='#'>&hellip;</a></li>", "<li><span class='dots'>&hellip;</span></li>", $paginate_links);
        $paginate_links = preg_replace('/\s*page-numbers/', '', $paginate_links);

        if ($paginate_links) {
            echo '<div class="page-navigation">';
            echo $paginate_links;
            echo '</div>';
        }
    }
}

/**
 * Custom pagination function
 */
function pagination($pages = '', $range = 4) {
    $showitems = ($range * 2) + 1;
    global $paged;
    if (empty($paged)) $paged = 1;

    if ($pages == '') {
        global $wp_query;
        $pages = $wp_query->max_num_pages;
        if (!$pages) {
            $pages = 1;
        }
    }

    if (1 != $pages) {
        echo "<nav class='vts-nav' aria-label='Page navigation example'>  <ul class='pagination'> <span>Page " . $paged . " of " . $pages . "</span>";
        if ($paged > 2 && $paged > $range + 1 && $showitems < $pages) echo "<a href='" . get_pagenum_link(1) . "'>&laquo; First</a>";
        if ($paged > 1 && $showitems < $pages) echo "<a href='" . get_pagenum_link($paged - 1) . "'>&lsaquo; Previous</a>";
        for ($i = 1; $i <= $pages; $i++) {
            if (1 != $pages && (!($i >= $paged + $range + 1 || $i <= $paged - $range - 1) || $pages <= $showitems)) {
                echo ($paged == $i) ? "<li class=\"page-item active\"><a class='page-link'>" . $i . "</a></li>" : "<li class='page-item'> <a href='" . get_pagenum_link($i) . "' class=\"page-link\">" . $i . "</a></li>";
            }
        }
        if ($paged < $pages && $showitems < $pages) echo " <li class='page-item'><a class='page-link' href=\"" . get_pagenum_link($paged + 1) . "\">i class='flaticon flaticon-back'></i></a></li>";
        if ($paged < $pages - 1 && $paged + $range - 1 < $pages && $showitems < $pages) echo " <li class='page-item'><a class='page-link' href='" . get_pagenum_link($pages) . "'><i class='flaticon flaticon-arrow'></i></a></li>";
        echo "</ul></nav>\n";
    }
}

/**
 * Clean pagination - remove heading and role
 */
function vts_clean_pagination($content) {
    $content = str_replace('role="navigation"', '', $content);
    $content = preg_replace('#<h2.*?>(.*?)<\ /h2>#si', '', $content);
    return $content;
}
add_action('navigation_markup_template', 'vts_clean_pagination');
