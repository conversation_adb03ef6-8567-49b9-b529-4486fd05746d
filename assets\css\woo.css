.woocommerce div.product div.images img {
  display: block;
  width: 100%;
  height: auto;
  box-shadow: none;
}

.flex-control-thumbs li:nth-child(4n) {
  clear: right;
  margin-right: 0 !important;
}

#content .buy-vts .infomation {
  display: block;
  height: auto;
  margin-bottom: 4px;
  border: 1px #e9e9e9 solid;
  padding: 11px 5px;
  overflow: hidden;
  overflow-y: scroll;
  overflow-x: hidden;
  border-radius: 6px;
}
#content .buy-vts .infomation .woocommerce-review-link {
  display: none;
}

.sidebar-single-product .buy-vts > .vts-option {
  font-weight: bold;
}
#content .buy-vts .price {
  display: block;
  margin-bottom: 4px;
  border-bottom: 1px #e9f2f7 dotted;
  text-align: center;
  overflow: hidden;
  width: 100%;
}

#content .buy-vts p > strong {
  margin-top: 8px;
  font-size: 16.67px;
  font-weight: 700;
  line-height: 20px;
}
.woocommerce-product-details__short-description p {
  padding: 0 10px;
}
.woocommerce-product-details__short-description ul {
  list-style: circle;
  margin-left: 24px;
}
.woocommerce-product-details__short-description li {
  font-size: 15.5px;
  line-height: 25px;
}
.woocommerce-product-details__short-description li::first-letter {
  text-transform: uppercase;
}

.single_add_to_cart_button {
  font-size: 16px;
  color: #fff;
  padding: 0 30px;
  line-height: 50px;
  height: 50px;
  border: 2px solid #22afcc;
  background: #56bfd4;
  width: 100%;
  margin-top: 10px;
  cursor: pointer;
  font-weight: bold;
  text-transform: uppercase;
}

.single_add_to_cart_button:hover {
  background: #2095af;
}
.product_meta .sku_wrapper,
.product_meta .posted_in,
.product_meta .tagged_as {
  clear: both;
  display: block;
}

.product_meta span:before {
  font-family: "FontAwesome";
  display: inline-block;
  color: #29607c;
  margin-right: 9px;
  margin-left: 1px;
  font-size: 0.765rem;
}
.product_meta .sku_wrapper:before {
  content: "\f05a";
}

.product_meta .posted_in:before {
  content: "\f07c";
}
.product_meta .tagged_as:before {
  content: "\f02c";
}

.product_meta {
  margin-top: 15px;
}

#content .buy-vts .infomation > span {
  padding: 4px;
}

#content .buy-vts {
  display: block;
  overflow: hidden;
  height: auto;
  /*padding: 1px*/
}
#content .vts-gallery {
  display: block;
  position: relative;
  float: left;
  border: 1px #e9e9e9 solid;
  padding: 8px;
  overflow: hidden;
  height: auto !important;
  border-radius: 4px;
}

.woocommerce-message .button,
.woocommerce-error .button,
.woocommerce-info .button {
  background: #fff !important;
  color: #5b5e6d !important;
  font-size: 0.8em !important;
  font-weight: 700;
  margin-left: auto !important;
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  -o-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  -webkit-order: 3;
  -ms-order: 3;
  order: 3;
}

.woocommerce-message {
  color: #468847;
  background: #e9ffd9;
}

.woocommerce-message,
.woocommerce-error,
.woocommerce-info {
  padding: 1em;
  box-shadow: none;
  border: 1px solid rgba(0, 0, 0, 0.05);
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -o-border-radius: 5px;
  border-radius: 5px;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: -ms-flex;
  display: flex;
  -webkit-align-items: center;
  -ms-align-items: center;
  align-items: center;
  position: relative;
  background-color: #e9ffd9;
  color: #515151;
  list-style: none outside;
  width: auto;
  word-wrap: break-word;
  margin: 0.3em 0;
}

.woocommerce-message > a,
.woocommerce-error > a,
.woocommerce-info > a {
  padding-left: 8px;
}

.woocommerce-message a.button,
.woocommerce-error a.button,
.woocommerce-info a.button {
  border: 0;
  padding: 0 20px;
  line-height: 40px;
  height: 40px;
  overflow: hidden;
  white-space: normal;
}

/*add sucess*/

.woocommerce-product-gallery {
  opacity: 1;
  position: relative;
  margin-bottom: 3em;
  clear: both;
}
.woocommerce-product-gallery figure {
  margin: 0;
}

.woocommerce-product-gallery .flex-control-thumbs li {
  list-style: none;
  float: left;
  cursor: pointer;
}

.woocommerce-product-gallery .flex-control-thumbs img {
  opacity: 0.5;
}

.woocommerce-product-gallery .flex-control-thumbs img.flex-active,
.woocommerce-product-gallery .flex-control-thumbs img:hover {
  opacity: 1;
}

.woocommerce-product-gallery img {
  display: block;
}

.woocommerce-product-gallery__trigger {
  position: absolute;
  top: 1em;
  right: 1em;
  z-index: 99;
}

.single-product
  div.product
  .woocommerce-product-gallery
  .woocommerce-product-gallery__trigger {
  position: absolute;
  top: 0.875em;
  right: 0.875em;
  display: block;
  height: 2em;
  width: 2em;
  border-radius: 3px;
  z-index: 99;
  text-align: center;
  text-indent: -999px;
  overflow: hidden;
}

.infomation > h1 {
  padding: 0;
  line-height: 25px;
  font-size: 19px;
  padding-top: 0;
  margin-top: 0;
  text-align: center;
}

.menu-header-menu-container {
  float: left;
}

#mini-cart-count {
  position: relative;
}

#mini-cart-count:before {
  content: "\f290";
  font-family: "FontAwesome";
  color: #1e8fcf;
}

#mini-cart-count span {
  position: absolute;
  top: 4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  color: #fff;
  text-align: center;
  letter-spacing: 0;
  font-weight: 400;
  font-size: 10px;
  background-color: #3390c5;
  line-height: 16px;
}

#mini-cart-count span::before {
  content: "";
  border-right: 7px solid transparent;
  position: absolute;
  right: 7px;
  top: 13px;
  border-top: 7px solid #3390c5;
}

.vts-price del {
  color: #265aaa;
  font-size: 14px;
}

.vts-price ins {
  text-decoration: none;
}

.woocommerce-ordering,
.woocommerce-result-count {
  display: none;
}

table.shop_table {
  border: 1px solid #e5ebef;
  margin: 0 -1px 20px 0;
  text-align: left;
  width: 100%;
  border-collapse: separate;
  border-radius: 3px;
  border-bottom: 3px solid #e5ebef;
  padding: 0;
  font-family: Montserrat;
}
.shop_table thead th {
  text-transform: uppercase;
}
.my_account_orders td,
#order_review table.shop_table td,
.cart-subtotal th,
.order-total th {
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 13px 12px;
  vertical-align: middle;
}
woocommerce-product-details__short-description {
  padding-top: 10px;
}
.woocommerce-orders-table__cell-order-actions {
  text-align: center;
}

.woocommerce-Addresses {
  margin-top: 2em;
  display: flex;
}

.woocommerce-account .u-column1,
.woocommerce-account .u-column2 {
  flex: 0 50%;
}

.woocommerce-account .input-field-wrapper {
  display: -webkit-flex;
  display: -ms-flexbox;
  display: -ms-flex;
  display: flex;
  -webkit-justify-content: space-between;
  -ms-justify-content: space-between;
  justify-content: space-between;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin: 0 -20px;
}

.woocommerce-account .name-filed-row,
.woocommerce-account .passowrd-field-row {
  -webkit-flex: 0 0 50%;
  -ms-flex: 0 0 46%;
  flex: 0 0 46%;
  padding: 0 20px;
}

.woocommerce-account
  .woocommerce
  .woocommerce-myaccount-content
  .edit-account
  h3 {
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0 0 1.5em;
}

.woocommerce-account .name-filed-row p,
.woocommerce-account .passowrd-field-row p {
  width: 100% !important;
  overflow: visible;
  float: none !important;
}

.woocommerce-account .name-filed-row p label,
.woocommerce-account .passowrd-field-row p label {
  font-size: 0.9em;
  margin-bottom: 0;
  padding-bottom: 0;
  font-weight: 500;
}

.woocommerce-account .name-filed-row h3,
.woocommerce-account .passowrd-field-row h3 {
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0 0 1.5em;
}

.woocommerce-EditAccountForm .woocommerce-Button {
  font-size: 16px;
  font-weight: 300;
  color: #fff;
  padding: 0 30px;
  line-height: 50px;
  height: 50px;
  border: 0;
  background: #56bfd4;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -o-border-radius: 3px;
  border-radius: 3px;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  -webkit-font-smoothing: auto;
  margin-top: 10px;
  cursor: pointer;
}

.woocommerce-EditAccountForm .woocommerce-Button:hover:focus {
  color: #fff;
  background: rgba(86, 191, 212, 0.8);
  -webkit-box-shadow: 0 10px 30px rgba(86, 191, 212, 0.3);
  -moz-box-shadow: 0 10px 30px rgba(86, 191, 212, 0.3);
  -o-box-shadow: 0 10px 30px rgba(86, 191, 212, 0.3);
  box-shadow: 0 10px 30px rgba(86, 191, 212, 0.3);
}

.woocommerce-address-fields__field-wrapper p {
  float: left !important;
  padding: 0 !important;
  width: 100% !important;
}

#order_review_heading {
  display: none;
}
.woocommerce-billing-fields > h3 {
  margin-top: 0;
}
#payment .place-order button {
  cursor: pointer;
  display: inline-block;
  text-decoration: none;
  background: #54c259;
  color: #fff;
  border-radius: 4px;
  border: none;
  font-size: 1em;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  width: 25%;
  font-weight: bold;
  text-transform: uppercase;
  -webkit-box-shadow: inset 0 -2px 0 rgba(0, 0, 0, 0.15);
  box-shadow: inset 0 -2px 0 rgba(0, 0, 0, 0.15);
  margin-left: 1%;
}

.woocommerce-billing-fields.woocommerce form .form-row-first,
.woocommerce-billing-fields form .form-row-last,
.woocommerce-page form .form-row-first,
.woocommerce-page form .form-row-last,
.woocommerce-form-row {
  overflow: visible;
  float: left;
}

.woocommerce-billing-fields form .form-row-last,
.woocommerce-billing-fields form .form-row-last {
  float: right;
}

.woocommerce-address-fields p {
  -webkit-flex: 0 0 50%;
  -ms-flex: 0 0 50%;
  flex: 0 0 50%;
}

#order_comments_field label,
.woocommerce-billing-fields__field-wrapper p label {
  display: none !important;
}

.vts-describe .woocommerce {
  overflow: hidden;
  padding: 1px;
}

.checkout_coupon .form-row-first {
  width: 50%;
}
.woocommerce-EditAccountForm > p {
  width: 100%;
  float: left !important;
}
.checkout_coupon .form-row-last button {
  cursor: pointer;
  display: inline-block;
  text-decoration: none;
  background: #1976d2;
  color: #fff;
  border-radius: 4px;
  border: none;
  font-size: 1em;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  font-weight: 400;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  padding: 12px 15px;
}

.payment_method_bacs input {
  float: left;
}

.payment_method_bacs label {
  float: left;
  padding: 1px;
  border-radius: 4px;
  font-weight: bold;
  text-transform: uppercase;
  margin-bottom: 4px;
}

.payment_method_bacs {
  padding: 5px 0px;
  overflow: hidden;
}

.wc-proceed-to-checkout {
  float: right;
  clear: both;
  text-transform: uppercase;
  font-weight: 700;
}

.wc-proceed-to-checkout a {
  cursor: pointer;
  display: inline-block;
  text-decoration: none;
  background: #1976d2;
  color: #fff;
  border-radius: 4px;
  border: none;
  font-size: 1em;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  font-weight: 400;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  padding: 12px 15px;
  border-bottom: 2px solid #094988;
}

.cart_totals h2 {
  text-transform: uppercase;
  text-align: right;
}

.cart_totals td {
  border: 1px solid #e5e5e5;
  line-height: 35px;
  text-align: center;
}

.cart_totals table {
  width: 50%;
  float: right;
}

.woocommerce table.shop_table th,
.order_details td,
.woocommerce-cart-form td {
  border: 1px solid #c7d9e2;
  display: table-cell;
  vertical-align: middle;
  padding: 10px;
}
.cart_item .product-name {
  text-align: left;
  font-size: 17px;
}

.account-orders-table tr .woocommerce-orders-table__header-order-actions {
  text-align: center;
}

.woocommerce-cart-form thead th,
.woocommerce-cart-form__cart-item td {
  text-align: center;
  color: #1976d2;
}

.shop_table img {
  text-align: center;
  padding: 6px;
  border-radius: 4px;
  overflow: hidden;
  margin: 0 auto;
  float: left;
  vertical-align: middle;
}

.shop_table .product-quantity {
  width: 10%;
}
.product-quantity {
  display: none;
}
.shop_table .product-quantity label {
  display: none;
}

.cart_item .product-subtotal {
  clear: both;
}
.shop_table .product-thumbnail {
  width: 23%;
}

.coupon label {
  display: none;
}

.coupon input {
  margin-top: 10px;
  width: 20% !important;
}

.coupon {
  padding-left: 5px;
}

.coupon > button {
  padding: 12px !important;
  margin-top: 9px !important;
  cursor: pointer;
  display: inline-block;
  text-decoration: none;
  background: #1976d2;
  color: #fff;
  border-radius: 4px;
  border: none;
  font-size: 1em;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  font-weight: 400;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.woocommerce-cart-form table.shop_table td.actions > .button {
  float: right;
  width: 24%;
  font-size: 14px;
  text-align: center;
  font-weight: bold;
}

.actions .button {
  cursor: pointer;
  display: inline-block;
  text-decoration: none;
  border-radius: 4px;
  border: none;
  font-size: 1em;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  font-weight: 400;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  padding: 12px 15px;
}

/*hide update cart*/
/*button[name='update_cart'] {
    display: none !important;
}*/

/*hide update cart*/

.woocommerce-cart-form__contents .coupon input {
  border-radius: 0;
  border: 2px solid #d0e1e4;
  padding: 9px;
  margin: 0;
}

.woocommerce-cart-form__contents .coupon button {
  text-transform: uppercase;
  border-radius: 0;
  padding: 12px !important;
}

.woocommerce-notices-wrapper .woocommerce-message {
  padding: 1em 1em 1em 1em;
  margin: 0 0 1em;
  position: relative;
  background-color: #e9ffd9;
  color: #468847;
  border-top: 3px solid #8fae1b;
  list-style: none outside;
  width: auto;
  word-wrap: break-word;
}

.product-remove a:hover {
  color: #d9534f !important;
  background: 0 0;
}

.product-remove {
  width: 4%;
}

.product-name a:hover {
  color: #1976d2;
}

.woocommerce .woocommerce-notice--success {
  background: #e6f7e4;
  padding: 15px;
  color: #39b44a;
  font-weight: bold;
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.7);
  border-radius: 4px;
}

/*thanks order*/
.woocommerce-order-pay .woocommerce-thankyou-order-details,
.woocommerce-order-received .woocommerce-thankyou-order-details,
.woocommerce-order-pay ul.order_details,
.woocommerce-order-received ul.order_details {
  padding: 0 !important;
  margin: 0 !important;
  background: #fff;
  border: 1px solid rgba(0, 34, 51, 0.1);
  margin-bottom: 0.7em !important;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -o-border-radius: 5px;
  border-radius: 5px;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: -ms-flex;
  display: flex;
  -webkit-align-items: center;
  -ms-align-items: center;
  align-items: center;
  -webkit-justify-content: space-between;
  -ms-justify-content: space-between;
  justify-content: space-between;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

ul.order_details li {
  list-style: none;
  margin-bottom: 1em;
  border-bottom: none !important;
  padding: 2em !important;
  position: relative;
  flex: 1;
  float: left;
  margin-right: 2em;
  text-transform: uppercase;
  font-size: 0.715em;
  line-height: 1;
  border-right: 1px dashed #d3ced2;
  padding-right: 2em;
  margin-left: 0;
  padding-left: 0;
  list-style-type: none;
}

.woocommerce-order-pay .woocommerce-thankyou-order-details li:last-child,
.woocommerce-order-received .woocommerce-thankyou-order-details li:last-child,
.woocommerce-order-pay ul.order_details li:last-child,
.woocommerce-order-received ul.order_details li:last-child {
  border: 0 !important;
}

.woocommerce ul.order_details li strong {
  display: block;
  text-transform: none;
  line-height: 1.5;
}

.wc-bacs-bank-details-heading {
  margin: 0 !important;
  background: rgba(165, 85, 202, 0.05);
  color: #a555ca;
  padding: 1em 1em !important;
  box-shadow: none;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: -ms-flex;
  display: flex;
  -webkit-align-items: center;
  -ms-align-items: center;
  align-items: center;
}

/*admin wooo*/
.woocommerce-MyAccount-navigation ul {
  margin: 0;
  padding: 0;
  border-bottom: 1px solid rgba(0, 34, 51, 0.1);
  display: -webkit-flex;
  display: -ms-flexbox;
  display: -ms-flex;
  display: flex;
  -webkit-align-items: center;
  -ms-align-items: center;
  align-items: center;
}

.woocommerce-MyAccount-navigation ul li.is-active {
  border: none;
}

.woocommerce-MyAccount-navigation ul li {
  font-size: 1rem;
  list-style: none;
  margin: 0 1.5em -1px 0;
  padding: 0;
}

.woocommerce-MyAccount-navigation ul li.is-active a {
  font-weight: 700;
  color: #323648;
  border-bottom: 1px solid #2c81d6;
}
#payment button {
  font-size: 16px;
  font-weight: 300;
  color: #fff;
  padding: 0 30px;
  line-height: 50px;
  height: 50px;
  border: 0;
  background: #56bfd4;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -o-border-radius: 3px;
  border-radius: 3px;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  -webkit-font-smoothing: auto;
  cursor: pointer;
  margin-top: 10px;
}

.woocommerce-MyAccount-paymentMethods td {
  padding: 12px;
  text-align: center;
  border: 1px solid #f5fafd;
}

.woocommerce-MyAccount-navigation ul li a {
  color: rgba(91, 94, 109, 0.8);
  border: none;
  font-weight: 400;
  padding: 1em 0;
  display: block;
}
.woocommerce-MyAccount-content {
  padding: 1em 0;
}

.woocommerce-MyAccount-content a {
  color: #56bfd4;
}

.woocommerce-MyAccount-content a:hover {
  border-color: #56bfd4;
}

.woocommerce-MyAccount-navigation ul li:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.woocommerce-MyAccount-navigation ul li:last-child a {
  padding: 0.7em 1.2em;
  background: rgba(0, 0, 0, 0.05);
  line-height: 1;
  color: #323648;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -o-border-radius: 3px;
  border-radius: 3px;
  font-weight: 400;
}

.woocommerce-MyAccount-navigation ul li a:hover {
  color: #323648;
}

.woocommerce-Address-title {
  display: block;
  height: 50px;
  line-height: 50px;
  font-weight: 700;
  font-size: 0;
  border: 0;
  border-left: 0;
  padding-left: 0;
  margin-bottom: 0;
}

.woocommerce-Address-title h3 {
  font-size: 1rem;
  margin: 0 0 10px;
  border-bottom: 1px solid rgba(0, 34, 51, 0.1);
  display: inline-block;
  line-height: 17px;
}

.woocommerce-Address-title .edit {
  float: none;
  margin-left: 10px;
  font-size: 11px;
  border: 0;
  padding: 2px 6px;
  background: #56bfd4;
  color: #fff;
  -webkit-border-radius: 1px;
  -moz-border-radius: 1px;
  -o-border-radius: 1px;
  border-radius: 1px;
}

.payment_box {
  position: relative;
  /*margin: 8px 5px 10px 7px;*/
  padding: 15px 15px;
  background-color: #f9fbff;
  -webkit-box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.05);
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.05);
  clear: both;
  border-radius: 3px;
}

.wc_payment_method {
  clear: both;
}

.woocommerce-checkout-review-order-table thead tr .product-name,
.woocommerce-checkout-review-order-table thead tr .product-total {
  text-transform: uppercase;
}

form.woocommerce-checkout {
  margin-top: 2%;
}
.woocommerce-terms-and-conditions-wrapper {
  display: none;
}
.woocommerce-form__label-for-checkbox {
  padding-left: 3px;
}
.woocommerce-privacy-policy-text {
  border-bottom: 1px solid #ccc;
  padding-bottom: 8px;
  margin-bottom: 10px;
  display: none;
}

.woocommerce-error {
  background: rgba(246, 75, 47, 0.05);
  color: #f64b2f;
  margin: 10px 0 !important;
  padding: 1em 0.7em !important;
  box-shadow: none;
  border: 1px solid rgba(0, 0, 0, 0.05);
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -o-border-radius: 5px;
  border-radius: 5px;
  overflow: hidden;
  display: block;
}

.woocommerce-error li {
  color: #f64b2f;
  line-height: 24px;
}

.woocommerce-error li:before {
  content: "\f071";
  top: 50%;
  left: 24px;
  margin-top: 0;
  font-size: 18px;
  font-family: FontAwesome;
  line-height: 18px;
  float: left;
  padding-right: 7px;
}

.woocommerce-order-received h1 {
  display: none;
}

.woocommerce-order > p {
  background: #dc7a54;
  padding: 15px;
  color: #fff;
  font-weight: bold;
}

.woocommerce-table--order-details thead th {
  text-transform: uppercase;
}

.woocommerce-order-details h2,
.woocommerce-order .woocommerce-customer-details,
.woocommerce-view-order .woocommerce-customer-details {
  display: none;
}

.woocommerce-address-fields > p > button {
  font-size: 16px;
  font-weight: 300;
  color: #fff;
  padding: 0 30px;
  line-height: 50px;
  height: 50px;
  border: 0;
  background: #56bfd4;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -o-border-radius: 3px;
  border-radius: 3px;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  -webkit-font-smoothing: auto;
  cursor: pointer;
  margin-top: 10px;
}

.woocommerce-address-fields > p > button:hover {
  color: #fff;
  background: rgba(86, 191, 212, 0.8);
  -webkit-box-shadow: 0 10px 30px rgba(86, 191, 212, 0.3);
  -moz-box-shadow: 0 10px 30px rgba(86, 191, 212, 0.3);
  -o-box-shadow: 0 10px 30px rgba(86, 191, 212, 0.3);
  box-shadow: 0 10px 30px rgba(86, 191, 212, 0.3);
}

.woocommerce-edit-address .woocommerce-address-fields > p {
  padding-left: 0;
}

.woocommerce-edit-address > p {
  float: left;
}

.woocommerce-edit-address .form-row-first,
.woocommerce-edit-address .form-row-last {
  float: left;
  width: 47% !important;
}

.woocommerce-edit-address .form-row-first {
  margin-right: 6%;
}

.woocommerce-address-fields .required {
  color: red;
}

tfoot tr:last-child {
  text-transform: uppercase;
  font-size: 20px;
}

tfoot tr:last-child span {
  color: red;
  font-weight: 700;
}

.woocommerce-MyAccount-navigation a:before {
  font-family: FontAwesome;
  color: #0578b7;
  padding-right: 4px;
}
.woocommerce-MyAccount-navigation-link--dashboard a:before {
  content: "\f0e4";
}

.woocommerce-MyAccount-navigation-link--orders a:before {
  content: "\f291";
}

.woocommerce-MyAccount-navigation-link--downloads a:before {
  content: "\f0ed";
}

.woocommerce-MyAccount-navigation-link--edit-address a:before {
  content: "\f09d";
}

.woocommerce-MyAccount-navigation-link--edit-account a:before {
  content: "\f007";
}

.woocommerce-MyAccount-navigation-link--customer-logout a:before {
  content: "\f08b";
}

.woocommerce-account
  .woocommerce-MyAccount-navigation
  ul
  li.is-active
  a:before {
  opacity: 1;
}

.woocommerce-Address {
  max-width: 100%;
  width: 100%;
  padding: 0;
}

.woocommerce-address-fields #billing_first_name_field,
.woocommerce-address-fields #billing_last_name_field {
  width: 100% !important;
  clear: both;
}
.woocommerce-address-fields
  #billing_first_name_field
  .woocommerce-input-wrapper,
.woocommerce-address-fields
  #billing_last_name_field
  .woocommerce-input-wrapper {
  width: 100% !important;
  clear: both;
}

/* Comment form woo */
.comment-reply-title,
.woocommerce-noreviews,
.woocommerce-Reviews-title {
  display: none;
}

.comment-form input[type="submit"] {
  padding: 10px 23px;
  outline: none;
  background: #ed3237;
  color: #fff;
  height: 40px;
  border: none;
  border-radius: 4px;
}

#reviews #comments ol.commentlist {
  margin: 0;
  width: 100%;
  background: 0 0;
  list-style: none;
  padding: 0;
}

#reviews #comments ol.commentlist:after {
  content: "";
  display: block;
  clear: both;
}

#reviews #comments ol.commentlist li {
  padding: 0;
  position: relative;
  background: 0 0;
  border: 0;
}

.star-rating {
  float: right;
  overflow: hidden;
  position: relative;
  height: 16px;
  line-height: 16px;
  width: 80px;
  background-image: url(../img/icon/star.png);
  background-size: 16px 48px;
  background-repeat: repeat-x;
}

.star-rating span {
  overflow: hidden;
  float: left;
  top: 0;
  left: 0;
  position: absolute;
  background-image: url(../img/icon/star.png);
  background-position: 0 -32px;
  background-repeat: repeat-x;
  text-indent: -9999px;
  background-size: 16px 48px;
}

.meta .star-rating {
  position: absolute;
  right: 20px;
  bottom: 14px;
}

.thumbnail {
  display: block;
  padding: 4px;
  line-height: 20px;
  border: 1px solid #ddd;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.055);
  -moz-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.055);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.055);
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.meta {
  border-top: 1px solid #ddd;
  background: #f8f8f8;
  padding: 10px 15px;
}

.comment_author_link {
  font-size: 11px;
  font-weight: bold;
  margin-right: 6px;
  text-transform: uppercase;
}

.comment-date {
  font-size: 11px;
  color: #888;
  padding: 0 6px;
  border-left: 1px solid #ddd;
  text-transform: uppercase;
}

.comment-text {
  position: relative;
  border: 1px solid #ddd;
  margin-left: 80px;
  margin-bottom: 15px;
}

.comment-text:before {
  display: block;
  width: 8px;
  height: 13px;
  background: url(img/arrow-comment.png) no-repeat;
  position: absolute;
  left: -8px;
  top: 15px;
}

.comment-text .star-rating {
  margin: 10px 5px;
}

.comment_container img {
  float: left;
}

.woocommerce-review__author {
  text-transform: capitalize;
}

.comment-text > .description > p {
  padding: 0 10px;
}

.single-product .price {
  text-align: center;
}

.single-product .quantity label {
  display: none;
}

.single-product .quantity input {
  box-shadow: 0 0 0 1px #edf0f3;
  display: none;
}

.single-product .related {
  margin-top: 15px;
  clear: both;
  display: block;
}

.single-product .related > h2 {
  text-transform: uppercase;
  border-bottom: 1px solid #f7f7f7;
  font-weight: bold;
  padding-bottom: 10px;
  line-height: 20px;
}

.single-product .product_list_widget {
  margin-top: 10px;
}

.woocommerce-message::before {
  color: #468847;
  content: "\f00c";
  font-family: "FontAwesome";
  padding-right: 5px;
}

.woocommerce-cart h1 {
  text-transform: uppercase;
}

.woocommerce-checkout .page-normal .title,
.woocommerce-cart .page-normal .title,
.woocommerce-cart .page-normal .title {
  display: none !important;
}

.woocommerce-cart .cart-empty:before {
  display: block;
  font-family: "FontAwesome";
  content: "\f07a";
  position: relative;
  font-size: 1em;
  color: #56bfd4;
  width: 2em;
  height: 2em;
  line-height: 2em;
  text-align: center;
  background: rgba(86, 191, 212, 0.1);
  -webkit-box-shadow: 0 0 0 0.5em rgba(86, 191, 212, 0.05);
  -moz-box-shadow: 0 0 0 0.5em rgba(86, 191, 212, 0.05);
  -o-box-shadow: 0 0 0 0.5em rgba(86, 191, 212, 0.05);
  box-shadow: 0 0 0 0.5em rgba(86, 191, 212, 0.05);
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -o-border-radius: 100%;
  border-radius: 100%;
  margin-right: 1em;
}

.woocommerce-cart .cart-empty,
.woocommerce-cart .return-to-shop {
  text-align: center;
}

.woocommerce-cart .cart-empty {
  font-size: 2em;
  font-weight: 300;
  line-height: 1.8em;
  margin: 2em 0 1em !important;
}

.woocommerce-cart .return-to-shop {
  margin-bottom: 8em !important;
}

.return-to-shop a {
  background: rgba(86, 191, 212, 0.8);
  -webkit-box-shadow: 0 10px 30px rgba(86, 191, 212, 0.3);
  -moz-box-shadow: 0 10px 30px rgba(86, 191, 212, 0.3);
  -o-box-shadow: 0 10px 30px rgba(86, 191, 212, 0.3);
  font-size: 100%;
  margin: 0;
  line-height: 1;
  cursor: pointer;
  position: relative;
  text-decoration: none;
  overflow: visible;
  padding: 0.618em 1em;
  font-weight: 700;
  border-radius: 3px;
  left: auto;
  color: #fff;
  background-color: #1976d2;
  border: 0;
  display: inline-block;
  background-image: none;
  box-shadow: none;
  text-shadow: none;
}

.vts-footer-cloum {
  /* width: 33.3333333%;*/
  float: left;
}

.vts-corporate,
.lien-ket-nhanh {
  width: 40%;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: " ";
}

.clearfix:after {
  clear: both;
}

.random.vts_news-list {
    padding: 10px 20px;
    margin: 20px 0;
    margin-left: 0;
    border: 1px dashed #ccc;
    clear: both;
    padding-top: 0;
}

.random.vts_news-list .news-itm {
  margin-bottom: 0;
  border-bottom: 0;
  padding-bottom: 0px;
}

.random.vts_news-list .news-itm .fa {
  color: #31a0da;
  font-size: 20px;
  padding-right: 5px;
}

.vts_news-list {
  margin-top: 20px;
}

.vts_news-list .news-itm {
  margin-bottom: 10px;
  border-bottom: 1px dashed;
  border-color: #ccc;
  padding-bottom: 10px;
}

.vts_news-list .news-itm a {
  font-size: 16px;
  line-height: 28px;
}

.vts_news-list .news-itm a:hover {
  text-decoration: underline;
  color: #337ab7;
}

.onsale {
  position: absolute !important;
  margin: 3px 0 2px 4px;
  z-index: 99;
  background: red;
  padding: 5px;
  border-radius: 100px;
  color: #fff;
}
.woocommerce-form-login label {
  display: none !important;
}
.woocommerce-form-login__submit {
  cursor: pointer;
  display: inline-block;
  text-decoration: none;
  background: #54c259;
  color: #fff;
  border-radius: 4px;
  border: none;
  font-size: 1em;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  padding: 19px 15px;
  width: 100%;
  font-weight: bold;
  text-transform: uppercase;
  -webkit-box-shadow: inset 0 -2px 0 rgba(0, 0, 0, 0.15);
  box-shadow: inset 0 -2px 0 rgba(0, 0, 0, 0.15);
}

.woocommerce-EditAccountForm fieldset {
  border: 1px solid #ddd;
  padding: 30px;
  border-radius: 4px;
  margin-top: 1%;
  clear: both;
  overflow: hidden;
  width: 100%;
  float: left;
}
.woocommerce-EditAccountForm fieldset p {
  float: left;
  width: 100%;
}
.woocommerce-EditAccountForm span em {
  font-size: 12px;
}
.woocommerce-billing-fields form .form-row label,
.woocommerce-page form .form-row label {
  display: block;
  color: #6c7b84;
  font-size: 16px;
}
.cart-subtotal {
  display: none;
}
.woocommerce-pagination span,
.woocommerce-pagination li a {
  display: inline-block;
  margin-right: 5px;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background: #dde0e2;
  border-radius: 100%;
  font-weight: 600;
  transition: all 0.4s ease-in-out;
}
.woocommerce-pagination {
  margin-bottom: 10px;
  overflow: hidden;
}
.woocommerce-pagination li{
float:left
}
#order_comments_field .woocommerce-input-wrapper {
  float: left;
  width: 100%;
}
.woocommerce-billing-fields__field-wrapper .form-row-first,
.form-row-last {
  float: left;
}
.address-field {
  clear: both;
}
.address-field span {
  width: 100%;
}
#billing_phone_field span,
#billing_email_field span {
  width: 100%;
}
#order_comments_field,
.woocommerce-additional-fields__field-wrapper,
.woocommerce-additional-fields {
  width: 100%;
  clear: both;
  padding: 0;
  margin: 0;
}
.woocommerce-account-fields .create-account {
  clear: both;
}

.woocommerce-terms-and-conditions-wrapper label {
  margin-left: 1%;
}

.archive-product .title .title > i {
  display: block;
  float: left;
  height: 50px;
  width: 50px;
  line-height: 50px;
  text-align: center;
  font-size: 19px;
  color: #333;
  background: #fff;
}

.archive-product .title span {
  font-weight: 700;
  font-size: 14.82px;
  color: #333;
}

.archive-product .title {
  display: block;
  overflow: hidden;
  background: #ffffff;
  color: #333;
  border: 1px #fbfcfc solid;
  font-weight: 700;
  border-radius: 4px;
  height: 46px;
  line-height: 48px;
  -webkit-box-shadow: 0 1px 3px rgba(17, 21, 25, 0.42);
  -moz-box-shadow: 0 1px 3px rgba(17, 21, 25, 0.42);
  box-shadow: 0 1px 3px rgb(222, 220, 220);
  text-transform: uppercase;
  margin: 0px;
  margin-bottom: 2%;
  padding: 0 14px;
}

  overflow: hidden;
  margin-bottom: 2%;
}
.woocommerce-billing-fields > h3 {
  display: none;
}
#order_review {
  margin-bottom: 2%;
}


@media screen and (max-width: 600px) {
  .product-quantity {
    width: 100%;
  }
  .product-quantity input {
    width: 36px !important;
    height: 28px;
  }

  .product-thumbnail {
    display: none !important;
  }
  .product-remove {
    width: 100%;
    border-radius: 4px 4px 0 0;
  }
  .woocommerce-page table.cart a.remove,
  .product-remove .remove {
    float: right !important;
  }

  .vts-title-single-product {
    font-size: 1.6em !important;
  }

  .woocommerce table.shop_table th {
    font-weight: 700;
    padding: 9px 12px;
    width: 100%;
    text-align: right;
  }

  .my_account_orders td,
  #order_review table.shop_table td,
  .cart-subtotal th,
  .order-total th {
    float: left;
  }
  #payment .place-order button {
    width: 98%;
    margin-left: 1%;
    border-radius: 6px;
  }
  .coupon input {
    width: 60% !important;
  }
  .woocommerce-cart-form table.shop_table td.actions > .button {
    width: 100%;
  }
  tbody > .woocommerce-cart-form__cart-item {
    margin-bottom: 2%;
    clear: both;
    overflow: hidden;
    display: block;
    border-radius: 4px;
  }
  .cart-collaterals {
    width: 100%;
    overflow: hidden;
    clear: both;
  }
  .cart_totals table {
    width: 100%;
    float: left;
  }

  /* Force table to not be like tables anymore */
  .woocommerce-page table.woocommerce-cart-form thead,
  .woocommerce-page table.woocommerce-cart-form tbody,
  .woocommerce-page table.woocommerce-cart-form th,
  .woocommerce-page table.woocommerce-cart-form td,
  .woocommerce-page table.woocommerce-cart-form tr {
    display: block;
  }

  /* Hide table headers (but not display: none;, for accessibility) */
  .woocommerce-page table.shop_table thead tr {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }

  .shop_table_responsive .cart-subtotal {
    display: none;
  }
  .cart-subtotal td {
    float: left;
  }
  .woocommerce-page table.shop_table td {
    /* Behave  like a "row" */
    /*    border: 1px solid #d2d3d3; */
    position: relative;
    padding-left: 30% !important;
    float: left;
    clear: both;
    width: 100%;
    text-align: right;
  }

  .woocommerce-page table.shop_table {
    border: none;
  }

  .woocommerce-page table.shop_table td.product-spacer {
    border-color: #fff;
    height: 10px;
  }

  .woocommerce-page table.shop_table td:before {
    position: absolute;
    top: 6px;
    left: 6px;
    white-space: nowrap;
  }

  /*
Label the data
*/

  .woocommerce-page table.shop_table td.product-thumbnail:before {
    content: "IMAGE";
  }

  .woocommerce-page table.shop_table td.product-name:before {
    content: "ITEM";
  }

  .woocommerce-page table.shop_table td.product-price:before {
    content: "GIÁ";
  }

  .woocommerce-page table.shop_table td.product-quantity:before {
    content: "SỐ LƯỢNG";
  }

  .woocommerce-page table.shop_table td.product-subtotal:before {
    content: "TỔNG";
  }

  .woocommerce-page table.shop_table td.product-total:before {
    content: "TỔNG";
  }

  .woocommerce-page table.cart td.actions,
  .woocommerce-page #content table.cart td.actions {
    text-align: left;
    border: 0;
    padding-left: 6px !important;
  }

  .woocommerce-page table.cart td.actions .button.alt,
  .woocommerce-page #content table.cart td.actions .button.alt {
    float: left;
    margin-top: 10px;
  }

  .woocommerce-page table.cart td.actions div,
  .woocommerce-page #content table.cart td.actions div,
  .woocommerce-page table.cart td.actions input,
  .woocommerce-page #content table.cart td.actions input {
    margin-bottom: 10px;
  }

  .woocommerce-page .cart-collaterals .cart_totals {
    float: left;
    width: 100%;
    text-align: left;
  }

  .woocommerce-page .cart-collaterals .cart_totals table tr.cart-subtotal td,
  .woocommerce-page .cart-collaterals .cart_totals table tr.shipping td,
  .woocommerce-page .cart-collaterals .cart_totals table tr.total td {
    padding-left: 6px !important;
  }

  .woocommerce-page table.shop_table tr.shipping td,
  .woocommerce-page table.shop_table tr.total td,
  .woocommerce-page table.shop_table.order_details tfoot th,
  .woocommerce-page table.shop_table.order_details tfoot td {
    padding-left: 6px !important;
    border: 0 !important;
  }

  .woocommerce-page table.shop_table tbody {
    padding-top: 10px;
  }

  .woocommerce .col2-set .col-1,
  .woocommerce-page .col2-set .col-1,
  .woocommerce .col2-set .col-2,
  .woocommerce-page .col2-set .col-2,
  .woocommerce form .form-row-first,
  .woocommerce form .form-row-last,
  .woocommerce-page form .form-row-first,
  .woocommerce-page form .form-row-last {
    float: none;
    width: 100%;
  }

  .woocommerce .order_details ul,
  .woocommerce-page .order_details ul,
  .woocommerce .order_details,
  .woocommerce-page .order_details {
    padding: 0;
  }

  .woocommerce .order_details li,
  .woocommerce-page .order_details li {
    clear: left;
    margin-bottom: 10px;
    border: 0;
  }

  .woocommerce-MyAccount-navigation ul {
    display: block !important;
    border: none;
  }
  .woocommerce-MyAccount-content {
    padding: 0 !important;
    margin-top: 6%;
  }
  /* END Make the cart table responsive */

  .woocommerce-page table.cart a.remove:hover {
    background-color: rgb(37, 37, 45);
    transition: color 0.4s ease 0s, background-color 0.4s ease 0s;
    color: rgb(255, 255, 255) !important;
  }

  .woocommerce #content table.cart a.remove,
  .woocommerce table.cart a.remove,
  .woocommerce-page #content table.cart a.remove,
  .woocommerce-page table.cart a.remove {
    height: 35px;
    border-radius: 50%;
    margin: 0px;
    font-family: "FontAwesome";
    font-style: normal;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    border: 1px solid transparent;
    line-height: 35px;
    float: left;
    position: relative;
    -webkit-font-smoothing: antialiased;
    transition: color 0.4s ease 0s, background-color 0.4s ease 0s;
    font-size: 0px !important;
    width: 35px !important;
    color: rgb(37, 37, 45) !important;
  }

  .woocommerce #content table.cart a.remove::before,
  .woocommerce table.cart a.remove::before,
  .woocommerce-page #content table.cart a.remove::before,
  .woocommerce-page table.cart a.remove::before {
    content: "\f014";
    font-size: 19px;
    position: absolute;
    width: 100%;
    height: 100%;
    transform: rotate(0deg);
    left: 0px;
    transition: transform 0.4s ease 0s;
  }

  .woocommerce-pagination {
    margin-bottom: 10px;
    overflow: hidden;
  }

  .woocommerce-pagination .current {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
  }
  .woocommerce-Price-amount bdi {
    font-size: 14px;
  }
}

/* Checkout Loader */
.blockUI.blockOverlay {
  background-image: url("https://vutruso.com/wp-content/themes/vutruso/img/icon/loading.svg") !important;
  background-position: center 30% !important;
  background-repeat: no-repeat !important;
  position: fixed !important;
}
.processing .blockOverlay {
  background-image: url("https://vutruso.com/wp-content/themes/vutruso/img/icon/loading.svg") !important;
  background-position: center 30% !important;
  background-repeat: no-repeat !important;
}
/* Checkout page processing spinner */
.woocommerce-checkout.processing .blockUI.blockOverlay {
  background-image: url("https://vutruso.com/wp-content/themes/vutruso/img/icon/loading.svg") !important;
  background-position: center 30% !important;
  background-repeat: no-repeat !important;
  position: fixed !important;
}
/* Checkout Loader */

.page-id-29 .main-single-page li {
  list-style-type: none;
  margin-left: 0 !important;
  width: 100%;
  margin-bottom: 0 !important;
  padding-right: 0 !important;
}

.page-id-29 .password-input {
  width: 100%;
  clear: both;
  margin-top: 10px;
}

.page-id-29 #order_review {
  margin-top: 0 !important;
}
