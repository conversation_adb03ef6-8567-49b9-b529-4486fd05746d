<?php
/**
 * VTS User Profile Customizations
 * 
 * Custom user profile fields and functionality
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add custom contact methods to user profile
 */
function vts_custom_contact_methods($contactmethods) {
    // Add custom contact fields
    $contactmethods['facebook'] = 'Facebook';
    $contactmethods['twitter'] = 'Twitter';
    $contactmethods['pinterest'] = 'Pinterest';
    $contactmethods['github'] = 'Github';
    $contactmethods['linkedin'] = 'Linkedin';
    $contactmethods['mobile'] = 'Mobile Phone';
    $contactmethods['zalo'] = 'Zalo';
    $contactmethods['jobTitle'] = 'Nghề nghiệp';
    $contactmethods['birthPlace'] = 'Nơi sinh';
    $contactmethods['diachi'] = 'Địa chỉ';
    $contactmethods['namsinh'] = 'Năm sinh';

    return $contactmethods;
}
add_filter('user_contactmethods', 'vts_custom_contact_methods', 10, 1);

/**
 * Add gender field to user profile
 */
function vts_add_user_profile_fields($bool) {
    global $current_user;
  
    // Get the current user's data
    $current_user_data = get_userdata($current_user->ID);
  
    echo '<tr><th><label for="s_box">Giới tính</label></th><td><select name="s_box">';
    echo '<option value="Nam" ' . selected('Nam', $current_user_data->s_box, false) . '>Nam</option>';
    echo '<option value="Nữ" ' . selected('Nữ', $current_user_data->s_box, false) . '>Nữ</option>';
    echo '</select></td></tr>';
  
    return $bool;
}
add_action('show_password_fields', 'vts_add_user_profile_fields');

/**
 * Update custom user profile fields
 */
function vts_update_user_profile_fields($user_id, $old_user_data) {
    // Update select box
    if (isset($_POST['s_box'])) {
        update_user_meta($user_id, 's_box', $_POST['s_box']);
    }
}
add_action('profile_update', 'vts_update_user_profile_fields', 10, 2);
