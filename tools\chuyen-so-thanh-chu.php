<?php
/*
 * Template Name: <PERSON><PERSON><PERSON><PERSON> số tiền thành chữ
 */
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Công cụ chuyển đổi số tiền thành chữ</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Basic Meta Tags -->
    <meta property="og:url" content="https://vutruso.com/chuyen-doi-so-tien-thanh-chu/">
    <link rel="canonical" href="https://vutruso.com/chuyen-doi-so-tien-thanh-chu/">
    <meta name="description" content="Công cụ chuyển đổi số thành chữ tiếng Việt nhanh chóng và chính xác với giao diện hiện đại và hoàn toàn miễn phí cho mọi người">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:site_name" content="Công cụ chuyển số thành chữ tiếng Việt">
    <meta property="og:locale" content="vi_VN">
    <meta property="og:type" content="website">
    <meta property="og:title" content="Công cụ chuyển số thành chữ tiếng Việt">
    <meta property="og:description" content="Công cụ chuyển đổi số thành chữ tiếng Việt nhanh chóng và chính xác với giao diện hiện đại và hoàn toàn miễn phí cho mọi người">
    <meta property="og:image" content="https://vutruso.com/wp-content/uploads/2025/07/chuyen-doi-so-tien-thanh-chu.png">
    <meta property="og:image:secure_url" content="https://vutruso.com/wp-content/uploads/2025/07/chuyen-doi-so-tien-thanh-chu.png">
    <meta property="og:image:width" content="405">
    <meta property="og:image:height" content="270">
    <meta property="og:image:alt" content="Công cụ chuyển số thành chữ - Vutruso">
    <meta property="fb:admins" content="100002603951764">
    <meta property="fb:app_id" content="***************">
    
    <!-- Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@vutruso">
    <meta name="twitter:title" content="Công cụ chuyển số thành chữ tiếng Việt">
    <meta name="twitter:description" content="Công cụ chuyển đổi số thành chữ tiếng Việt nhanh chóng và chính xác với giao diện hiện đại và hoàn toàn miễn phí cho mọi người">
    <meta name="twitter:image" content="https://vutruso.com/wp-content/uploads/2025/07/chuyen-doi-so-tien-thanh-chu.png">
    
    <!-- Structured Data JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "Công cụ chuyển số thành chữ tiếng Việt",
        "url": "https://vutruso.com/chuyen-doi-so-tien-thanh-chu/",
        "description": "Công cụ chuyển đổi số thành chữ tiếng Việt nhanh chóng và chính xác với giao diện hiện đại và hoàn toàn miễn phí cho mọi người",
        "applicationCategory": "UtilityApplication",
        "operatingSystem": "Any",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "VND"
        },
        "creator": {
            "@type": "Organization",
            "name": "Vũ Trụ Số",
            "url": "https://vutruso.com",
            "alternateName": "Vũ Trụ Số",
            "description": "Vũ Trụ Số chuyên cung cấp các giải pháp công nghệ, thiết kế website, quản trị website cho doanh nghiệp, cung cấp máy chủ ảo VPS/Hosting, dịch vụ chạy quảng cáo Google, quảng cáo Facebook, quảng cáo Zalo, các dịch vụ bảo mật website WordPress, tăng tốc website WordPress và SEO mới từ khóa lên top Google",
            "logo": "https://vutruso.com/wp-content/uploads/2020/07/vutruso_login.jpg",
            "sameAs": [
                "https://www.facebook.com/vutruso",
                "https://twitter.com/@vutruso",
                "https://www.pinterest.com/vutruso/",
                "https://www.instagram.com/vutruso",
                "https://www.youtube.com/channel/UCOEXvu-rxMQ6Tvqm1kZJUrg/about",
                "https://www.linkedin.com/in/vutruso",
                "https://g.page/vutruso",
                "https://vutruso.business.site/",
                "https://sites.google.com/view/vutruweb",
                "https://vutruso.tumblr.com/",
                "https://ok.ru/profile/589668477610"
            ],
            "vatID": "0317358676",
            "contactPoint": [
                {
                    "@type": "ContactPoint",
                    "telephone": "+***********",
                    "email": "<EMAIL>",
                    "contactOption": "TollFree",
                    "contactType": "customer support"
                }
            ]
        }
    }
    </script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            padding: 0;
        }

        .header-banner {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            padding: 20px 0 120px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header-banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-banner::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 30s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .header-content {
            position: relative;
            z-index: 2;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .main-title {
            font-size: 3.5em;
            margin: 0 0 20px 0;
            color: #fff;
            font-weight: 300;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            letter-spacing: -0.02em;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
        }

        .sub-title {
            color: rgba(255,255,255,0.9);
            font-size: 1.3em;
            line-height: 1.6;
            font-weight: 300;
            max-width: 890px;
            margin: 0 auto;
        }

        .container {
            max-width: 1000px;
            margin: -80px auto 0;
            position: relative;
            z-index: 10;
            padding: 0 20px 40px;padding-bottom: 0;
    margin-bottom: 0;
        }

        .converter-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            backdrop-filter: blur(15px);
            margin-bottom: 30px;
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 35px;
            text-align: center;
            position: relative;
        }

        .card-title {
            font-size: 1.8em;
            margin: 0 0 10px 0;
            font-weight: 300;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .card-subtitle {
            font-size: 1em;
            opacity: 0.9;
            font-weight: 300;
        }

        .card-body {
            padding: 10px;
        }

        .input-section {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
            padding: 0px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.05);
        }

        .input-group {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .input-group:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: #667eea;
        }

        .input-label {
            display: block;
            margin-bottom: 15px;
            color: #333;
            font-weight: 600;
            font-size: 1.1em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .number-input {
            width: 100%;
            padding: 18px 20px;
            border: 2px solid #e1e8ed;
            border-radius: 12px;
            font-size: 18px;
            font-family: 'Segoe UI', monospace;
            transition: all 0.3s ease;
            background: #f8f9ff;
            resize: none;
            min-height: 60px;
            line-height: 1.4;
        }

        .number-input:focus {
            outline: none;
            border-color: #667eea;
            background: #fff;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .number-input::placeholder {
            color: #9ca3af;
            font-style: italic;
        }

        .input-hint {
            margin-top: 12px;
            font-size: 14px;
            color: #6b7280;
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(102, 126, 234, 0.1);
            padding: 10px 15px;
            border-radius: 8px;
        }

        .convert-section {
            text-align: center;
            margin: 30px 0;
        }

        .convert-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 20px 60px;
            border-radius: 50px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 12px;
            position: relative;
            overflow: hidden;
        }

        .convert-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: all 0.3s ease;
        }

        .convert-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .convert-button:hover::before {
            left: 100%;
        }

        .convert-button:active {
            transform: translateY(-1px);
        }

        .convert-button:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .loading-spinner {
            display: none;
            width: 18px;
            height: 18px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .result-section {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            margin-top: 30px;
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .result-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .result-title {
            font-size: 1.4em;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .main-copy-button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            padding: 12px 20px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            backdrop-filter: blur(10px);
        }

        .main-copy-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .main-copy-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .result-content {
            padding: 30px;
        }

        .result-placeholder {
            text-align: center;
            padding: 40px 20px;
            color: #9ca3af;
            font-style: italic;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            font-size: 16px;
        }

        .result-placeholder i {
            font-size: 3em;
            opacity: 0.5;
        }

        .result-variants {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .result-variant {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
            border-radius: 15px;
            padding: 25px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .result-variant:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }

        .variant-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .variant-label {
            font-weight: 600;
            color: #4b5563;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .variant-copy-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            min-width: 36px;
            height: 32px;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .variant-copy-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .variant-copy-btn:active {
            transform: translateY(0);
        }

        .variant-copy-btn.copied {
            background: linear-gradient(135deg, #10b981, #059669);
            animation: pulse 0.3s ease;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .variant-text {
            color: #1f2937;
            font-size: 18px;
            line-height: 1.6;
            padding: 20px;
            background: white;
            border-radius: 12px;
            border: 2px solid #e5e7eb;
            word-wrap: break-word;
            min-height: 60px;
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .examples-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
        }

        .examples-header {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 25px;
            text-align: center;
        }

        .examples-title {
            font-size: 1.3em;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .examples-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .example-item {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .example-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
            border-color: #667eea;
        }

        .example-number {
            font-weight: 700;
            color: #667eea;
            font-size: 1.1em;
        }

        .toast {
            position: fixed;
            bottom: 30px;
            right: 30px;
            padding: 15px 25px;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
            max-width: 350px;
            font-size: 14px;
            backdrop-filter: blur(10px);
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .toast.error {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }

        .toast.warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        @media (max-width: 768px) {
            .header-banner {
                padding: 20px 0 100px;
            }
            
            .card-header {
                padding: 15px;
                font-size: 15px;
            }
            .main-title {
                font-size: 2.5em;
                flex-direction: column;
                gap: 10px;
            }
            
            .sub-title {
                font-size: 1.1em;
            }
            
            .container {
                margin: -60px auto 0;
                padding: 0 15px 30px;
            }
            
            .card-body {
                padding: 5px;
            }
            
            .input-section {
                padding: 0px;
            }
            
            .convert-button {
                width: 100%;
                padding: 18px;
            }
            
            .result-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .variant-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .variant-copy-btn {
                align-self: flex-end;
            }
            
            .examples-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .toast {
                bottom: 20px;
                right: 20px;
                left: 20px;
                max-width: none;
            }

            .examples-section{
                padding: 15px;

            }

            .example-item {
                padding: 5px;
            }
            .input-group {
                padding: 15px;;
            }
        }

        @media (max-width: 480px) {
            .main-title {
                font-size: 2em;
            }
            
            .variant-text {
                font-size: 16px;
            }
            
        }
    </style>
</head>
<body>
    <div class="header-banner">
        <div class="header-content">
            <h1 class="main-title">
                <i class="fas fa-calculator"></i>
                Chuyển số thành chữ
            </h1>
            <p class="sub-title">Công cụ chuyển đổi số thành chữ tiếng Việt nhanh chóng và chính xác với giao diện hiện đại</p>
        </div>
    </div>

    <div class="container">
        <div class="converter-card">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-exchange-alt"></i>
                    Chuyển Số Tiền Thành Chữ
                </h2>
                <p class="card-subtitle">Nhập số bất kỳ để chuyển đổi thành chữ tiếng Việt</p>
            </div>
            
            <div class="card-body">
                <div class="input-section">
                    <div class="input-group">
                        <label class="input-label" for="numberInput">
                            <i class="fas fa-hashtag"></i>
                            Nhập số cần chuyển đổi
                        </label>
                        <textarea 
                            class="number-input" 
                            id="numberInput" 
                            placeholder="Ví dụ: 123, 1000, 50000, 999999..."
                            aria-describedby="input-hint"
                        ></textarea>
                        <div class="input-hint" id="input-hint">
                            <i class="fas fa-info-circle"></i>
                            Hỗ trợ số từ 0 đến 999.999.999.999 (12 chữ số)
                        </div>
                    </div>
                </div>

                <div class="convert-section">
                    <button class="convert-button" id="convertBtn">
                        <span class="loading-spinner" id="loadingSpinner"></span>
                        <i class="fas fa-magic"></i>
                        <span id="buttonText">Chuyển Đổi Ngay</span>
                    </button>
                </div>

                <div class="result-section" id="resultSection" style="display: none;">
                    <div class="result-header">
                        <span class="result-title">
                            <i class="fas fa-file-text"></i>
                            Kết quả chuyển đổi
                        </span>
                        <button class="main-copy-button" id="copyBtn" disabled>
                            <i class="fas fa-copy"></i>
                            <span>Sao chép tất cả</span>
                        </button>
                    </div>
                    <div class="result-content" id="resultContent">
                        <div class="result-placeholder">
                            <i class="fas fa-arrow-up"></i>
                            <span>Nhập số ở trên và nhấn "Chuyển Đổi" để xem kết quả</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="examples-section">
            <div class="examples-header">
                <h3 class="examples-title">
                    <i class="fas fa-lightbulb"></i>
                    Ví dụ nhanh - Nhấn để thử
                </h3>
            </div>
            <div class="examples-grid">
                <div class="example-item" onclick="fillExample('10000')">
                    <div class="example-number">10.000</div>
                </div>
                <div class="example-item" onclick="fillExample('50000')">
                    <div class="example-number">50.000</div>
                </div>
                <div class="example-item" onclick="fillExample('150000')">
                    <div class="example-number">150.000</div>
                </div>
                <div class="example-item" onclick="fillExample('999999')">
                    <div class="example-number">999.999</div>
                </div>
                <div class="example-item" onclick="fillExample('1000000')">
                    <div class="example-number">1.000.000</div>
                </div>
            </div>

            
        </div>

            <footer class="seo-footer" style="background: #f8f9fa; border-radius: 17px 17px 0 0; padding: 10px; text-align: center; margin: 10px 0; margin-bottom: 0;">
              <div class="footer-content">
                <p style="margin-bottom: 10px; color: #666;">Công cụ chuyển đổi số thành chữ tiếng Việt hy vọng sẽ giúp ích được cho nhiều người</p>
                <p style="margin-bottom: 10px; color: #666;">Phát triển bởi <a style="color: #4285f4;" href="https://vutruso.com/">Vũ Trụ Số</a></p>
              </div>
            </footer>

    </div>

    <div class="toast" id="toast">
        <i class="fas fa-check-circle"></i>
        <span id="toastMessage"></span>
    </div>

    <script>
function _0x15d8(_0x2f8657,_0x18aa93){const _0xd10e31=_0xd10e();return _0x15d8=function(_0x15d88f,_0x7a971f){_0x15d88f=_0x15d88f-0x1dc;let _0x2f313c=_0xd10e31[_0x15d88f];return _0x2f313c;},_0x15d8(_0x2f8657,_0x18aa93);}const _0x5a8b8a=_0x15d8;(function(_0x43b685,_0x30ade3){const _0x1acbbe=_0x15d8,_0x15153d=_0x43b685();while(!![]){try{const _0x940c07=parseInt(_0x1acbbe(0x214))/0x1*(-parseInt(_0x1acbbe(0x21a))/0x2)+parseInt(_0x1acbbe(0x207))/0x3*(parseInt(_0x1acbbe(0x228))/0x4)+-parseInt(_0x1acbbe(0x24c))/0x5+parseInt(_0x1acbbe(0x221))/0x6*(-parseInt(_0x1acbbe(0x1eb))/0x7)+parseInt(_0x1acbbe(0x203))/0x8+-parseInt(_0x1acbbe(0x25a))/0x9*(-parseInt(_0x1acbbe(0x201))/0xa)+-parseInt(_0x1acbbe(0x229))/0xb;if(_0x940c07===_0x30ade3)break;else _0x15153d['push'](_0x15153d['shift']());}catch(_0xe68d31){_0x15153d['push'](_0x15153d['shift']());}}}(_0xd10e,0xaed5e));const VIETNAMESE_NUMBERS={'ones':['','một',_0x5a8b8a(0x237),'ba',_0x5a8b8a(0x1e5),_0x5a8b8a(0x218),_0x5a8b8a(0x22f),_0x5a8b8a(0x222),_0x5a8b8a(0x25c),_0x5a8b8a(0x25f)],'tens':['','','hai\x20mươi',_0x5a8b8a(0x254),'bốn\x20mươi',_0x5a8b8a(0x246),_0x5a8b8a(0x1ee),_0x5a8b8a(0x232),_0x5a8b8a(0x1f9),_0x5a8b8a(0x1e7)],'scales':['',_0x5a8b8a(0x241),'triệu','tỷ']},CONFIG={'MAX_DIGITS':0xc,'LOADING_DELAY':0x190,'TOAST_DURATION':0xbb8,'COPY_FEEDBACK_DURATION':0x7d0},TOAST_TYPES={'SUCCESS':_0x5a8b8a(0x23a),'ERROR':_0x5a8b8a(0x231),'WARNING':_0x5a8b8a(0x266)};class NumberTooLargeError extends Error{constructor(){const _0x38471c=_0x5a8b8a;super(_0x38471c(0x24b)),this[_0x38471c(0x25b)]=_0x38471c(0x26d);}}class InvalidNumberError extends Error{constructor(){const _0x392f6b=_0x5a8b8a;super(_0x392f6b(0x252)),this[_0x392f6b(0x25b)]=_0x392f6b(0x206);}}const debounce=(_0x387959,_0x4d7afd)=>{let _0x1ea2cf;return function _0x40a242(..._0x2f4665){const _0x100b5a=()=>{clearTimeout(_0x1ea2cf),_0x387959(..._0x2f4665);};clearTimeout(_0x1ea2cf),_0x1ea2cf=setTimeout(_0x100b5a,_0x4d7afd);};},sanitizeInput=_0x466591=>_0x466591['replace'](/\D/g,''),formatNumberWithDots=_0x2672e7=>_0x2672e7[_0x5a8b8a(0x20f)](/\B(?=(\d{3})+(?!\d))/g,'.'),escapeHtml=_0x14242c=>_0x14242c['replace'](/'/g,'\x5c\x27')[_0x5a8b8a(0x20f)](/"/g,'\x5c\x22');class VietnameseNumberConverter{static[_0x5a8b8a(0x21b)](_0x5a5bb7){const _0x1e3285=_0x5a8b8a;let _0x429b65='';const _0x8b4e9e=Math[_0x1e3285(0x21f)](_0x5a5bb7/0x64),_0x43a61d=_0x5a5bb7%0x64,_0x5e84bd=Math['floor'](_0x43a61d/0xa),_0xc8cb19=_0x43a61d%0xa;if(_0x8b4e9e>0x0){_0x429b65+=VIETNAMESE_NUMBERS[_0x1e3285(0x255)][_0x8b4e9e]+_0x1e3285(0x1f4);if(_0x43a61d>0x0)_0x429b65+='\x20';}if(_0x5e84bd>=0x2){_0x429b65+=VIETNAMESE_NUMBERS[_0x1e3285(0x24d)][_0x5e84bd];if(_0xc8cb19>0x0){if(_0xc8cb19===0x1)_0x429b65+=_0x1e3285(0x277);else _0xc8cb19===0x5&&_0x5e84bd>0x1?_0x429b65+=_0x1e3285(0x270):_0x429b65+='\x20'+VIETNAMESE_NUMBERS[_0x1e3285(0x255)][_0xc8cb19];}}else{if(_0x5e84bd===0x1){if(_0xc8cb19===0x0)_0x429b65+=_0x1e3285(0x1de);else _0xc8cb19===0x5?_0x429b65+='mười\x20lăm':_0x429b65+='mười\x20'+VIETNAMESE_NUMBERS[_0x1e3285(0x255)][_0xc8cb19];}else{if(_0xc8cb19>0x0){if(_0x8b4e9e>0x0&&_0xc8cb19===0x1)_0x429b65+=_0x1e3285(0x250);else _0x8b4e9e>0x0&&_0xc8cb19===0x5?_0x429b65+=_0x1e3285(0x269):_0x429b65+=VIETNAMESE_NUMBERS[_0x1e3285(0x255)][_0xc8cb19];}}}return _0x429b65;}static['convert'](_0x592d4e){const _0x3fe9f4=_0x5a8b8a;if(_0x592d4e===0x0)return _0x3fe9f4(0x1dd);const _0x2d0865=[];let _0x47bc5b=_0x592d4e;while(_0x47bc5b>0x0){_0x2d0865[_0x3fe9f4(0x257)](_0x47bc5b%0x3e8),_0x47bc5b=Math[_0x3fe9f4(0x21f)](_0x47bc5b/0x3e8);}let _0x4321bb='';for(let _0x32d964=_0x2d0865[_0x3fe9f4(0x1f5)]-0x1;_0x32d964>=0x0;_0x32d964--){if(_0x2d0865[_0x32d964]>0x0){if(_0x4321bb!=='')_0x4321bb+='\x20';_0x4321bb+=this[_0x3fe9f4(0x21b)](_0x2d0865[_0x32d964]),_0x32d964>0x0&&(_0x4321bb+='\x20'+VIETNAMESE_NUMBERS[_0x3fe9f4(0x1ea)][_0x32d964]);}}return _0x4321bb[_0x3fe9f4(0x24f)]();}static[_0x5a8b8a(0x219)](_0x16358e){const _0x1b6251=_0x5a8b8a;return _0x16358e['charAt'](0x0)[_0x1b6251(0x20e)]()+_0x16358e[_0x1b6251(0x25d)](0x1);}static[_0x5a8b8a(0x1dc)](_0x3f2c81){const _0x72c2a3=_0x5a8b8a;return _0x3f2c81[_0x72c2a3(0x1fa)]('\x20')[_0x72c2a3(0x23c)](_0x31fafe=>this['capitalizeFirstLetter'](_0x31fafe))['join']('\x20');}static['generateVariants'](_0x3e051b){const _0x246a94=_0x5a8b8a,_0x4cde3a=this[_0x246a94(0x274)](_0x3e051b);return[{'label':_0x246a94(0x24a),'text':_0x4cde3a,'id':_0x246a94(0x1fc)},{'label':'🔤\x20Chữ\x20hoa\x20đầu\x20từ','text':this['capitalizeWords'](_0x4cde3a),'id':_0x246a94(0x1ec)},{'label':'🔠\x20Chữ\x20hoa\x20toàn\x20bộ','text':_0x4cde3a['toUpperCase'](),'id':_0x246a94(0x1ed)}];}}class NumberConverterUI{constructor(){const _0x7541cb=_0x5a8b8a;this[_0x7541cb(0x233)]=this[_0x7541cb(0x247)](),this[_0x7541cb(0x1e1)]();}[_0x5a8b8a(0x247)](){const _0x58774d=_0x5a8b8a;return{'numberInput':document['getElementById'](_0x58774d(0x275)),'convertBtn':document[_0x58774d(0x216)](_0x58774d(0x213)),'loadingSpinner':document[_0x58774d(0x216)]('loadingSpinner'),'buttonText':document[_0x58774d(0x216)](_0x58774d(0x234)),'resultSection':document[_0x58774d(0x216)](_0x58774d(0x1fb)),'resultContent':document[_0x58774d(0x216)](_0x58774d(0x22b)),'copyBtn':document['getElementById'](_0x58774d(0x21c)),'toast':document[_0x58774d(0x216)](_0x58774d(0x23d)),'toastMessage':document[_0x58774d(0x216)]('toastMessage')};}[_0x5a8b8a(0x1e1)](){const _0x67000a=_0x5a8b8a;this[_0x67000a(0x233)][_0x67000a(0x213)][_0x67000a(0x264)](_0x67000a(0x225),()=>this[_0x67000a(0x242)]()),this[_0x67000a(0x233)][_0x67000a(0x275)][_0x67000a(0x264)](_0x67000a(0x1e4),_0x431456=>{const _0x1cf356=_0x67000a;_0x431456[_0x1cf356(0x204)]===_0x1cf356(0x26a)&&!_0x431456[_0x1cf356(0x265)]&&(_0x431456[_0x1cf356(0x259)](),this[_0x1cf356(0x242)]());}),this['elements']['numberInput'][_0x67000a(0x264)](_0x67000a(0x1e0),debounce(this[_0x67000a(0x1e2)][_0x67000a(0x245)](this),0x64)),this['elements'][_0x67000a(0x21c)]['addEventListener']('click',()=>this[_0x67000a(0x23e)]());}[_0x5a8b8a(0x1e2)](_0x229780){const _0x1616e7=_0x5a8b8a,_0x25bb73=_0x229780[_0x1616e7(0x20c)][_0x1616e7(0x239)];let _0x364399=sanitizeInput(_0x25bb73);_0x364399['length']>CONFIG[_0x1616e7(0x1f7)]&&(_0x364399=_0x364399[_0x1616e7(0x1e6)](0x0,CONFIG[_0x1616e7(0x1f7)])),_0x229780[_0x1616e7(0x20c)][_0x1616e7(0x239)]=formatNumberWithDots(_0x364399);}[_0x5a8b8a(0x271)](_0x9bb9b5){const _0x1e0012=_0x5a8b8a;_0x9bb9b5?(this[_0x1e0012(0x233)][_0x1e0012(0x213)]['disabled']=!![],this[_0x1e0012(0x233)][_0x1e0012(0x205)][_0x1e0012(0x267)][_0x1e0012(0x223)]=_0x1e0012(0x1f8),this[_0x1e0012(0x233)][_0x1e0012(0x234)]['textContent']=_0x1e0012(0x240)):(this[_0x1e0012(0x233)][_0x1e0012(0x213)][_0x1e0012(0x26e)]=![],this[_0x1e0012(0x233)][_0x1e0012(0x205)]['style'][_0x1e0012(0x223)]='none',this[_0x1e0012(0x233)][_0x1e0012(0x234)][_0x1e0012(0x26b)]=_0x1e0012(0x238));}async[_0x5a8b8a(0x242)](){const _0x248ceb=_0x5a8b8a,_0x1fd0f6=this[_0x248ceb(0x233)][_0x248ceb(0x275)][_0x248ceb(0x239)]['trim'](),_0x238765=sanitizeInput(_0x1fd0f6);this[_0x248ceb(0x271)](!![]);try{await new Promise(_0x5c8fbc=>setTimeout(_0x5c8fbc,CONFIG[_0x248ceb(0x260)]));if(!_0x238765)throw new InvalidNumberError();if(_0x238765['length']>CONFIG[_0x248ceb(0x1f7)])throw new NumberTooLargeError();const _0xbdccd0=parseInt(_0x238765),_0x4433d5=VietnameseNumberConverter[_0x248ceb(0x22c)](_0xbdccd0);this['showResult'](this[_0x248ceb(0x227)](_0x4433d5),_0x248ceb(0x273),TOAST_TYPES[_0x248ceb(0x200)]),this[_0x248ceb(0x233)][_0x248ceb(0x21c)]['disabled']=![],this['elements'][_0x248ceb(0x1fb)]['style'][_0x248ceb(0x223)]=_0x248ceb(0x248);}catch(_0x5a9b24){this[_0x248ceb(0x21e)]('',_0x5a9b24[_0x248ceb(0x256)],TOAST_TYPES['ERROR']),this['elements'][_0x248ceb(0x1fb)][_0x248ceb(0x267)]['display']=_0x248ceb(0x263);}finally{this[_0x248ceb(0x271)](![]);}}[_0x5a8b8a(0x227)](_0x1011c0){const _0x2d67f5=_0x5a8b8a;return _0x2d67f5(0x22d)+_0x1011c0[_0x2d67f5(0x23c)](_0x437c82=>_0x2d67f5(0x20d)+_0x437c82[_0x2d67f5(0x26c)]+'</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<button\x20class=\x22variant-copy-btn\x22\x20onclick=\x22ui.copyVariant(this,\x20\x27'+escapeHtml(_0x437c82['text'])+_0x2d67f5(0x20b)+_0x437c82['text']+_0x2d67f5(0x276))['join']('')+_0x2d67f5(0x1fd);}[_0x5a8b8a(0x21e)](_0x2f1e17,_0x636f29,_0x3e6a0b){const _0x38958e=_0x5a8b8a;_0x2f1e17?this[_0x38958e(0x233)][_0x38958e(0x22b)]['innerHTML']=_0x2f1e17:(this[_0x38958e(0x233)][_0x38958e(0x22b)]['innerHTML']=_0x38958e(0x268)+_0x636f29+'</span>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20',this[_0x38958e(0x233)][_0x38958e(0x21c)]['disabled']=!![]),this[_0x38958e(0x258)](_0x636f29,_0x3e6a0b);}async[_0x5a8b8a(0x211)](_0x33670f,_0x2a4b97){const _0x5659ea=_0x5a8b8a;try{await this['copyToClipboard'](_0x2a4b97),this[_0x5659ea(0x258)](_0x5659ea(0x261),TOAST_TYPES[_0x5659ea(0x200)]),this[_0x5659ea(0x20a)](_0x33670f);}catch(_0x50acce){this[_0x5659ea(0x258)](_0x5659ea(0x236),TOAST_TYPES['ERROR']);}}async[_0x5a8b8a(0x23e)](){const _0x555945=_0x5a8b8a,_0x4bdc15=document[_0x555945(0x215)](_0x555945(0x212));if(_0x4bdc15[_0x555945(0x1f5)]===0x0){this['showToast'](_0x555945(0x1e9),TOAST_TYPES[_0x555945(0x210)]);return;}const _0x172c4b=Array[_0x555945(0x209)](_0x4bdc15)[_0x555945(0x23c)]((_0x3dbbc4,_0x54af76)=>{const _0x51a2ff=_0x555945,_0x15c852=['Chữ\x20thường',_0x51a2ff(0x220),_0x51a2ff(0x1ef)];return _0x15c852[_0x54af76]+':\x20'+_0x3dbbc4[_0x51a2ff(0x26b)];})['join']('\x0a');try{await this[_0x555945(0x1e3)](_0x172c4b),this[_0x555945(0x258)](_0x555945(0x272),TOAST_TYPES['SUCCESS']),this[_0x555945(0x262)]();}catch(_0x435667){this['showToast'](_0x555945(0x236),TOAST_TYPES[_0x555945(0x253)]);}}async[_0x5a8b8a(0x1e3)](_0x53b561){const _0x1b950a=_0x5a8b8a;navigator['clipboard']&&window['isSecureContext']?await navigator[_0x1b950a(0x251)]['writeText'](_0x53b561):this['fallbackCopy'](_0x53b561);}[_0x5a8b8a(0x1f1)](_0x254bb7){const _0x195bd6=_0x5a8b8a,_0x20551e=document[_0x195bd6(0x1f2)](_0x195bd6(0x1ff));_0x20551e[_0x195bd6(0x239)]=_0x254bb7,_0x20551e[_0x195bd6(0x267)][_0x195bd6(0x23f)]=_0x195bd6(0x1f0),_0x20551e['style']['opacity']='0',document['body'][_0x195bd6(0x244)](_0x20551e),_0x20551e[_0x195bd6(0x243)]();const _0x32b4c6=document['execCommand']('copy');document['body'][_0x195bd6(0x249)](_0x20551e);if(!_0x32b4c6)throw new Error(_0x195bd6(0x1df));}[_0x5a8b8a(0x20a)](_0x58b462){const _0x307737=_0x5a8b8a,_0x171286=_0x58b462[_0x307737(0x235)];_0x58b462['innerHTML']=_0x307737(0x24e),_0x58b462[_0x307737(0x26f)]['add'](_0x307737(0x22e)),setTimeout(()=>{const _0x13ef9e=_0x307737;_0x58b462['innerHTML']=_0x171286,_0x58b462[_0x13ef9e(0x26f)][_0x13ef9e(0x208)]('copied');},CONFIG[_0x307737(0x226)]);}[_0x5a8b8a(0x262)](){const _0x585bf9=_0x5a8b8a,_0xaeaf7f=this[_0x585bf9(0x233)]['copyBtn'][_0x585bf9(0x235)];this['elements']['copyBtn'][_0x585bf9(0x235)]=_0x585bf9(0x1fe),setTimeout(()=>{const _0x2fe095=_0x585bf9;this['elements'][_0x2fe095(0x21c)][_0x2fe095(0x235)]=_0xaeaf7f;},CONFIG[_0x585bf9(0x226)]);}[_0x5a8b8a(0x258)](_0x27c6ec,_0x4b343f){const _0x12c8ee=_0x5a8b8a,_0x2ae01d=this[_0x12c8ee(0x233)][_0x12c8ee(0x23d)][_0x12c8ee(0x202)]('i');this[_0x12c8ee(0x233)]['toast']['className']=_0x12c8ee(0x23d),this['elements'][_0x12c8ee(0x23d)][_0x12c8ee(0x26f)][_0x12c8ee(0x224)](_0x4b343f);const _0x17b1ec={[TOAST_TYPES[_0x12c8ee(0x200)]]:'fas\x20fa-check-circle',[TOAST_TYPES['ERROR']]:_0x12c8ee(0x22a),[TOAST_TYPES['WARNING']]:_0x12c8ee(0x21d)};_0x2ae01d[_0x12c8ee(0x25e)]=_0x17b1ec[_0x4b343f]||_0x17b1ec[TOAST_TYPES[_0x12c8ee(0x200)]],this[_0x12c8ee(0x233)][_0x12c8ee(0x1e8)][_0x12c8ee(0x26b)]=_0x27c6ec,this[_0x12c8ee(0x233)]['toast'][_0x12c8ee(0x26f)]['add'](_0x12c8ee(0x1f6)),setTimeout(()=>{const _0x85e6d3=_0x12c8ee;this['elements'][_0x85e6d3(0x23d)]['classList'][_0x85e6d3(0x208)]('show');},CONFIG[_0x12c8ee(0x1f3)]);}[_0x5a8b8a(0x217)](_0x5c21a2){const _0x736861=_0x5a8b8a;this[_0x736861(0x233)][_0x736861(0x275)][_0x736861(0x239)]=formatNumberWithDots(_0x5c21a2),this[_0x736861(0x233)][_0x736861(0x275)][_0x736861(0x230)](),setTimeout(()=>{const _0x24eb1a=_0x736861;this[_0x24eb1a(0x242)]();},0x12c);}}let ui;document[_0x5a8b8a(0x264)](_0x5a8b8a(0x23b),()=>{ui=new NumberConverterUI();});function fillExample(_0x2d663a){ui&&ui['fillExample'](_0x2d663a);}function _0xd10e(){const _0x7da2a7=['43603VuWMFp','titlecase','uppercase','sáu\x20mươi','Chữ\x20hoa\x20toàn\x20bộ','fixed','fallbackCopy','createElement','TOAST_DURATION','\x20trăm','length','show','MAX_DIGITS','inline-block','tám\x20mươi','split','resultSection','lowercase','\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20','<i\x20class=\x22fas\x20fa-check\x22></i><span>Đã\x20sao\x20chép!</span>','textarea','SUCCESS','1181630JzFqhJ','querySelector','11027136WfanJo','key','loadingSpinner','InvalidNumberError','267321spHFJf','remove','from','showCopyFeedback','\x27)\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<i\x20class=\x22fas\x20fa-copy\x22></i>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</button>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22variant-text\x22>','target','\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22result-variant\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22variant-header\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22variant-label\x22>','toUpperCase','replace','WARNING','copyVariant','.variant-text','convertBtn','8cgbDlJ','querySelectorAll','getElementById','fillExample','năm','capitalizeFirstLetter','104508EQGDSC','convertHundreds','copyBtn','fas\x20fa-exclamation-triangle','showResult','floor','Chữ\x20hoa\x20đầu\x20từ','648bjYixa','bảy','display','add','click','COPY_FEEDBACK_DURATION','generateResultHTML','44xzgdfV','964887Pdsfoj','fas\x20fa-exclamation-circle','resultContent','generateVariants','\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22result-variants\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20','copied','sáu','focus','error','bảy\x20mươi','elements','buttonText','innerHTML','❌\x20Không\x20thể\x20sao\x20chép.\x20Vui\x20lòng\x20thử\x20lại!','hai','Chuyển\x20Đổi\x20Ngay','value','success','DOMContentLoaded','map','toast','copyAllResults','position','Đang\x20xử\x20lý...','nghìn','convertNumber','select','appendChild','bind','năm\x20mươi','cacheElements','block','removeChild','📝\x20Chữ\x20thường','Số\x20quá\x20lớn!\x20Vui\x20lòng\x20nhập\x20số\x20có\x20tối\x20đa\x2012\x20chữ\x20số','5273885CGJvZp','tens','<i\x20class=\x22fas\x20fa-check\x22></i>','trim','lẻ\x20một','clipboard','Vui\x20lòng\x20nhập\x20một\x20số\x20hợp\x20lệ','ERROR','ba\x20mươi','ones','message','push','showToast','preventDefault','45onkxOa','name','tám','slice','className','chín','LOADING_DELAY','📋\x20Đã\x20sao\x20chép!','showMainCopyFeedback','none','addEventListener','shiftKey','warning','style','\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22result-placeholder\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<i\x20class=\x22fas\x20fa-exclamation-triangle\x22></i>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span>','lẻ\x20năm','Enter','textContent','label','NumberTooLargeError','disabled','classList','\x20lăm','showLoading','📋\x20Đã\x20sao\x20chép\x20tất\x20cả\x20kết\x20quả!','✅\x20Chuyển\x20đổi\x20thành\x20công!','convert','numberInput','</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20','\x20một','capitalizeWords','không','mười','Copy\x20failed','input','initEventListeners','formatInput','copyToClipboard','keypress','bốn','substring','chín\x20mươi','toastMessage','⚠️\x20Chưa\x20có\x20kết\x20quả\x20để\x20sao\x20chép!','scales'];_0xd10e=function(){return _0x7da2a7;};return _0xd10e();}
    </script>
</body>
</html>