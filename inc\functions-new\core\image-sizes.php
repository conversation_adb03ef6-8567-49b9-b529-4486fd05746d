<?php
/**
 * VTS Image Sizes Configuration
 * 
 * Register and manage custom image sizes
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register custom image sizes
 */
function vts_register_thumbnail_sizes() {
    add_theme_support('post-thumbnails');

    // Archive thumbnail
    add_image_size('archive_thumb', 480, 300, true);
    
    // Add more custom sizes as needed
    // add_image_size('product_thumb', 363, 181, true);
    // add_image_size('related', 363, 181, true);
}
add_action('init', 'vts_register_thumbnail_sizes', 1);

/**
 * Remove unused WordPress image sizes
 */
function vts_disable_other_image_sizes() {
    remove_image_size('2048x2048');
    remove_image_size('1536x1536');
    remove_image_size('medium_large');
    remove_image_size('large');
    
    // Remove WooCommerce image sizes if not needed
    remove_image_size('woocommerce_single');
    remove_image_size('shop_single');
    remove_image_size('shop_catalog');
}
add_action('init', 'vts_disable_other_image_sizes', 9999);
