<?php
/*
 * Template Name: Bot Index Checker
 */

class AdvancedGoogleBotSimulator {
    
    private $googlebotVariants = [
        'desktop' => 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
        'mobile' => 'Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/W.X.Y.Z Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
        'image' => 'Googlebot-Image/1.0',
        'news' => 'Googlebot-News'
    ];
    
    private $timeout = 30;
    private $followRedirects = true;
    private $maxRedirects = 5;
    private $currentUserAgent = 'desktop';
    
    public function __construct($botType = 'desktop') {
        $this->currentUserAgent = $botType;
    }
    
    /**
     * <PERSON><PERSON>m tra khả năng index với thuật toán nâng cao
     */
    public function checkIndexability($url, $options = []) {
        $result = [
            'url' => $url,
            'indexable' => true,
            'confidence_level' => 'high', // high, medium, low
            'reasons' => [],
            'warnings' => [],
            'robots_txt' => null,
            'meta_robots' => null,
            'x_robots_tag' => null,
            'http_status' => null,
            'redirects' => [],
            'canonical_url' => null,
            'processing_time' => 0,
            'googlebot_type' => $this->currentUserAgent,
            'detected_blocks' => [],
            'crawl_budget_impact' => 'low'
        ];
        
        $startTime = microtime(true);
        
        try {
            // 1. Advanced URL validation
            $urlValidation = $this->validateUrl($url);
            if (!$urlValidation['valid']) {
                throw new Exception($urlValidation['error']);
            }
            
            // 2. Enhanced robots.txt check với full RFC compliance
            $robotsCheck = $this->advancedRobotsTxtCheck($url);
            $result['robots_txt'] = $robotsCheck;
            
            if (!$robotsCheck['allowed']) {
                $result['indexable'] = false;
                $result['reasons'][] = 'Bị chặn bởi robots.txt: ' . $robotsCheck['matched_rule'];
                $result['confidence_level'] = 'high';
                $result['detected_blocks'][] = 'robots_txt';
            }
            
            // 3. Advanced page fetching với error handling
            $pageData = $this->advancedFetchPage($url);
            $result['http_status'] = $pageData['http_code'];
            $result['redirects'] = $pageData['redirects'];
            
            // 4. Enhanced HTTP status handling với validation
            if ($pageData['http_code'] === 0 || empty($pageData['http_code'])) {
                $result['indexable'] = false;
                $result['reasons'][] = 'Không thể xác định HTTP status code';
                $result['confidence_level'] = 'low';
                return $result;
            }
            
            $statusResult = $this->analyzeHttpStatus($pageData['http_code']);
            if (!$statusResult['indexable']) {
                $result['indexable'] = false;
                $result['reasons'][] = $statusResult['reason'];
                $result['confidence_level'] = $statusResult['confidence'];
                $result['detected_blocks'][] = 'http_status';
                return $result;
            }
            
            if (!empty($statusResult['warnings'])) {
                $result['warnings'] = array_merge($result['warnings'], $statusResult['warnings']);
            }
            
            // 5. Advanced X-Robots-Tag parsing
            if (isset($pageData['headers']['x-robots-tag'])) {
                $xRobotsAnalysis = $this->parseAdvancedRobotsDirectives($pageData['headers']['x-robots-tag']);
                $result['x_robots_tag'] = $xRobotsAnalysis;
                
                if ($xRobotsAnalysis['blocks_indexing']) {
                    $result['indexable'] = false;
                    $result['reasons'][] = 'Bị chặn bởi X-Robots-Tag: ' . implode(', ', $xRobotsAnalysis['blocking_directives']);
                    $result['confidence_level'] = 'high';
                    $result['detected_blocks'][] = 'x_robots_tag';
                }
            }
            
            // 6. Advanced meta robots parsing với conflict detection
            if (!empty($pageData['content'])) {
                $metaAnalysis = $this->advancedMetaRobotsCheck($pageData['content']);
                $result['meta_robots'] = $metaAnalysis;
                
                if ($metaAnalysis['blocks_indexing']) {
                    $result['indexable'] = false;
                    $result['reasons'][] = 'Bị chặn bởi meta robots: ' . implode(', ', $metaAnalysis['blocking_directives']);
                    $result['confidence_level'] = 'high';
                    $result['detected_blocks'][] = 'meta_robots';
                }
                
                // 7. Advanced canonical analysis
                $canonicalAnalysis = $this->advancedCanonicalCheck($pageData['content'], $url);
                if ($canonicalAnalysis['found']) {
                    $result['canonical_url'] = $canonicalAnalysis['url'];
                    if ($canonicalAnalysis['is_self_referencing']) {
                        $result['warnings'][] = 'Có thẻ canonical trỏ về URL hiện tại (tốt)';
                    } else {
                        $result['warnings'][] = 'Canonical points to different URL: ' . $canonicalAnalysis['url'];
                        $result['confidence_level'] = 'medium';
                    }
                }
                
                // 8. Detect JavaScript-rendered content issues
                $jsAnalysis = $this->detectJavaScriptContentIssues($pageData['content']);
                if ($jsAnalysis['has_issues']) {
                    $result['warnings'] = array_merge($result['warnings'], $jsAnalysis['warnings']);
                    if ($result['confidence_level'] === 'high') {
                        $result['confidence_level'] = 'medium';
                    }
                }
            }
            
            // 9. Crawl budget impact analysis
            $result['crawl_budget_impact'] = $this->analyzeCrawlBudgetImpact($url, $pageData);
            
            // 10. Cross-check conflicts between different blocking methods
            $conflictAnalysis = $this->detectBlockingConflicts($result);
            if (!empty($conflictAnalysis)) {
                $result['warnings'] = array_merge($result['warnings'], $conflictAnalysis);
                $result['confidence_level'] = 'low';
            }
            
        } catch (Exception $e) {
            $result['indexable'] = false;
            $result['reasons'][] = 'Lỗi hệ thống: ' . $e->getMessage();
            $result['confidence_level'] = 'low';
            
            // Ensure we have some HTTP status for error cases
            if (!isset($result['http_status']) || $result['http_status'] === null) {
                $result['http_status'] = 'Error';
            }
        }
        
        $result['processing_time'] = round(microtime(true) - $startTime, 2);
        return $result;
    }
    
    /**
     * Advanced URL validation
     */
    private function validateUrl($url) {
        // Basic validation
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return ['valid' => false, 'error' => 'URL không hợp lệ'];
        }
        
        $parsed = parse_url($url);
        
        // Check for blocked schemes
        if (!in_array($parsed['scheme'], ['http', 'https'])) {
            return ['valid' => false, 'error' => 'Chỉ hỗ trợ HTTP/HTTPS'];
        }
        
        // Check for localhost/private IPs (could be blocked)
        $host = $parsed['host'];
        if (in_array($host, ['localhost', '127.0.0.1', '::1']) || 
            preg_match('/^(10\.|172\.(1[6-9]|2[0-9]|3[01])\.|192\.168\.)/', $host)) {
            return ['valid' => false, 'error' => 'Không thể truy cập localhost/private IP'];
        }
        
        return ['valid' => true];
    }
    
    /**
     * Advanced robots.txt parsing với RFC compliance
     */
    private function advancedRobotsTxtCheck($url) {
        $parsedUrl = parse_url($url);
        $robotsUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'] . '/robots.txt';
        
        $result = [
            'exists' => false,
            'allowed' => true,
            'matched_rule' => null,
            'content' => null,
            'url' => $robotsUrl,
            'parse_errors' => [],
            'crawl_delay' => null,
            'sitemap_found' => false,
            'user_agent_groups' => []
        ];
        
        try {
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $robotsUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => $this->timeout,
                CURLOPT_USERAGENT => $this->googlebotVariants[$this->currentUserAgent],
                CURLOPT_FOLLOWLOCATION => false,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_HEADER => true
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
            curl_close($ch);
            
            // Handle different HTTP status codes for robots.txt
            if ($httpCode === 404 || $httpCode === 410) {
                // No robots.txt = allow all
                return $result;
            }
            
            if ($httpCode >= 500) {
                // Server error = allow all (temporary)
                $result['parse_errors'][] = "Robots.txt server error ($httpCode) - assuming allowed";
                return $result;
            }
            
            if ($httpCode === 200 && $response) {
                $robotsContent = substr($response, $headerSize);
                $result['exists'] = true;
                $result['content'] = $robotsContent;
                
                // Advanced parsing
                $parseResult = $this->parseAdvancedRobotsTxt($robotsContent, $url);
                $result = array_merge($result, $parseResult);
            }
            
        } catch (Exception $e) {
            $result['parse_errors'][] = 'Error fetching robots.txt: ' . $e->getMessage();
        }
        
        return $result;
    }
    
    /**
     * Advanced robots.txt parser với full RFC support
     */
    private function parseAdvancedRobotsTxt($content, $url) {
        $result = [
            'allowed' => true,
            'matched_rule' => null,
            'parse_errors' => [],
            'crawl_delay' => null,
            'sitemap_found' => false,
            'user_agent_groups' => []
        ];
        
        // Normalize line endings và remove BOM
        $content = str_replace(["\r\n", "\r"], "\n", $content);
        $content = preg_replace('/^\xEF\xBB\xBF/', '', $content); // Remove UTF-8 BOM
        
        $lines = explode("\n", $content);
        $groups = [];
        $currentGroup = null;
        $lineNumber = 0;
        
        foreach ($lines as $line) {
            $lineNumber++;
            $originalLine = $line;
            
            // Remove comments
            $commentPos = strpos($line, '#');
            if ($commentPos !== false) {
                $line = substr($line, 0, $commentPos);
            }
            
            $line = trim($line);
            if (empty($line)) continue;
            
            // Parse directive
            $colonPos = strpos($line, ':');
            if ($colonPos === false) {
                $result['parse_errors'][] = "Line $lineNumber: No colon found in '$originalLine'";
                continue;
            }
            
            $directive = strtolower(trim(substr($line, 0, $colonPos)));
            $value = trim(substr($line, $colonPos + 1));
            
            switch ($directive) {
                case 'user-agent':
                    // Start new group
                    $currentGroup = [
                        'user_agents' => [strtolower($value)],
                        'rules' => [],
                        'crawl_delay' => null
                    ];
                    $groups[] = &$currentGroup;
                    break;
                    
                case 'disallow':
                case 'allow':
                    if ($currentGroup === null) {
                        $result['parse_errors'][] = "Line $lineNumber: $directive without user-agent";
                        continue 2;
                    }
                    $currentGroup['rules'][] = [
                        'type' => $directive,
                        'path' => $value,
                        'line' => $lineNumber
                    ];
                    break;
                    
                case 'crawl-delay':
                    if ($currentGroup === null) {
                        $result['parse_errors'][] = "Line $lineNumber: crawl-delay without user-agent";
                        continue 2;
                    }
                    if (is_numeric($value)) {
                        $currentGroup['crawl_delay'] = (float)$value;
                        $result['crawl_delay'] = (float)$value;
                    }
                    break;
                    
                case 'sitemap':
                    $result['sitemap_found'] = true;
                    break;
                    
                default:
                    $result['parse_errors'][] = "Line $lineNumber: Unknown directive '$directive'";
            }
        }
        
        $result['user_agent_groups'] = $groups;
        
        // Find matching group for current bot
        $botName = $this->getBotNameFromUserAgent();
        $matchingGroup = $this->findBestMatchingGroup($groups, $botName);
        
        if ($matchingGroup) {
            $pathToCheck = parse_url($url, PHP_URL_PATH) ?: '/';
            $checkResult = $this->checkPathAgainstRules($pathToCheck, $matchingGroup['rules']);
            
            $result['allowed'] = $checkResult['allowed'];
            $result['matched_rule'] = $checkResult['matched_rule'];
            
            if ($matchingGroup['crawl_delay']) {
                $result['crawl_delay'] = $matchingGroup['crawl_delay'];
            }
        }
        
        return $result;
    }
    
    /**
     * Find best matching user-agent group
     */
    private function findBestMatchingGroup($groups, $botName) {
        $wildcardGroup = null;
        $exactMatch = null;
        $partialMatches = [];
        
        foreach ($groups as $group) {
            foreach ($group['user_agents'] as $ua) {
                if ($ua === '*') {
                    $wildcardGroup = $group;
                } elseif ($ua === $botName) {
                    $exactMatch = $group;
                } elseif (strpos($botName, $ua) === 0) {
                    $partialMatches[] = ['group' => $group, 'length' => strlen($ua)];
                }
            }
        }
        
        // Return most specific match
        if ($exactMatch) return $exactMatch;
        
        if (!empty($partialMatches)) {
            // Sort by length descending (longest match first)
            usort($partialMatches, function($a, $b) {
                return $b['length'] - $a['length'];
            });
            return $partialMatches[0]['group'];
        }
        
        return $wildcardGroup;
    }
    
    /**
     * Check path against rules với wildcard support
     */
    private function checkPathAgainstRules($path, $rules) {
        $allowRules = [];
        $disallowRules = [];
        
        // Separate allow and disallow rules
        foreach ($rules as $rule) {
            if ($rule['type'] === 'allow') {
                $allowRules[] = $rule;
            } else {
                $disallowRules[] = $rule;
            }
        }
        
        // Check for matches
        $bestDisallowMatch = $this->findBestPathMatch($path, $disallowRules);
        $bestAllowMatch = $this->findBestPathMatch($path, $allowRules);
        
        // Apply precedence rules: longer path wins, allow beats disallow if same length
        if ($bestDisallowMatch && $bestAllowMatch) {
            $disallowLen = strlen($bestDisallowMatch['path']);
            $allowLen = strlen($bestAllowMatch['path']);
            
            if ($allowLen >= $disallowLen) {
                return ['allowed' => true, 'matched_rule' => "Allow: {$bestAllowMatch['path']}"];
            } else {
                return ['allowed' => false, 'matched_rule' => "Disallow: {$bestDisallowMatch['path']}"];
            }
        } elseif ($bestDisallowMatch) {
            return ['allowed' => false, 'matched_rule' => "Disallow: {$bestDisallowMatch['path']}"];
        } elseif ($bestAllowMatch) {
            return ['allowed' => true, 'matched_rule' => "Allow: {$bestAllowMatch['path']}"];
        }
        
        // Default is allowed
        return ['allowed' => true, 'matched_rule' => null];
    }
    
    /**
     * Find best path match với wildcard support
     */
    private function findBestPathMatch($path, $rules) {
        $bestMatch = null;
        $bestLength = -1;
        
        foreach ($rules as $rule) {
            if ($this->pathMatches($path, $rule['path'])) {
                $ruleLength = strlen(str_replace(['*', '$'], '', $rule['path']));
                if ($ruleLength > $bestLength) {
                    $bestMatch = $rule;
                    $bestLength = $ruleLength;
                }
            }
        }
        
        return $bestMatch;
    }
    
    /**
     * Check if path matches rule với wildcard support
     */
    private function pathMatches($path, $rulePattern) {
        // Convert robots.txt pattern to regex
        $pattern = preg_quote($rulePattern, '/');
        $pattern = str_replace('\*', '.*', $pattern);
        $pattern = str_replace('\$', '$', $pattern);
        
        return preg_match('/^' . $pattern . '/', $path) === 1;
    }
    
    /**
     * Get bot name from current user agent
     */
    private function getBotNameFromUserAgent() {
        switch ($this->currentUserAgent) {
            case 'mobile': return 'googlebot';
            case 'image': return 'googlebot-image';
            case 'news': return 'googlebot-news';
            default: return 'googlebot';
        }
    }
    
    /**
     * Advanced page fetching với comprehensive error handling và accurate status detection
     */
    private function advancedFetchPage($url) {
        $ch = curl_init();
        $redirects = [];
        $responseHeaders = [];
        $debugInfo = [];
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_USERAGENT => $this->googlebotVariants[$this->currentUserAgent],
            CURLOPT_FOLLOWLOCATION => $this->followRedirects,
            CURLOPT_MAXREDIRS => $this->maxRedirects,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HEADER => true,
            CURLOPT_NOBODY => false, // Ensure we get body content
            CURLOPT_ENCODING => 'gzip, deflate',
            CURLOPT_VERBOSE => false,
            CURLOPT_HEADERFUNCTION => function($ch, $header) use (&$redirects, &$responseHeaders) {
                $trimmed = trim($header);
                if (preg_match('/^Location:\s*(.+)$/i', $trimmed, $matches)) {
                    $redirects[] = trim($matches[1]);
                }
                if (strpos($trimmed, ':') !== false) {
                    list($key, $value) = explode(':', $trimmed, 2);
                    $responseHeaders[strtolower(trim($key))] = trim($value);
                }
                return strlen($header);
            }
        ]);
        
        $response = curl_exec($ch);
        
        // Get detailed curl info
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        $totalTime = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
        $curlError = curl_error($ch);
        $curlErrno = curl_errno($ch);
        $effectiveUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
        
        curl_close($ch);
        
        // Debug info for troubleshooting
        $debugInfo = [
            'curl_error' => $curlError,
            'curl_errno' => $curlErrno,
            'effective_url' => $effectiveUrl,
            'response_size' => strlen($response ?? ''),
            'header_size' => $headerSize
        ];
        
        // Handle CURL errors
        if ($curlErrno !== 0) {
            throw new Exception("CURL Error ($curlErrno): $curlError");
        }
        
        // Validate response
        if ($response === false) {
            throw new Exception("Failed to fetch URL: No response received");
        }
        
        // Extract content
        $content = '';
        $actualHeaders = [];
        
        if ($headerSize > 0 && strlen($response) >= $headerSize) {
            $headerText = substr($response, 0, $headerSize);
            $content = substr($response, $headerSize);
            
            // Parse headers more carefully
            $headerLines = explode("\r\n", $headerText);
            foreach ($headerLines as $line) {
                $line = trim($line);
                if (empty($line)) continue;
                
                // Parse status line
                if (preg_match('/^HTTP\/\d\.\d\s+(\d+)\s*(.*)$/', $line, $matches)) {
                    $actualHttpCode = (int)$matches[1];
                    // Use actual HTTP code from headers if different from curl_getinfo
                    if ($actualHttpCode !== $httpCode) {
                        $httpCode = $actualHttpCode;
                        $debugInfo['status_code_corrected'] = true;
                    }
                }
                
                // Parse header fields
                if (strpos($line, ':') !== false) {
                    list($key, $value) = explode(':', $line, 2);
                    $key = strtolower(trim($key));
                    $value = trim($value);
                    $actualHeaders[$key] = $value;
                    
                    // Also add to legacy array for backward compatibility
                    if (!isset($responseHeaders[$key])) {
                        $responseHeaders[$key] = $value;
                    }
                }
            }
        }
        
        // Final validation
        if ($httpCode === 0) {
            throw new Exception("Invalid HTTP status code received");
        }
        
        // Detect encoding and convert to UTF-8
        if (!empty($content)) {
            $content = $this->normalizeEncoding($content, $actualHeaders);
        }
        
        return [
            'content' => $content,
            'headers' => $actualHeaders,
            'http_code' => $httpCode,
            'redirects' => $redirects,
            'content_type' => $contentType,
            'total_time' => $totalTime,
            'debug_info' => $debugInfo,
            'success' => true
        ];
    }
    
    /**
     * Normalize content encoding
     */
    private function normalizeEncoding($content, $headers) {
        // Try to detect encoding from Content-Type header
        $encoding = 'UTF-8';
        if (isset($headers['content-type'])) {
            if (preg_match('/charset=([^;]+)/i', $headers['content-type'], $matches)) {
                $encoding = trim($matches[1], '"\'');
            }
        }
        
        // Try to detect from HTML meta tag
        if (preg_match('/<meta[^>]+charset[^>]*content[^>]*["\']([^"\']+)["\'][^>]*>/i', $content, $matches)) {
            $encoding = $matches[1];
        }
        
        // Convert to UTF-8 if needed
        if (strtoupper($encoding) !== 'UTF-8') {
            $converted = @iconv($encoding, 'UTF-8//IGNORE', $content);
            if ($converted !== false) {
                $content = $converted;
            }
        }
        
        return $content;
    }
    
    /**
     * Advanced HTTP status analysis với comprehensive status code handling
     */
    private function analyzeHttpStatus($httpCode) {
        $result = [
            'indexable' => true,
            'reason' => '',
            'confidence' => 'high',
            'warnings' => []
        ];
        
        // Convert to integer if string
        $httpCode = (int)$httpCode;
        
        // Validate status code range
        if ($httpCode < 100 || $httpCode > 599) {
            $result['indexable'] = false;
            $result['confidence'] = 'low';
            $result['reason'] = "HTTP $httpCode: Invalid status code";
            return $result;
        }
        
        if ($httpCode >= 400 && $httpCode < 500) {
            // Client errors - definitely not indexable
            $result['indexable'] = false;
            $result['confidence'] = 'high';
            
            switch ($httpCode) {
                case 400:
                    $result['reason'] = 'HTTP 400: Bad Request - URL không hợp lệ';
                    break;
                case 401:
                    $result['reason'] = 'HTTP 401: Unauthorized - Yêu cầu xác thực';
                    break;
                case 403:
                    $result['reason'] = 'HTTP 403: Forbidden - Bị chặn truy cập hoàn toàn';
                    break;
                case 404:
                    $result['reason'] = 'HTTP 404: Not Found - Không tìm thấy trang';
                    break;
                case 405:
                    $result['reason'] = 'HTTP 405: Method Not Allowed - Phương thức không được phép';
                    break;
                case 410:
                    $result['reason'] = 'HTTP 410: Gone - Trang đã bị xóa vĩnh viễn';
                    break;
                case 429:
                    $result['reason'] = 'HTTP 429: Too Many Requests - Bị rate limit';
                    $result['confidence'] = 'medium'; // Có thể tạm thời
                    break;
                default:
                    $result['reason'] = "HTTP $httpCode: Client Error - Lỗi từ phía client";
            }
        } elseif ($httpCode >= 500) {
            // Server errors - usually not indexable but might be temporary
            $result['indexable'] = false;
            $result['confidence'] = 'medium'; // Có thể tạm thời
            
            switch ($httpCode) {
                case 500:
                    $result['reason'] = "HTTP 500: Internal Server Error - Lỗi server nội bộ";
                    break;
                case 502:
                    $result['reason'] = "HTTP 502: Bad Gateway - Lỗi gateway";
                    break;
                case 503:
                    $result['reason'] = "HTTP 503: Service Unavailable - Dịch vụ tạm ngưng";
                    break;
                case 504:
                    $result['reason'] = "HTTP 504: Gateway Timeout - Gateway timeout";
                    break;
                default:
                    $result['reason'] = "HTTP $httpCode: Server Error - Lỗi server (có thể tạm thời)";
            }
        } elseif ($httpCode >= 300 && $httpCode < 400) {
            // Redirects - usually indexable but with warnings
            $result['warnings'][] = "HTTP $httpCode: Redirect detected";
            
            switch ($httpCode) {
                case 301:
                    $result['warnings'][] = '301 Moved Permanently - Có thể truyền link juice';
                    break;
                case 302:
                    $result['warnings'][] = '302 Found - Redirect tạm thời, có thể ảnh hưởng SEO';
                    break;
                case 303:
                    $result['warnings'][] = '303 See Other - Redirect sau POST request';
                    break;
                case 307:
                    $result['warnings'][] = '307 Temporary Redirect - Giữ nguyên HTTP method';
                    break;
                case 308:
                    $result['warnings'][] = '308 Permanent Redirect - Giữ nguyên HTTP method';
                    break;
                default:
                    $result['warnings'][] = "Redirect $httpCode - Kiểm tra chuỗi redirect";
            }
        } elseif ($httpCode >= 200 && $httpCode < 300) {
            // Success status - indexable
            if ($httpCode !== 200) {
                $result['warnings'][] = "HTTP $httpCode: Success but not standard 200";
            }
        } else {
            // 1xx Informational - unusual for final response
            $result['warnings'][] = "HTTP $httpCode: Informational status - không phải final response";
            $result['confidence'] = 'low';
        }
        
        return $result;
    }
    
    /**
     * Advanced robots directives parsing
     */
    private function parseAdvancedRobotsDirectives($content) {
        $directives = [];
        $parts = preg_split('/[,\s]+/', strtolower(trim($content)));
        
        foreach ($parts as $part) {
            $part = trim($part);
            if (!empty($part)) {
                $directives[] = $part;
            }
        }
        
        $result = [
            'directives' => $directives,
            'blocks_indexing' => false,
            'blocks_following' => false,
            'blocking_directives' => [],
            'conflicts' => []
        ];
        
        // Check for indexing blocks
        if (in_array('noindex', $directives)) {
            $result['blocks_indexing'] = true;
            $result['blocking_directives'][] = 'noindex';
        }
        
        // Check for following blocks
        if (in_array('nofollow', $directives)) {
            $result['blocks_following'] = true;
            $result['blocking_directives'][] = 'nofollow';
        }
        
        // Check for conflicts
        if (in_array('index', $directives) && in_array('noindex', $directives)) {
            $result['conflicts'][] = 'Conflicting index/noindex directives';
        }
        
        if (in_array('follow', $directives) && in_array('nofollow', $directives)) {
            $result['conflicts'][] = 'Conflicting follow/nofollow directives';
        }
        
        return $result;
    }
    
    /**
     * Advanced meta robots check
     */
    private function advancedMetaRobotsCheck($html) {
        $result = [
            'tags_found' => [],
            'blocks_indexing' => false,
            'blocks_following' => false,
            'blocking_directives' => [],
            'conflicts' => [],
            'multiple_tags' => false
        ];
        
        // Find all meta robots tags (including googlebot-specific)
        $patterns = [
            '/<meta\s+name=["\']robots["\']\s+content=["\']([^"\']+)["\']/i',
            '/<meta\s+name=["\']googlebot["\']\s+content=["\']([^"\']+)["\']/i',
            '/<meta\s+name=["\']googlebot-news["\']\s+content=["\']([^"\']+)["\']/i'
        ];
        
        $allDirectives = [];
        
        foreach ($patterns as $pattern) {
            preg_match_all($pattern, $html, $matches);
            foreach ($matches[1] as $content) {
                $parsed = $this->parseAdvancedRobotsDirectives($content);
                $result['tags_found'][] = [
                    'content' => $content,
                    'parsed' => $parsed
                ];
                $allDirectives = array_merge($allDirectives, $parsed['directives']);
            }
        }
        
        if (count($result['tags_found']) > 1) {
            $result['multiple_tags'] = true;
        }
        
        // Analyze combined directives
        $allDirectives = array_unique($allDirectives);
        
        if (in_array('noindex', $allDirectives)) {
            $result['blocks_indexing'] = true;
            $result['blocking_directives'][] = 'noindex';
        }
        
        if (in_array('nofollow', $allDirectives)) {
            $result['blocks_following'] = true;
            $result['blocking_directives'][] = 'nofollow';
        }
        
        // Check for conflicts across tags
        if (in_array('index', $allDirectives) && in_array('noindex', $allDirectives)) {
            $result['conflicts'][] = 'Conflicting index/noindex across multiple tags';
        }
        
        return $result;
    }
    
    /**
     * Advanced canonical URL analysis
     */
    private function advancedCanonicalCheck($html, $currentUrl) {
        $result = [
            'found' => false,
            'url' => null,
            'is_self_referencing' => false,
            'is_relative' => false,
            'issues' => []
        ];
        
        // Find canonical links
        preg_match_all('/<link\s+rel=["\']canonical["\']\s+href=["\']([^"\']+)["\']/i', $html, $matches);
        
        if (empty($matches[1])) {
            return $result;
        }
        
        if (count($matches[1]) > 1) {
            $result['issues'][] = 'Multiple canonical tags found - only first one used';
        }
        
        $canonicalUrl = $matches[1][0];
        $result['found'] = true;
        $result['url'] = $canonicalUrl;
        
        // Check if relative URL
        if (!preg_match('/^https?:\/\//', $canonicalUrl)) {
            $result['is_relative'] = true;
            $result['issues'][] = 'Canonical URL is relative - should be absolute';
            
            // Convert to absolute URL for comparison
            $parsedCurrent = parse_url($currentUrl);
            $baseUrl = $parsedCurrent['scheme'] . '://' . $parsedCurrent['host'];
            if (strpos($canonicalUrl, '/') === 0) {
                $canonicalUrl = $baseUrl . $canonicalUrl;
            } else {
                $canonicalUrl = $baseUrl . '/' . $canonicalUrl;
            }
        }
        
        // Check if self-referencing
        $normalizedCurrent = rtrim($currentUrl, '/');
        $normalizedCanonical = rtrim($canonicalUrl, '/');
        
        if ($normalizedCurrent === $normalizedCanonical) {
            $result['is_self_referencing'] = true;
        }
        
        return $result;
    }
    
    /**
     * Detect JavaScript content issues
     */
    private function detectJavaScriptContentIssues($html) {
        $result = [
            'has_issues' => false,
            'warnings' => [],
            'spa_detected' => false,
            'heavy_js_detected' => false
        ];
        
        // Check for SPA indicators
        $spaIndicators = [
            'angular', 'react', 'vue', 'ng-app', 'data-reactroot',
            'v-app', 'nuxt', 'next.js'
        ];
        
        // foreach ($spaIndicators as $indicator) {
        //     if (stripos($html, $indicator) !== false) {
        //         $result['spa_detected'] = true;
        //         $result['has_issues'] = true;
        //         $result['warnings'][] = 'SPA framework detected - ensure server-side rendering for SEO';
        //         break;
        //     }
        // }
        
        // Check for heavy JavaScript usage
        $jsScriptCount = substr_count(strtolower($html), '<script');
        if ($jsScriptCount > 20) {
            $result['heavy_js_detected'] = true;
            $result['has_issues'] = true;
            $result['warnings'][] = "Site có khá nhiều js ($jsScriptCount scripts) - có thể ảnh hưởng đến việc thu thập dữ liệu";
        }
        
        // Check for dynamic content loading
        // $dynamicIndicators = ['ajax', 'fetch(', 'XMLHttpRequest', 'document.write'];
        // foreach ($dynamicIndicators as $indicator) {
        //     if (stripos($html, $indicator) !== false) {
        //         $result['has_issues'] = true;
        //         $result['warnings'][] = 'Dynamic content loading detected - may not be visible to crawlers';
        //         break;
        //     }
        // }
        
        return $result;
    }
    
    /**
     * Analyze crawl budget impact
     */
    private function analyzeCrawlBudgetImpact($url, $pageData) {
        $score = 0;
        
        // Check page size
        $contentLength = strlen($pageData['content']);
        if ($contentLength > 1000000) { // > 1MB
            $score += 3;
        } elseif ($contentLength > 500000) { // > 500KB
            $score += 2;
        } elseif ($contentLength > 100000) { // > 100KB
            $score += 1;
        }
        
        // Check load time
        if ($pageData['total_time'] > 5) {
            $score += 3;
        } elseif ($pageData['total_time'] > 3) {
            $score += 2;
        } elseif ($pageData['total_time'] > 1) {
            $score += 1;
        }
        
        // Check for redirects
        if (!empty($pageData['redirects'])) {
            $score += count($pageData['redirects']);
        }
        
        // Determine impact level
        if ($score >= 5) return 'high';
        if ($score >= 3) return 'medium';
        return 'low';
    }
    
    /**
     * Detect conflicts between different blocking methods
     */
    private function detectBlockingConflicts($result) {
        $warnings = [];
        
        // Check if robots.txt allows but meta/headers block
        if ($result['robots_txt']['allowed'] && 
            ($result['meta_robots']['blocks_indexing'] ?? false)) {
            $warnings[] = 'Conflict: robots.txt allows crawling but meta robots blocks indexing';
        }
        
        if ($result['robots_txt']['allowed'] && 
            ($result['x_robots_tag']['blocks_indexing'] ?? false)) {
            $warnings[] = 'Conflict: robots.txt allows crawling but X-Robots-Tag blocks indexing';
        }
        
        // Check for multiple blocking methods
        $blockingMethods = [];
        if (!$result['robots_txt']['allowed']) $blockingMethods[] = 'robots.txt';
        if ($result['meta_robots']['blocks_indexing'] ?? false) $blockingMethods[] = 'meta robots';
        if ($result['x_robots_tag']['blocks_indexing'] ?? false) $blockingMethods[] = 'X-Robots-Tag';
        
        if (count($blockingMethods) > 1) {
            $warnings[] = 'Multiple blocking methods detected: ' . implode(', ', $blockingMethods);
        }
        
        return $warnings;
    }
    
    /**
     * Bulk check với progress tracking
     */
    public function bulkCheck($urls, $callback = null) {
        $results = [];
        
        foreach ($urls as $index => $url) {
            // Rotate bot types for more comprehensive testing
            $botTypes = ['desktop', 'mobile'];
            $this->currentUserAgent = $botTypes[$index % 2];
            
            $result = $this->checkIndexability($url);
            $results[] = $result;
            
            if ($callback && is_callable($callback)) {
                $callback($result, $index + 1, count($urls));
            }
            
            // Add delay để tránh rate limiting
            usleep(rand(300000, 700000)); // 0.3-0.7 giây random
        }
        
        return $results;
    }
    
    /**
     * Generate enhanced report
     */
    public function generateEnhancedReport($results) {
        $html = '<!DOCTYPE html><html><head><meta charset="UTF-8">';
        $html .= '<title>Advanced Google Bot Index Report</title>';
        $html .= '<style>
            body { font-family: "Segoe UI", sans-serif; margin: 20px; background: #f5f7fa; }
            .container { max-width: 1200px; margin: 0 auto; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 20px; }
            .result { background: white; border-radius: 8px; margin: 15px 0; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .indexable { border-left: 5px solid #28a745; }
            .not-indexable { border-left: 5px solid #dc3545; }
            .confidence-high { border-top: 3px solid #28a745; }
            .confidence-medium { border-top: 3px solid #ffc107; }
            .confidence-low { border-top: 3px solid #dc3545; }
            .success { color: #28a745; font-weight: bold; }
            .error { color: #dc3545; font-weight: bold; }
            .warning { color: #856404; background: #fff3cd; padding: 8px; border-radius: 4px; margin: 5px 0; }
            .details { margin-top: 15px; }
            .detail-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0; }
            .detail-box { background: #f8f9fa; padding: 15px; border-radius: 5px; }
            .badge { display: inline-block; padding: 4px 8px; border-radius: 12px; font-size: 0.8em; font-weight: bold; margin: 2px; }
            .badge-success { background: #d4edda; color: #155724; }
            .badge-danger { background: #f8d7da; color: #721c24; }
            .badge-warning { background: #fff3cd; color: #856404; }
            .stats { background: white; border-radius: 8px; padding: 20px; margin: 20px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
            .stat-card { text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px; }
            .stat-number { font-size: 2.5em; font-weight: bold; color: #667eea; }
            .crawl-impact-low { color: #28a745; }
            .crawl-impact-medium { color: #ffc107; }
            .crawl-impact-high { color: #dc3545; }
        </style></head><body>';
        
        $html .= '<div class="container">';
        $html .= '<div class="header">';
        $html .= '<h1>🔍 Advanced Google Bot Index Analysis Report</h1>';
        $html .= '<p>Comprehensive indexability analysis with edge case detection • Generated: ' . date('Y-m-d H:i:s') . '</p>';
        $html .= '</div>';
        
        // Statistics
        $total = count($results);
        $indexable = array_filter($results, fn($r) => $r['indexable']);
        $highConfidence = array_filter($results, fn($r) => $r['confidence_level'] === 'high');
        $robotsBlocked = array_filter($results, fn($r) => in_array('robots_txt', $r['detected_blocks'] ?? []));
        
        $html .= '<div class="stats">';
        $html .= '<h2>📊 Overview Statistics</h2>';
        $html .= '<div class="stats-grid">';
        $html .= '<div class="stat-card"><div class="stat-number">' . $total . '</div><div class="stat-label">Total URLs</div></div>';
        $html .= '<div class="stat-card"><div class="stat-number" style="color: #28a745;">' . count($indexable) . '</div><div class="stat-label">Indexable</div></div>';
        $html .= '<div class="stat-card"><div class="stat-number" style="color: #dc3545;">' . ($total - count($indexable)) . '</div><div class="stat-label">Blocked</div></div>';
        $html .= '<div class="stat-card"><div class="stat-number">' . round(count($indexable) / $total * 100, 1) . '%</div><div class="stat-label">Success Rate</div></div>';
        $html .= '<div class="stat-card"><div class="stat-number" style="color: #28a745;">' . count($highConfidence) . '</div><div class="stat-label">High Confidence</div></div>';
        $html .= '<div class="stat-card"><div class="stat-number" style="color: #dc3545;">' . count($robotsBlocked) . '</div><div class="stat-label">Robots.txt Blocked</div></div>';
        $html .= '</div></div>';
        
        // Detailed results
        $html .= '<h2>🔍 Detailed Analysis Results</h2>';
        
        foreach ($results as $result) {
            $statusClass = $result['indexable'] ? 'indexable' : 'not-indexable';
            $confidenceClass = 'confidence-' . $result['confidence_level'];
            $statusText = $result['indexable'] ? 
                '<span class="success">✅ INDEXABLE</span>' : 
                '<span class="error">❌ BLOCKED</span>';
            
            $html .= "<div class='result $statusClass $confidenceClass'>";
            
            // Header
            $html .= "<div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;'>";
            $html .= "<h3 style='margin: 0; word-break: break-all;'>{$result['url']}</h3>";
            $html .= "<div>$statusText</div>";
            $html .= "</div>";
            
            // Confidence and bot type
            $html .= "<div style='margin-bottom: 15px;'>";
            $html .= "<span class='badge badge-" . ($result['confidence_level'] === 'high' ? 'success' : ($result['confidence_level'] === 'medium' ? 'warning' : 'danger')) . "'>Độ tin cậy: " . ucfirst($result['confidence_level']) . "</span>";
            $html .= "<span class='badge badge-info' style='background: #d1ecf1; color: #0c5460;'>Bot: " . ucfirst($result['googlebot_type']) . "</span>";
            $html .= "<span class='badge crawl-impact-{$result['crawl_budget_impact']}'>Crawl Impact: " . ucfirst($result['crawl_budget_impact']) . "</span>";
            $html .= "</div>";
            
            // Details grid
            $html .= "<div class='detail-grid'>";
            
            // Left column
            $html .= "<div class='detail-box'>";
            $html .= "<h4>🌐 Technical Details</h4>";
            $html .= "<p><strong>HTTP Status:</strong> {$result['http_status']}</p>";
            $html .= "<p><strong>Processing Time:</strong> {$result['processing_time']}s</p>";
            if ($result['robots_txt']) {
                $robotsStatus = $result['robots_txt']['allowed'] ? '✅ Allowed' : '❌ Blocked';
                $html .= "<p><strong>Robots.txt:</strong> $robotsStatus</p>";
                if ($result['robots_txt']['crawl_delay']) {
                    $html .= "<p><strong>Crawl Delay:</strong> {$result['robots_txt']['crawl_delay']}s</p>";
                }
            }
            $html .= "</div>";
            
            // Right column
            $html .= "<div class='detail-box'>";
            $html .= "<h4>🏷️ Meta & Headers</h4>";
            if ($result['meta_robots']) {
                $metaStatus = $result['meta_robots']['blocks_indexing'] ? '❌ Blocks' : '✅ Allows';
                $html .= "<p><strong>Meta Robots:</strong> $metaStatus</p>";
                if (!empty($result['meta_robots']['blocking_directives'])) {
                    $html .= "<p><strong>Directives:</strong> " . implode(', ', $result['meta_robots']['blocking_directives']) . "</p>";
                }
            }
            if ($result['canonical_url']) {
                $html .= "<p><strong>Canonical:</strong> Found</p>";
            }
            $html .= "</div>";
            
            $html .= "</div>";
            
            // Reasons for blocking
            if (!empty($result['reasons'])) {
                $html .= "<div class='details'>";
                $html .= "<h4>🚫 Blocking Reasons:</h4>";
                foreach ($result['reasons'] as $reason) {
                    $html .= "<div class='warning'>$reason</div>";
                }
                $html .= "</div>";
            }
            
            // Warnings
            if (!empty($result['warnings'])) {
                $html .= "<div class='details'>";
                $html .= "<h4>⚠️ Warnings & Issues:</h4>";
                foreach ($result['warnings'] as $warning) {
                    $html .= "<div class='warning'>$warning</div>";
                }
                $html .= "</div>";
            }
            
            $html .= "</div>";
        }
        
        $html .= '</div></body></html>';
        
        return $html;
    }
}

// Xử lý AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    if ($_POST['action'] === 'check_url') {
        $url = trim($_POST['url'] ?? '');
        $botType = $_POST['bot_type'] ?? 'desktop';
        
        if (empty($url)) {
            echo json_encode(['error' => 'Vui lòng nhập URL']);
            exit;
        }
        
        if (!preg_match('/^https?:\/\//', $url)) {
            $url = 'https://' . $url;
        }
        
        $simulator = new AdvancedGoogleBotSimulator($botType);
        $result = $simulator->checkIndexability($url);
        
        echo json_encode($result);
        exit;
    }
    
    if ($_POST['action'] === 'bulk_check') {
        $urls = array_filter(array_map('trim', explode("\n", $_POST['urls'] ?? '')));
        
        if (empty($urls)) {
            echo json_encode(['error' => 'Vui lòng nhập ít nhất một URL']);
            exit;
        }
        
        $urls = array_slice($urls, 0, 10);
        
        $simulator = new AdvancedGoogleBotSimulator();
        $results = [];
        
        foreach ($urls as $index => $url) {
            if (!preg_match('/^https?:\/\//', $url)) {
                $url = 'https://' . $url;
            }
            $results[] = $simulator->checkIndexability($url);
        }
        
        echo json_encode($results);
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Bot Index Checker - Công cụ kiểm tra xem website có bị chặn robot index hay không</title>
    <meta name="description" content="Công cụ kiểm tra Google Bot index nâng cao giúp kiểm tra xem website có bị chặn robot index hay không?">
    <meta name="keywords" content="google bot checker, robots.txt analyzer, seo audit tool, indexability check, crawl budget optimization, technical seo">
    <meta name="robots" content="index, follow">
    <meta property="og:url" content="https://vutruso.com/bot-index-checker/">
    <link rel="canonical" href="https://vutruso.com/bot-index-checker/">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="Google Bot Index Checker - Công Cụ Kiểm Tra Indexability Website">
    <meta property="og:description" content="Công cụ kiểm tra Google Bot index nâng cao giúp kiểm tra xem website có bị chặn robot index hay không?">
    <meta property="og:url" content="https://vutruso.com/bot-index-checker/">
    <meta property="og:image" content="https://vutruso.com/wp-content/uploads/2025/07/advanced-google-bot-index-checker.png">
    <meta property="og:image:secure_url" content="https://vutruso.com/wp-content/uploads/2025/07/advanced-google-bot-index-checker.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:title" content="Google Bot Index Checker - Công cụ kiểm tra xem website có bị chặn robot index hay không">
    <meta property="twitter:description" content="Công cụ kiểm tra Google Bot index nâng cao giúp kiểm tra xem website có bị chặn robot index hay không?">
    <meta name="twitter:image" content="https://vutruso.com/wp-content/uploads/2025/07/advanced-google-bot-index-checker.png">
    
    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "Google Bot Index Checker - Công cụ kiểm tra xem website có bị chặn robot index hay không",
        "description": "Google Bot Index Checker - Công cụ kiểm tra xem website có bị chặn robot, status index hay không",
        "applicationCategory": "SEO Tool",
        "operatingSystem": "Web Browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "featureList": [
            "Google Bot Simulation",
            "Robots.txt Analysis", 
            "Meta Robots Check",
            "Crawl Budget Analysis",
            "HTTP Status Validation",
            "Multiple Bot Types Support"
        ]
    }
    </script>
<style> * {margin: 0;padding: 0;box-sizing: border-box;}body {font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);min-height: 100vh;padding: 20px;}h2{margin-bottom: 15px;}.container {max-width: 1200px;margin: 0 auto;background: white;border-radius: 15px;box-shadow: 0 20px 40px rgba(0,0,0,0.1);overflow: hidden;}.header {background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);color: white;padding: 30px;text-align: center;}.header h1 {font-size: 2.5em;margin-bottom: 10px;font-weight: 300;}.header p {font-size: 1.1em;opacity: 0.9;}.result-details h4 {margin-bottom: 15px;}.result-details ul {margin-left: 25px;}.main-content {padding: 40px;}.tabs {display: flex;margin-bottom: 30px;border-bottom: 2px solid #f0f0f0;}.tab {padding: 15px 30px;background: none;border: none;font-size: 1.1em;cursor: pointer;border-bottom: 3px solid transparent;transition: all 0.3s ease;}.tab.active {color: #4285f4;border-bottom-color: #4285f4;font-weight: 600;}.tab-content {display: none;}.tab-content.active {display: block;}.form-group {margin-bottom: 25px;}.form-group label {display: block;margin-bottom: 8px;font-weight: 600;color: #333;}.form-control {width: 100%;padding: 15px;border: 2px solid #e0e0e0;border-radius: 8px;font-size: 1em;transition: all 0.3s ease;}.form-control:focus {outline: none;border-color: #4285f4;box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);}.form-row {display: grid;grid-template-columns: 2fr 1fr;gap: 15px;align-items: end;}textarea.form-control {resize: vertical;min-height: 120px;}.btn {background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);color: white;padding: 15px 30px;border: none;border-radius: 8px;font-size: 1.1em;cursor: pointer;transition: all 0.3s ease;display: inline-flex;align-items: center;gap: 10px;}.btn:hover {transform: translateY(-2px);box-shadow: 0 10px 20px rgba(0,0,0,0.1);}.btn:disabled {opacity: 0.6;cursor: not-allowed;transform: none;}.spinner {width: 20px;height: 20px;border: 2px solid #f3f3f3;border-top: 2px solid #4285f4;border-radius: 50%;animation: spin 1s linear infinite;margin: 0;}@keyframes spin {0% {transform: rotate(0deg);}100% {transform: rotate(360deg);}}.results {display: none;margin-top: 30px;}.result-item {border: 1px solid #e0e0e0;border-radius: 8px;margin-bottom: 20px;overflow: hidden;transition: all 0.3s ease;}.result-item:hover {box-shadow: 0 5px 15px rgba(0,0,0,0.1);}.result-item.indexable {border-left: 5px solid #28a745;}.result-item.not-indexable {border-left: 5px solid #dc3545;}.result-header {padding: 20px;background: #f8f9fa;border-bottom: 1px solid #e0e0e0;}.result-url {font-size: 1.2em;font-weight: 600;color: #333;margin-bottom: 10px;word-break: break-all;}.result-status {display: inline-block;padding: 8px 16px;border-radius: 20px;font-weight: 600;font-size: 0.9em;}.status-indexable {background: #d4edda;color: #155724;}.status-blocked {background: #f8d7da;color: #721c24;}.confidence-badge {margin-left: 10px;padding: 4px 8px;border-radius: 12px;font-size: 0.8em;}.confidence-high {background: #d4edda;color: #155724;}.confidence-medium {background: #fff3cd;color: #856404;}.confidence-low {background: #f8d7da;color: #721c24;}.result-details {padding: 20px;}.detail-grid {display: grid;grid-template-columns: 1fr 1fr;gap: 20px;margin: 15px 0;margin-top: 0;padding-top: 0;}.detail-section p {margin-bottom: 5px;}.detail-section {background: #f8f9fa;padding: 15px;border-radius: 5px;}.detail-section h4 {margin-bottom: 10px;color: #333;}.reason-item, .warning-item {padding: 10px 15px;border-radius: 5px;margin-bottom: 8px;}.reason-item {background: #f8d7da;color: #721c24;border-left: 4px solid #dc3545;margin-top: 10px;}.warning-item {background: #fff3cd;color: #856404;border-left: 4px solid #ffc107;}.stats {background: #f8f9fa;border-radius: 8px;padding: 20px;margin-top: 20px;display: none;}.stats-grid {display: grid;grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));gap: 15px;}.stat-item {text-align: center;padding: 15px;background: white;border-radius: 5px;border: 1px solid #e0e0e0;}.stat-number {font-size: 2em;font-weight: bold;color: #4285f4;}.feature-highlight {background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);color: white;padding: 20px;border-radius: 8px;margin: 20px 0;}.feature-grid {display: grid;grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));gap: 15px;margin-top: 15px;}.feature-item {text-align: center;padding: 15px 5px;background: rgba(255,255,255,0.1);border-radius: 5px;}.test-btn {display: inline-block;padding: 6px 12px;margin: 3px;background: #007bff;color: white;border: none;border-radius: 4px;cursor: pointer;font-size: 0.85em;transition: background 0.2s;}.test-btn:hover {background: #0056b3;}details {margin-top: 10px;}summary {cursor: pointer;font-weight: bold;padding: 5px;background: #e9ecef;border-radius: 4px;}.seo-content {background: white;border-radius: 8px;padding: 30px;margin: 30px 0;box-shadow: 0 2px 10px rgba(0,0,0,0.1);line-height: 1.6;}.seo-content h2 {color: #4285f4;border-bottom: 3px solid #4285f4;padding-bottom: 10px;margin-bottom: 20px;}.seo-content h3 {color: #333;margin: 25px 0 15px 0;font-size: 1.3em;}.seo-content p {margin-bottom: 15px;color: #555;}.seo-content ul, .seo-content ol {margin: 15px 0;padding-left: 25px;}.seo-content li {margin-bottom: 8px;color: #555;}.seo-content strong {color: #333;font-weight: 600;}.seo-content em {background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);color: white;padding: 15px;border-radius: 8px;display: block;margin-top: 20px;font-style: normal;}.seo-footer {background: #f8f9fa;border-radius: 8px;padding: 20px;margin-top: 20px;text-align: center;border-top: 3px solid #4285f4;}.footer-content p {margin-bottom: 10px;color: #666;}.footer-links {margin-top: 15px;font-size: 0.9em;}.footer-links a {color: #4285f4;text-decoration: none;margin: 0 5px;}.footer-links a:hover {text-decoration: underline;}@media (max-width: 768px) {.form-row, .detail-grid {grid-template-columns: 1fr;}.seo-footer { padding: 10px 5px; }.form-row {gap: 10px; }.tab { padding: 15px 10px;}.main-content {padding: 10px;}.header { padding: 15px 10px; }.seo-content {padding: 10px;margin: 20px 0;}.seo-content h2 {font-size: 1.5em;}.seo-content h3 {font-size: 1.2em;}.seo-footer {padding: 15px;margin-top: 15px;}.footer-links {font-size: 0.8em;}}</style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Advanced Google Bot Index Checker</h1>
            <p>Công cụ kiểm tra Google Bot index nâng cao giúp kiểm tra xem website có bị chặn robot index hay không</p>
        </div>
        
        <div class="main-content">
            <!-- Feature Highlight -->
            <div class="feature-highlight">
                <h3>🚀 Advanced Features</h3>
                <div class="feature-grid">
                    <div class="feature-item">
                        <strong>🤖 Multi-Bot Testing</strong><br>
                        Desktop, Mobile, Image crawlers
                    </div>
                    <div class="feature-item">
                        <strong>📋 RFC Compliance</strong><br>
                        Full robots.txt specification
                    </div>
                    <div class="feature-item">
                        <strong>⚡ Conflict Detection</strong><br>
                        Identify blocking conflicts
                    </div>
                    <div class="feature-item">
                        <strong>🎯 Confidence Scoring</strong><br>
                        High/Medium/Low reliability
                    </div>
                </div>
            </div>
            
            <div class="tabs">
                <button class="tab active" onclick="switchTab('single')">Kiểm tra nâng cao</button>
                <button class="tab" onclick="switchTab('bulk')">Phân tích hàng loạt</button>
            </div>
            
            <!-- Single URL Check -->
            <div id="single-tab" class="tab-content active">
                <form id="single-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="single-url">🌐 URL Website</label>
                            <input type="url" id="single-url" class="form-control" 
                                   placeholder="Nhập URL website (vd: https://vutruso.com)" required>
                        </div>
                        <div class="form-group">
                            <label for="bot-type">🤖 Bot Type</label>
                            <select id="bot-type" class="form-control">
                                <option value="desktop">Desktop Googlebot</option>
                                <option value="mobile">Mobile Googlebot</option>
                                <option value="image">Googlebot-Image</option>
                                <option value="news">Googlebot-News</option>
                            </select>
                        </div>
                    </div>
                    <button type="submit" class="btn">
                        <span id="single-btn-text">🔍 Phân tích</span>
                        <div id="single-spinner" class="spinner" style="display: none;"></div>
                    </button>
                </form>
            </div>
            
            <!-- Bulk URL Check -->
            <div id="bulk-tab" class="tab-content">
                <form id="bulk-form">
                    <div class="form-group">
                        <label for="bulk-urls">📋 Danh sách URL (mỗi URL một dòng, tối đa 10)</label>
                        <textarea id="bulk-urls" class="form-control" 
                                  placeholder="Nhập danh sách URL, mỗi URL một dòng:&#10;https://example.com&#10;https://example.com/about&#10;https://example.com/contact" required></textarea>
                    </div>
                    <button type="submit" class="btn">
                        <span id="bulk-btn-text">🚀 Phân tích tất cả</span>
                        <div id="bulk-spinner" class="spinner" style="display: none;"></div>
                    </button>
                </form>
            </div>
            
            <!-- Results -->
            <div id="results" class="results"></div>
            
            <!-- Stats -->
            <div id="stats" class="stats"></div>
            
            <!-- SEO Content Section -->
            <div class="seo-content">
                <h2>🔍 Google Bot Index Checker - Công Cụ Kiểm Tra Khả Năng Index Website</h2>
                
                <p><strong>Google Bot Index Checker</strong> là công cụ SEO chuyên nghiệp giúp bạn phân tích khả năng index của website trên Google. Tool này mô phỏng chính xác cách Google Bot crawl và đánh giá trang web của bạn.</p>
                
                <h3>📋 Tại Sao Cần Kiểm Tra Indexability?</h3>
                <p>Kiểm tra <strong>robots.txt</strong>, meta robots tag và X-Robots-Tag header là yếu tố quan trọng trong <strong>SEO audit</strong>. Nếu Google Bot không thể truy cập trang web, website sẽ không xuất hiện trên kết quả tìm kiếm, làm giảm traffic organic.</p>
                
                <h3>🤖 Công Cụ Phân Tích Googlebot Simulator</h3>
                <p>Tool hỗ trợ nhiều loại Google crawler:</p>
                <ul>
                    <li><strong>Desktop Googlebot</strong> - Crawler chính cho desktop search</li>
                    <li><strong>Mobile Googlebot</strong> - Crawler cho mobile-first indexing</li>
                    <li><strong>Googlebot-Image</strong> - Crawler cho Google Images</li>
                    <li><strong>Googlebot-News</strong> - Crawler cho Google News</li>
                </ul>
                
                <h3>🔧 Các Vấn Đề Thường Gặp</h3>
                <p>Công cụ phát hiện các lỗi SEO phổ biến:</p>
                <ul>
                    <li><strong>Robots.txt blocking</strong> - File robots.txt chặn Google Bot</li>
                    <li><strong>Meta noindex</strong> - Thẻ meta robots có directive noindex</li>
                    <li><strong>HTTP errors</strong> - Lỗi 404, 403, 500 ngăn indexing</li>
                    <li><strong>Canonical conflicts</strong> - Canonical URL không phù hợp</li>
                    <li><strong>JavaScript rendering issues</strong> - Content dynamic không được crawl</li>
                </ul>
                
                <h3>💡 Cách Sử Dụng Hiệu Quả</h3>
                <p>Để tối ưu <strong>crawl budget</strong> và cải thiện SEO:</p>
                <ol>
                    <li>Kiểm tra trang chủ và landing pages quan trọng</li>
                    <li>Phân tích robots.txt compliance</li>
                    <li>Xác minh meta robots directives</li>
                    <li>Kiểm tra mobile-first indexing readiness</li>
                    <li>Monitor redirect chains và page load speed</li>
                </ol>
                
                <h3>📊 Technical SEO Audit</h3>
                <p>Tool cung cấp <strong>confidence scoring</strong> giúp đánh giá độ tin cậy của kết quả. Sử dụng thường xuyên để monitoring website health và đảm bảo Google có thể crawl toàn bộ nội dung quan trọng.</p>
                
                <p><em>Công cụ miễn phí này giúp SEO specialists, web developers và digital marketers thực hiện indexability audit chuyên nghiệp, phát hiện blocking issues và tối ưu hóa website cho search engines.</em></p>
            </div>
            
            <!-- Footer -->
            <footer class="seo-footer">
                <div class="footer-content">
                    <p><strong>🔧 Công Cụ SEO Miễn Phí</strong> | Kiểm tra indexability, robots.txt analyzer, crawl budget optimization</p>
                    <p>Phát triển bởi <a style="color: #4285f4;" href="https://vutruso.com/">Vũ Trụ Số</a> để hỗ trợ cộng đồng webmaster Việt Nam. Tool được cập nhật thường xuyên theo Google guidelines.</p>
                    <div class="footer-links">
                        <span>📚 Related: </span>
                        <a href="#" onclick="testUrl('https://www.google.com/robots.txt')">Google Robots.txt</a> | 
                        <a href="#" onclick="testUrl('https://developers.google.com')">Google Developers</a> |
                        <a href="#" onclick="switchTab('bulk')">Bulk Analysis</a> 
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <script>
        function testUrl(url) {
            document.getElementById('single-url').value = url;
            switchTab('single');
            // Optionally auto-submit
            // document.getElementById('single-form').dispatchEvent(new Event('submit'));
        }
        
        function switchTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
            
            document.getElementById('results').style.display = 'none';
            document.getElementById('stats').style.display = 'none';
        }
        
        document.getElementById('single-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const url = document.getElementById('single-url').value.trim();
            const botType = document.getElementById('bot-type').value;
            
            if (!url) return;
            
            await checkSingleUrl(url, botType);
        });
        
        document.getElementById('bulk-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const urls = document.getElementById('bulk-urls').value.trim();
            if (!urls) return;
            
            await checkBulkUrls(urls);
        });
        
        async function checkSingleUrl(url, botType) {
            showLoading('single');
            
            try {
                const response = await fetch('', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=check_url&url=${encodeURIComponent(url)}&bot_type=${encodeURIComponent(botType)}`
                });
                
                const result = await response.json();
                
                if (result.error) {
                    alert(result.error);
                    return;
                }
                
                displayAdvancedResults([result]);
                
            } catch (error) {
                alert('Có lỗi xảy ra: ' + error.message);
            } finally {
                hideLoading('single');
            }
        }
        
        async function checkBulkUrls(urls) {
            showLoading('bulk');
            
            try {
                const response = await fetch('', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=bulk_check&urls=${encodeURIComponent(urls)}`
                });
                
                const results = await response.json();
                
                if (results.error) {
                    alert(results.error);
                    return;
                }
                
                displayAdvancedResults(results);
                displayAdvancedStats(results);
                
            } catch (error) {
                alert('Có lỗi xảy ra: ' + error.message);
            } finally {
                hideLoading('bulk');
            }
        }
        
        function showLoading(type) {
            const btnText = document.getElementById(type + '-btn-text');
            const spinner = document.getElementById(type + '-spinner');
            const form = document.getElementById(type + '-form');
            
            btnText.style.display = 'none';
            spinner.style.display = 'block';
            form.querySelector('button').disabled = true;
            
            document.getElementById('results').style.display = 'none';
            document.getElementById('stats').style.display = 'none';
        }
        
        function hideLoading(type) {
            const btnText = document.getElementById(type + '-btn-text');
            const spinner = document.getElementById(type + '-spinner');
            const form = document.getElementById(type + '-form');
            
            btnText.style.display = 'inline';
            spinner.style.display = 'none';
            form.querySelector('button').disabled = false;
        }
        
        function displayAdvancedResults(results) {
            const resultsDiv = document.getElementById('results');
            let html = '<h2>📊 Advanced Analysis Results</h2>';
            
            results.forEach(result => {
                const statusClass = result.indexable ? 'indexable' : 'not-indexable';
                const statusText = result.indexable ? '✅ INDEXABLE' : '❌ BLOCKED';
                const confidenceClass = 'confidence-' + result.confidence_level;
                const confidenceText = result.confidence_level.charAt(0).toUpperCase() + result.confidence_level.slice(1);
                
                html += `
                    <div class="result-item ${statusClass}">
                        <div class="result-header">
                            <div class="result-url">${result.url}</div>
                            <div>
                                <span class="result-status ${result.indexable ? 'status-indexable' : 'status-blocked'}">${statusText}</span>
                                <span style="display:none" class="confidence-badge ${confidenceClass}">Độ tin cậy: ${confidenceText}</span>
                            </div>
                        </div>
                        <div class="result-details">
                            <div class="detail-grid">
                                <div class="detail-section">
                                    <h4>🌐 Technical Analysis</h4>
                                    <ul>
                                    <li><strong>HTTP Status:</strong> ${result.http_status || 'N/A'}</li>
                                    <li><strong>Bot Type:</strong> ${result.googlebot_type}</li>
                                    <li><strong>Processing Time:</strong> ${result.processing_time}s</li>
                                    <li><strong>Crawl Budget Impact:</strong> <span class="crawl-impact-${result.crawl_budget_impact}">${result.crawl_budget_impact}</span></li>
                                    </ul>

                `;
                
                if (result.robots_txt) {
                    const robotsStatus = result.robots_txt.allowed ? '✅ Allowed' : '❌ Blocked';
                    html += `<ul><li><strong>Robots.txt:</strong> ${robotsStatus}</li>`;
                    if (result.robots_txt.crawl_delay) {
                        html += `<li><strong>Crawl Delay:</strong> ${result.robots_txt.crawl_delay}s</li><ul>`;
                    }
                }
                
                html += `</div><div class="detail-section"><h4>🏷️ Meta & Headers</h4>`;
                
                if (result.meta_robots) {
                    const metaStatus = result.meta_robots.blocks_indexing ? '❌ Blocks' : '✅ Allows';
                    html += `<ul><li><strong>Meta Robots:</strong> ${metaStatus}</li>`;
                    if (result.meta_robots.multiple_tags) {
                        html += `<li><strong>Multiple Tags:</strong> ⚠️ Yes</li></ul>`;
                    }
                }
                
                if (result.x_robots_tag) {
                    const xRobotsStatus = result.x_robots_tag.blocks_indexing ? '❌ Blocks' : '✅ Allows';
                    html += `<li><strong>X-Robots-Tag:</strong> ${xRobotsStatus}</li>`;
                }
                
                if (result.canonical_url) {
                    html += `<li><strong>Canonical URL:</strong> Có tìm thấy</li>`;
                }
                
                html += `</div></div>`;
                
                // Blocking reasons
                if (result.reasons && result.reasons.length > 0) {
                    html += '<h4>🚫 Blocking Reasons:</h4>';
                    result.reasons.forEach(reason => {
                        html += `<div class="reason-item">${reason}</div>`;
                    });
                }
                
                // Warnings
                if (result.warnings && result.warnings.length > 0) {
                    html += '<h4>⚠️ Warnings & Issues:</h4>';
                    result.warnings.forEach(warning => {
                        html += `<div class="warning-item">${warning}</div>`;
                    });
                }
                
                html += '</div></div>';
            });
            
            resultsDiv.innerHTML = html;
            resultsDiv.style.display = 'block';
        }
        
        function displayAdvancedStats(results) {
            const statsDiv = document.getElementById('stats');
            const total = results.length;
            const indexable = results.filter(r => r.indexable).length;
            const highConfidence = results.filter(r => r.confidence_level === 'high').length;
            const robotsBlocked = results.filter(r => r.detected_blocks && r.detected_blocks.includes('robots_txt')).length;
            const metaBlocked = results.filter(r => r.detected_blocks && r.detected_blocks.includes('meta_robots')).length;
            const avgProcessingTime = (results.reduce((sum, r) => sum + r.processing_time, 0) / total).toFixed(2);
            
            const html = `
                <h3>📈 Advanced Statistics</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">${total}</div>
                        <div class="stat-label">Total URLs</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" style="color: #28a745;">${indexable}</div>
                        <div class="stat-label">Indexable</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" style="color: #28a745;">${highConfidence}</div>
                        <div class="stat-label">High Confidence</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" style="color: #dc3545;">${robotsBlocked}</div>
                        <div class="stat-label">Robots.txt Blocks</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" style="color: #dc3545;">${metaBlocked}</div>
                        <div class="stat-label">Meta Blocks</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${avgProcessingTime}s</div>
                        <div class="stat-label">Avg Processing</div>
                    </div>
                </div>
            `;
            
            statsDiv.innerHTML = html;
            statsDiv.style.display = 'block';
        }
    </script>
</body>
</html>