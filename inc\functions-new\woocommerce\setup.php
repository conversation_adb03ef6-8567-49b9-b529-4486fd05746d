<?php
/**
 * VTS WooCommerce Setup
 * 
 * Basic WooCommerce setup and configuration
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add WooCommerce support
 */
function vts_woocommerce_support() {
    add_theme_support('woocommerce');
}
add_action('after_setup_theme', 'vts_woocommerce_support');

/**
 * Disable WooCommerce Blocks
 */
function vts_disable_woocommerce_blocks() {
    // Disable WooCommerce Blocks
    add_filter('woocommerce_blocks_enabled', '__return_false');

    // Disable block editor for WooCommerce
    add_filter('woocommerce_admin_disabled', '__return_true');

    // Disable specific block features
    add_filter('woocommerce_admin_features', function($features) {
        return array_diff($features, [
            'product-block-editor',
            'checkout-blocks',
            'cart-blocks'
        ]);
    });
}
add_action('init', 'vts_disable_woocommerce_blocks');

/**
 * Advanced WooCommerce Blocks disabling
 */
function vts_advanced_disable_wc_blocks() {
    // Remove WooCommerce Blocks
    if (class_exists('Automattic\WooCommerce\Packages')) {
        remove_action('init', 'wc_blocks_register_assets');
        remove_action('enqueue_block_assets', 'wc_blocks_enqueue_assets');

        // Disable loading of WooCommerce Blocks
        if (!is_admin() || (defined('DOING_AJAX') && DOING_AJAX)) {
            add_filter('woocommerce_blocks_enabled', '__return_false');
        }
    }
}
add_action('plugins_loaded', 'vts_advanced_disable_wc_blocks', 5);

/**
 * Remove WooCommerce Blocks package
 */
function vts_remove_wc_blocks_package() {
    if (!class_exists('Automattic\WooCommerce\Packages')) {
        return;
    }
    
    // Keep dependencies for Woo admin screens
    if (!empty($_GET['page']) && is_admin() && str_starts_with($_GET['page'], 'wc-')) {
        return;
    }
    
    $override = new class() extends Automattic\WooCommerce\Packages {
        public function __construct() {
        }
        public function remove_woocommerce_blocks(): void {
            unset(static::$packages['woocommerce-blocks']);
        }
    };
    $override->remove_woocommerce_blocks();
}
add_action('plugins_loaded', 'vts_remove_wc_blocks_package', 6);

/**
 * Remove block widgets registration
 */
function vts_remove_block_widgets() {
    remove_theme_support('widgets-block-editor');
}
add_action('after_setup_theme', 'vts_remove_block_widgets');

/**
 * Disable WooCommerce block styles (front-end)
 */
function vts_disable_woocommerce_block_styles() {
    wp_dequeue_style('wc-blocks-style');
}
add_action('wp_enqueue_scripts', 'vts_disable_woocommerce_block_styles');

/**
 * Disable WooCommerce block styles (back-end)
 */
function vts_disable_woocommerce_block_editor_styles() {
    wp_deregister_style('wc-block-editor');
    wp_deregister_style('wc-blocks-style');
}
add_action('enqueue_block_assets', 'vts_disable_woocommerce_block_editor_styles', 1, 1);

/**
 * Remove WooCommerce styles
 */
function vts_remove_woo_styles() {
    wp_deregister_style('woocommerce-general'); // the main CSS file
    wp_deregister_style('woocommerce-smallscreen'); // for max-width: 768px
    wp_deregister_style('woocommerce-layout'); // layout only
}
add_action('wp_enqueue_scripts', 'vts_remove_woo_styles', 20);

/**
 * Disable WooCommerce Marketplace
 */
function vts_disable_woocommerce_marketplace() {
    add_filter('woocommerce_allow_marketplace_suggestions', '__return_false');
    add_filter('woocommerce_show_marketplace_suggestions', function ($show) { 
        return 'no'; 
    });
}
add_action('init', 'vts_disable_woocommerce_marketplace');

/**
 * Remove WooCommerce admin submenus
 */
function vts_remove_woo_admin_submenus() {
    remove_submenu_page('woocommerce', 'wc-status');
    remove_submenu_page('woocommerce', 'wc-addons');
    remove_submenu_page('woocommerce', 'wc-addons&section=helper');
    remove_submenu_page('woocommerce', 'wc-reports');
}
add_action('admin_menu', 'vts_remove_woo_admin_submenus', 999);

/**
 * Disable WooCommerce dashboard status widget
 */
function vts_disable_woocommerce_status() {
    remove_meta_box('woocommerce_dashboard_status', 'dashboard', 'normal');
    remove_meta_box('woocommerce_dashboard_recent_reviews', 'dashboard', 'normal');
}
add_action('wp_dashboard_setup', 'vts_disable_woocommerce_status');

/**
 * Remove the generated product schema markup from Product Category and Shop pages
 */
function vts_remove_product_schema() {
    remove_action('woocommerce_shop_loop', [WC()->structured_data, 'generate_product_data'], 10, 0);
}
add_action('woocommerce_init', 'vts_remove_product_schema');

/**
 * Remove WooCommerce breadcrumb
 */
function vts_remove_woocommerce_breadcrumb() {
    remove_action('woocommerce_before_main_content', 'woocommerce_breadcrumb', 20);
}
add_action('init', 'vts_remove_woocommerce_breadcrumb');

/**
 * Remove WooCommerce noscript
 */
function vts_remove_wc_noscript() {
    remove_action('wp_head', 'wc_gallery_noscript');
}
add_action('init', 'vts_remove_wc_noscript');

/**
 * Remove woocommerce-no-js class
 */
function vts_remove_woocommerce_no_js($classes) {
    remove_action('wp_footer', 'wc_no_js');
    $classes = array_diff($classes, ['woocommerce-no-js']);
    return array_values($classes);
}
add_filter('body_class', 'vts_remove_woocommerce_no_js', 10, 1);
