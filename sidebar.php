<?php 
if(is_single()){
    global $post;
    $category_detail = get_the_category($post->ID);
    
    foreach($category_detail as $cd){
        $main_cat = $cd->slug;
        break;
    }
}
?>

<div class="blog-content-sidebar post_sidebar col-lg-4 stickySidebar px-1" itemscope
    itemtype="http://schema.org/WPSideBar">
    <div class="blog-sidebar">
        <aside class="widget widget_theme_vtspoststabs">
            <div class="vts_posts-tabs">
                <div class="tabs-body">
                    <div class="tabbable-responsive">
                        <div class="tabbable">
                            <ul class="vts_tabs nav nav-tabs" id="myTab" role="tablist">
                                <li data-tab="tab-bai-viet-moi" class="tab-link active show">Bài viết mới</li>
                                <li data-tab="tab-bai-viet-hot" class="tab-link">Bài viết hot</li>
                            </ul>
                        </div>
                    </div>

                    <div class="card-body">
                        <div class="tab-content">

                            <!-- New Posts Tab -->
                            <div id="tab-bai-viet-moi" class="tab-pane fade show active">
                                <?php 
                                $thamso = array(
                                    'post_type'               => 'post',
                                    'orderby'                 => 'date',
                                    'post_status'             => 'publish',
                                    'category_name'           => $main_cat,
                                    'posts_per_page'          => 6,
                                    'post__not_in'            => array($post->ID),
                                    'update_post_meta_cache'  => false,
                                    'update_post_term_cache'  => false,
                                    'ignore_sticky_posts'     => true,
                                    'no_found_rows'           => true
                                );
                                $new_posts = new WP_Query($thamso);
                                
                                if($new_posts->have_posts()) :
                                    while($new_posts->have_posts()) : $new_posts->the_post();
                                        $featured_img_url = get_the_post_thumbnail_url(get_the_ID(),'full');
                                ?>
                                <div <?php post_class('vts_posts-tabs-related_posts'); ?>>
                                    <div>
                                        <a title="<?php the_title(); ?>" itemprop="url"
                                            href="<?php the_permalink(); ?>">
                                            <div class="search-thumbnail"
                                                style="background-image: url('<?php echo esc_url($featured_img_url); ?>');">
                                            </div>
                                        </a>
                                        <div>
                                            <a itemprop="url" href="<?php the_permalink(); ?>"
                                                class="vts_posts-tabs-title">
                                                <span itemprop="name"><?php the_title(); ?></span>
                                            </a>
                                            <div class="vts_posts-tabs-footer">
                                                <a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>"
                                                    class="vts_posts-tabs-post-author">
                                                    <?php the_author(); ?>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <?php 
                                    endwhile;
                                    wp_reset_postdata();
                                ?>

                                <?php endif; ?>
                            </div>

                            <!-- Hot Posts Tab -->
                            <div id="tab-bai-viet-hot" class="tab-pane fade">
                                <?php  
                                $sticky = get_option('sticky_posts');
                                $args = array(
                                    'post_type'      => 'post',
                                    'orderby'        => 'rand',
                                    'posts_per_page' => 6,
                                    'post__in'       => $sticky,
                                    'post_status'    => 'publish',
                                );
                                $query = new WP_Query($args);
                                
                                if($query->have_posts()) :
                                    while($query->have_posts()) : $query->the_post();
                                        $featured_img_url = get_the_post_thumbnail_url(get_the_ID(),'full');
                                ?>
                                <div <?php post_class('vts_posts-tabs-related_posts'); ?>>
                                    <div>
                                        <a itemprop="url" href="<?php the_permalink(); ?>">
                                            <div class="search-thumbnail"
                                                style="background-image: url('<?php echo esc_url($featured_img_url); ?>');">
                                            </div>
                                        </a>
                                        <div>
                                            <a title="<?php the_title(); ?>" itemprop="url"
                                                href="<?php the_permalink(); ?>" class="vts_posts-tabs-title">
                                                <span itemprop="name"><?php the_title(); ?></span>
                                            </a>
                                            <div class="vts_posts-tabs-footer">
                                                <a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>"
                                                    class="vts_posts-tabs-post-author">
                                                    <?php the_author(); ?>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php 
                                    endwhile;
                                    wp_reset_postdata();
                                endif; 
                                ?>
                            </div>

                        </div>
                    </div>
                    
                </div>
            </div>
        </aside>

        <!-- Popular Tags Section -->
        <aside class="widget widget_theme_vtspopulartags">
            <h3 class="widget-title">Từ khóa phổ biến</h3>
            <div class="vts_popular-tags">
                <p class="vts_description"></p>
                <div class="tags-body">
                    <?php
                    $tags = get_terms('post_tag', array(
                        'orderby'  => 'count',
                        'order'    => 'DESC',
                        'number'   => 4,
                        'parent'   => 0
                    ));
                    
                    $i = 0;
                    foreach ($tags as $tag) :
                        if ($i >= 12) break;
                    ?>
                    <div class="tag-item">
                        <a href="<?php echo get_home_url(); ?>/tag/<?php echo $tag->slug; ?>/">
                            # <?php echo $tag->name; ?>
                        </a>
                    </div>
                    <?php
                        $i++;
                    endforeach;
                    ?>
                </div>
            </div>
        </aside>
    </div>
</div>