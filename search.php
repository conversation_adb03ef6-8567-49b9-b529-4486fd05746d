<?php get_header(); ?>

<div class="innerContainer">
  <section class="news posts-grid layout-medium" style="margin-top: 2%;">
    <div class="text-left">
      <h1 class="title-search">
        <?php echo __('<PERSON><PERSON><PERSON> quả tìm kiếm với từ khóa', 'vutruso'); ?> "<?php echo get_search_query(); ?>"
      </h1>
    </div>

    <?php while(have_posts()) : the_post(); ?>
    <article <?php post_class('item post-item'); ?>>
      <div class="post-wrapper">
        <div class="post-media post-grid-media">
          <a href="<?php the_permalink(); ?>">
            <?php if(has_post_thumbnail()){ the_post_thumbnail('medium', array('class'=>'pic', 'title' => get_the_title(), 'alt' => get_the_title(), 'itemprop' => 'image')); } else { ?>
                  <img height="199"  width="354" src="<?php echo get_template_directory_uri(); ?>/screenshot.jpg" alt="<?php the_title(); ?>" />
            <?php } ?>
          </a>
        </div>
        <div class="post-info">
          <h3 class="post-title">
            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
          </h3>
          <div class="post-content"><p class="excerpt"><?php echo get_the_excerpt(); ?></p></div>
          <div class="post-info-footer">
            <div class="post-meta-wrapper">
              <div class="post-meta">
                <div class="post-meta-item post-author">
                    <span class="post-author-avatar"><?php echo get_avatar( get_the_author_meta( 'ID' ), 32 ); ?></span>
                    <span class="post-author-name"><?php the_author(); ?></span>                      
                </div>
              </div>
              <div class="post-info-footer-divider"></div>
            </div>
            <div class="read-more-wrapper">
              <a href="<?php the_permalink(); ?>" class="read-more"><?php echo __('Đọc chi tiết', 'vutruso') ?></a>
            </div>
          </div>
        </div>
    </article>
    <?php endwhile; ?><?php vts_page_navi(); ?>
  </section>
</div>

<style>
  header #menu .menu ul li ul {
    display: none;
    position: absolute;
    top: 55px;
    left: -41px;
    width: 400px;
    transition: all .3s;
    z-index: 999;
}
  .title-search{
    font-weight: 700;
    border-radius: 4px;
    line-height: 24px;
    padding-left: 17px;
    -webkit-box-shadow: 0 1px 15px rgba(17,21,25,.42);
    -moz-box-shadow: 0 1px 15px rgba(17,21,25,.42);
    box-shadow: 0 1px 15px rgba(17,21,25,.42);
    text-transform: uppercase;
    margin-bottom: 3%;
    padding: 5px 10px;
    font-size: 13px;
  }

	.page-navigation{margin: 10px 0;display: block;overflow: hidden;}
	.pagination li{float:left}
	.pagination .current, .pagination li a {
		color: #fff;
		display: block;
		float: left;
		padding: 1px 16px !important;
		border-radius: 2px;
		text-align: center;
		margin: 0 5px 3px 0 !important;
		transition: all .3s;
		font-weight: 700;
		border: 0 !important;
		background: #227cd4;
		font-size: 18px;
	}

.pagination .current {background: #4b6f84;border-radius: 0 !important;}
.excerpt {line-height: 20px;font-size: 15px;}
.news.posts-grid .item .post-wrapper .post-info .post-title,
.news.posts-grid .item .post-wrapper .post-info .post-title a,
.news.posts-grid .item .post-wrapper .post-info .read-more-wrapper .read-more,
.news.posts-grid .item .post-wrapper .post-author .post-author-name a {
  color: #142b5f;
  font-family: initial;
	line-height:27px;
}
.post-meta-item:before {background-color: #142b5f;}
.post-meta-wrapper > * a:not(.read-more):before {color: rgba(31, 90, 188, 0.7);}
.news.posts-grid
  .item
  .post-wrapper
  .post-info
  .read-more-wrapper
  .read-more:before {
  color: rgb(64, 166, 255);
}
.layout-medium .item .post-content:after {background-color: rgb(64, 166, 255);}
.post-meta-wrapper .post-author .post-author-avatar img {background-color: rgba(64, 166, 255, 0.25);}
.news.posts-grid
  .item
  .post-wrapper
  .post-info
  .post-info-footer
  .post-info-footer-divider {
  background-color: rgba(26, 57, 127, 0.2);
}
.post-meta-wrapper {
  font-size: 15px;
  line-height: 19px;
  display: -webkit-box;
  display: -moz-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -moz-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-justify-content: normal;
  -moz-justify-content: normal;
  -ms-justify-content: normal;
  justify-content: normal;
  -webkit-align-items: flex-start;
  -moz-align-items: flex-start;
  -ms-align-items: flex-start;
  align-items: flex-start;
}
.post-meta-wrapper .post-meta-item {position: relative;margin: 5px 14px 0 0;}
.post-meta-wrapper .post-meta-item:not(:first-child) {padding: 0 0 0 20px;}
.post-meta-wrapper .post-meta-item:not(:first-child):before {
  content: "";
  position: absolute;
  display: block;
  width: 5px;
  height: 5px;
  -webkit-border-radius: 50%;
  border-radius: 50%;
  left: 0;
  right: auto;
  top: 6px;
  opacity: 0.25;
}
.post-meta-wrapper .post-meta-item a {color: inherit;}
.post-author .post-author-avatar {display: inline-block;vertical-align: top;margin: -8px 15px 0 0;}
.post-author .post-author-avatar a {display: block;}
.post-author .post-author-avatar .avatar {display: block;-webkit-border-radius: 50%;border-radius: 50%;}
.post-author .post-author-name {display: inline-block;vertical-align: top;line-height: 19px;}
.post-author .post-author-name a {line-height: 19px;}
.item .post-wrapper .post-media .pic { line-height: 0; font-size: 0; display: block; margin: 0 auto;}
.item .post-wrapper .post-media .pic img {display: block;}
.news.posts-grid.layout-medium
  .item:not(.related-item)
  .post-wrapper
  .post-media
  .pic
  img {
  -webkit-border-radius: 15px;
  border-radius: 15px;
}
.news.posts-grid.layout-medium
  .item:not(.related-item)
  .post-wrapper
  .post-info {
  position: relative;
}
.news.posts-grid.layout-medium
  .item:not(.related-item)
  .post-wrapper
  .post-info
  .post-title {
  font-size: 24px;
  font-weight: 800;
  word-break: break-word;
}
.news.posts-grid.layout-medium
  .item:not(.related-item)
  .post-wrapper
  .post-info
  .post-content {
  margin-top: 20px;
}
.news.posts-grid.layout-medium
  .item:not(.related-item)
  .post-wrapper
  .post-info
  .post-content:empty {
  margin-top: 0 !important;
}
.news.posts-grid.layout-medium
  .item:not(.related-item)
  .post-wrapper
  .post-info
  .post-info-footer {
  margin: 30px 0 0;
  display: -webkit-box;
  display: -moz-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -moz-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-wrap: nowrap;
  -moz-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-justify-content: space-between;
  -moz-justify-content: space-between;
  -ms-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: flex-start;
  -moz-align-items: flex-start;
  -ms-align-items: flex-start;
  align-items: flex-start;
}
.news.posts-grid.layout-medium
  .item:not(.related-item)
  .post-wrapper
  .post-info
  .post-info-footer
  .post-meta-wrapper {
  display: -webkit-box;
  display: -moz-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -moz-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-wrap: nowrap;
  -moz-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-justify-content: space-between;
  -moz-justify-content: space-between;
  -ms-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: flex-start;
  -moz-align-items: flex-start;
  -ms-align-items: flex-start;
  align-items: flex-start;
  width: 100%;
}
.news.posts-grid.layout-medium
  .item:not(.related-item)
  .post-wrapper
  .post-info
  .post-info-footer
  .post-meta-wrapper
  .post-meta {
  max-width: calc(100% - 70px);
  display: -webkit-box;
  display: -moz-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -moz-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-justify-content: flex-start;
  -moz-justify-content: flex-start;
  -ms-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: flex-start;
  -moz-align-items: flex-start;
  -ms-align-items: flex-start;
  align-items: flex-start;
  -webkit-flex-shrink: 0;
  -moz-flex-shrink: 0;
  -ms-flex-shrink: 0;
  flex-shrink: 0;
}
.news.posts-grid.layout-medium
  .item:not(.related-item)
  .post-wrapper
  .post-info
  .post-info-footer
  .post-meta-wrapper
  .post-info-footer-divider {
  min-width: 30px;
  width: 100%;
  height: 1px;
  margin: 13px 20px 10px 16px;
}
.news.posts-grid.layout-medium
  .item:not(.related-item)
  .post-wrapper
  .post-info
  .post-info-footer
  .read-more-wrapper {
  -webkit-flex-shrink: 0;
  -moz-flex-shrink: 0;
  -ms-flex-shrink: 0;
  flex-shrink: 0;
  margin: 2px 0 0 13px;
  text-align: right;
}
.news.posts-grid.layout-medium
  .item:not(.related-item)
  .post-wrapper
  .post-info
  .post-content:after {
  content: "";
  display: table;
  clear: both;
}
.news.posts-grid.layout-medium .item {
    border-radius: 4px;
    height: auto;
    padding-left: 17px;
    -webkit-box-shadow: 0 1px 15px rgba(17,21,25,.42);
    -moz-box-shadow: 0 1px 15px rgba(17,21,25,.42);
    box-shadow: 0 1px 15px rgba(17,21,25,.42);
    clear: both;
    margin: 1% 0;
}
.news.posts-grid.layout-medium .item .post-wrapper {
  display: -webkit-box;
  display: -moz-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -moz-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-wrap: nowrap;
  -moz-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-justify-content: flex-start;
  -moz-justify-content: flex-start;
  -ms-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  align-items: center;
  -webkit-border-radius: 30px;
  border-radius: 30px;
padding: 20px 9px 20px 6px;
}
.news.posts-grid.layout-medium .item .post-wrapper .post-media {
  padding: 0;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-flex-shrink: 0;
  -moz-flex-shrink: 0;
  -ms-flex-shrink: 0;
  flex-shrink: 0;
  width: 25%;
}
.news.posts-grid.layout-medium .item .post-wrapper .post-info {
  margin: 0;
  width: 100%;
  padding-left: 12px;
}
.news.posts-grid.layout-medium .item .post-wrapper .post-info > *:first-child {
  margin-top: 0;
}



.read-more {
  display: inline-block;
  vertical-align: top;
  position: relative;
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
}
.read-more:before {
  content: "\f061";
  position: relative;
  margin: -1px 12px 0 0;
  font: 400 normal 15px/24px "fontawesome";
  display: inline-block;
  vertical-align: top;
  left: 0;
  -webkit-transition: left 0.3s, color 0.3s;
  transition: left 0.3s, color 0.3s;
}
.pic {
  position: relative;
  font-size: 0px;
  line-height: 0px;
}

@media screen and (min-width: 1367px),
  screen and (min-width: 1200px) and (any-hover: hover),
  screen and (min-width: 1200px) and (min--moz-device-pixel-ratio: 0),
  screen and (min-width: 1200px) and (-ms-high-contrast: none),
  screen and (min-width: 1200px) and (-ms-high-contrast: active) {
  .post-meta-wrapper > * a:not(.read-more):hover,
  .news.posts-grid .item .post-wrapper .post-author .post-author-name a:hover,
  .news.posts-grid
    .item
    .post-wrapper
    .post-info
    .read-more-wrapper
    .read-more:hover:before {
    color: rgb(31, 90, 188);
  }
  .news.posts-grid
    .item
    .post-wrapper
    .post-info
    .read-more-wrapper
    .read-more:hover {
    color: rgb(31, 90, 188) !important;
  }
  a:hover,
  .news.posts-grid .item .post-wrapper .post-info .post-title a:hover,
  .news.posts-grid
    .item
    .post-wrapper
    .post-info
    .read-more-wrapper
    .read-more:hover {
    color: rgb(64, 166, 255);
  }
}

@media screen and (max-width: 767px) {
  .page-navigation{margin: 0;}
  .post-info-footer{display: none!important}
  ul.pagination {
    margin-bottom: 17px;
    padding-top: 6px;
}
  .news.posts-grid.layout-medium .item:not(.related-item) {
    width: auto;
    margin-bottom: 24px;
  }
  .news.posts-grid.layout-medium .item:not(.related-item) .post-wrapper {
    display: block;
    text-align: left;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-media {
    width: 100%;
    float: none;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-info {
    width: auto;
    margin: 0 !important;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-info
    .post-title {
    font-size: 18px;
    margin: 9px 0 0;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-info
    .post-info-footer {
    display: block;
    margin: 4px 0 0;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-info
    .post-info-footer
    .post-info-footer-divider {
    display: none;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-info
    .post-info-footer
    .post-meta-wrapper {
    max-width: none;
    display: block;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-info
    .post-info-footer
    .post-meta-wrapper
    .post-author
    .post-author-avatar {
    margin: -8px 7px 0 0;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-info
    .post-info-footer
    .post-meta-item {
    display: inline-block;
    vertical-align: top;
    margin: 5px 10px 0 0;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-info
    .post-info-footer
    .post-meta-item:not(:first-child) {
    padding: 0 0 0 14px;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-info
    .post-info-footer
    .read-more-wrapper {
    text-align: right;
    margin: 8px 0 0;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-info
    .post-info-footer
    .read-more-wrapper
    .read-more:before {
    display: none;
  }
  .news.posts-grid.layout-medium .item:not(.related-item) {
    margin: 0 0 24px;
    padding: 0;
  }
  .news.posts-grid.layout-medium
    .item:not(.related-item)
    .post-wrapper
    .post-media {
    padding: 0;
  }
	.page-navigation ul{
		margin-left: auto;
		margin-right: auto;
		clear: both;
		width: 50%;
		display: block;
		overflow: hidden;
	}
	.page-navigation{
		margin-left: auto;
		margin-right: auto;
		clear: both;
		float: left;
		width: 100%;
		display: block;
	}		
	
}
</style>

<?php get_footer(); ?>