<?php
/**
 * VTS Theme Migration Tool - WordPress Admin Page
 * 
 * Tạo trang admin để thực hiện migration
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add admin menu for migration tool
 */
function vts_add_migration_admin_menu() {
    add_management_page(
        'VTS Theme Migration',
        'VTS Migration',
        'manage_options',
        'vts-migration',
        'vts_migration_admin_page'
    );
}
add_action('admin_menu', 'vts_add_migration_admin_menu');

/**
 * Migration admin page
 */
function vts_migration_admin_page() {
    // Handle form submission
    if (isset($_POST['run_migration']) && wp_verify_nonce($_POST['migration_nonce'], 'vts_migration')) {
        vts_run_migration_process();
    }
    
    if (isset($_POST['run_rollback']) && wp_verify_nonce($_POST['rollback_nonce'], 'vts_rollback')) {
        vts_run_rollback_process();
    }
    
    if (isset($_POST['run_test']) && wp_verify_nonce($_POST['test_nonce'], 'vts_test')) {
        vts_run_test_process();
    }
    
    ?>
    <div class="wrap">
        <h1>VTS Theme Migration Tool</h1>
        
        <div class="notice notice-warning">
            <p><strong>Cảnh báo:</strong> Hãy backup website trước khi thực hiện migration!</p>
        </div>
        
        <?php vts_display_current_status(); ?>
        
        <div class="card" style="max-width: none;">
            <h2>Migration Actions</h2>
            
            <!-- Migration Form -->
            <form method="post" style="margin-bottom: 20px;">
                <?php wp_nonce_field('vts_migration', 'migration_nonce'); ?>
                <p>
                    <input type="submit" name="run_migration" class="button button-primary" 
                           value="Run Migration" 
                           onclick="return confirm('Bạn có chắc chắn muốn chạy migration? Hãy đảm bảo đã backup!');">
                </p>
                <p class="description">Chuyển đổi từ cấu trúc cũ sang cấu trúc mới</p>
            </form>
            
            <!-- Test Form -->
            <form method="post" style="margin-bottom: 20px;">
                <?php wp_nonce_field('vts_test', 'test_nonce'); ?>
                <p>
                    <input type="submit" name="run_test" class="button" value="Run Tests">
                </p>
                <p class="description">Kiểm tra tất cả chức năng hoạt động</p>
            </form>
            
            <!-- Rollback Form -->
            <form method="post">
                <?php wp_nonce_field('vts_rollback', 'rollback_nonce'); ?>
                <p>
                    <input type="submit" name="run_rollback" class="button button-secondary" 
                           value="Rollback" 
                           onclick="return confirm('Bạn có chắc chắn muốn rollback về cấu trúc cũ?');">
                </p>
                <p class="description">Quay về cấu trúc cũ nếu có vấn đề</p>
            </form>
        </div>
        
        <div class="card" style="max-width: none;">
            <h2>Manual Steps</h2>
            <ol>
                <li><strong>Backup:</strong> Sao chép functions.php và thư mục inc/functions/</li>
                <li><strong>Replace:</strong> Đổi tên functions.php thành functions-old.php</li>
                <li><strong>Activate:</strong> Đổi tên functions-new.php thành functions.php</li>
                <li><strong>Test:</strong> Kiểm tra website hoạt động bình thường</li>
                <li><strong>Rollback:</strong> Nếu có lỗi, đổi lại tên file</li>
            </ol>
        </div>
    </div>
    <?php
}

/**
 * Display current status
 */
function vts_display_current_status() {
    $theme_dir = get_template_directory();
    
    echo '<div class="card" style="max-width: none;">';
    echo '<h2>Current Status</h2>';
    echo '<table class="widefat">';
    echo '<tr><th>File</th><th>Status</th><th>Size</th></tr>';
    
    // Check functions.php
    $functions_file = $theme_dir . '/functions.php';
    if (file_exists($functions_file)) {
        $size = filesize($functions_file);
        $lines = count(file($functions_file));
        echo '<tr><td>functions.php</td><td>✅ Exists</td><td>' . $lines . ' lines (' . number_format($size/1024, 1) . ' KB)</td></tr>';
    } else {
        echo '<tr><td>functions.php</td><td>❌ Missing</td><td>-</td></tr>';
    }
    
    // Check functions-new.php
    $functions_new = $theme_dir . '/functions-new.php';
    if (file_exists($functions_new)) {
        $size = filesize($functions_new);
        $lines = count(file($functions_new));
        echo '<tr><td>functions-new.php</td><td>✅ Ready</td><td>' . $lines . ' lines (' . number_format($size/1024, 1) . ' KB)</td></tr>';
    } else {
        echo '<tr><td>functions-new.php</td><td>❌ Missing</td><td>-</td></tr>';
    }
    
    // Check autoloader
    $autoloader = $theme_dir . '/inc/functions-new/autoloader.php';
    if (file_exists($autoloader)) {
        echo '<tr><td>autoloader.php</td><td>✅ Ready</td><td>-</td></tr>';
    } else {
        echo '<tr><td>autoloader.php</td><td>❌ Missing</td><td>-</td></tr>';
    }
    
    // Check if new structure is active
    if (class_exists('VTS_Functions_Autoloader')) {
        $loaded_files = VTS_Functions_Autoloader::get_loaded_files();
        echo '<tr><td>New Structure</td><td>✅ Active</td><td>' . count($loaded_files) . ' files loaded</td></tr>';
    } else {
        echo '<tr><td>New Structure</td><td>❌ Not Active</td><td>-</td></tr>';
    }
    
    echo '</table>';
    echo '</div>';
}

/**
 * Run migration process
 */
function vts_run_migration_process() {
    $theme_dir = get_template_directory();
    
    echo '<div class="notice notice-info"><p>Starting migration...</p></div>';
    
    try {
        // Step 1: Backup current functions.php
        $backup_name = 'functions-backup-' . date('Y-m-d-H-i-s') . '.php';
        if (file_exists($theme_dir . '/functions.php')) {
            copy($theme_dir . '/functions.php', $theme_dir . '/' . $backup_name);
            echo '<div class="notice notice-success"><p>✅ Backup created: ' . $backup_name . '</p></div>';
        }
        
        // Step 2: Check if new structure exists
        if (!file_exists($theme_dir . '/functions-new.php')) {
            throw new Exception('functions-new.php not found!');
        }
        
        if (!file_exists($theme_dir . '/inc/functions-new/autoloader.php')) {
            throw new Exception('autoloader.php not found!');
        }
        
        // Step 3: Switch files
        if (file_exists($theme_dir . '/functions.php')) {
            rename($theme_dir . '/functions.php', $theme_dir . '/functions-old.php');
        }
        
        rename($theme_dir . '/functions-new.php', $theme_dir . '/functions.php');
        
        echo '<div class="notice notice-success"><p>✅ Migration completed successfully!</p></div>';
        echo '<div class="notice notice-info"><p>Please test your website thoroughly.</p></div>';
        
    } catch (Exception $e) {
        echo '<div class="notice notice-error"><p>❌ Migration failed: ' . $e->getMessage() . '</p></div>';
    }
}

/**
 * Run rollback process
 */
function vts_run_rollback_process() {
    $theme_dir = get_template_directory();
    
    echo '<div class="notice notice-info"><p>Starting rollback...</p></div>';
    
    try {
        if (file_exists($theme_dir . '/functions-old.php')) {
            // Remove new functions.php
            if (file_exists($theme_dir . '/functions.php')) {
                rename($theme_dir . '/functions.php', $theme_dir . '/functions-new.php');
            }
            
            // Restore old functions.php
            rename($theme_dir . '/functions-old.php', $theme_dir . '/functions.php');
            
            echo '<div class="notice notice-success"><p>✅ Rollback completed successfully!</p></div>';
        } else {
            echo '<div class="notice notice-error"><p>❌ functions-old.php not found!</p></div>';
        }
        
    } catch (Exception $e) {
        echo '<div class="notice notice-error"><p>❌ Rollback failed: ' . $e->getMessage() . '</p></div>';
    }
}

/**
 * Run test process
 */
function vts_run_test_process() {
    echo '<div class="notice notice-info"><p>Running tests...</p></div>';
    
    $tests = [];
    
    // Test autoloader
    if (class_exists('VTS_Functions_Autoloader')) {
        $loaded_files = VTS_Functions_Autoloader::get_loaded_files();
        $tests[] = ['Autoloader', 'PASS', count($loaded_files) . ' files loaded'];
    } else {
        $tests[] = ['Autoloader', 'FAIL', 'Class not found'];
    }
    
    // Test theme supports
    $supports = [
        'post-thumbnails' => 'Post Thumbnails',
        'title-tag' => 'Title Tag',
        'woocommerce' => 'WooCommerce'
    ];
    
    foreach ($supports as $feature => $name) {
        if (current_theme_supports($feature)) {
            $tests[] = [$name, 'PASS', 'Supported'];
        } else {
            $tests[] = [$name, 'FAIL', 'Not supported'];
        }
    }
    
    // Test functions
    $functions = [
        'vts_page_navi' => 'Pagination',
        'vts_custom_excerpt' => 'Custom Excerpt',
        'vts_setPostViews' => 'View Counter'
    ];
    
    foreach ($functions as $func => $name) {
        if (function_exists($func)) {
            $tests[] = [$name, 'PASS', 'Function exists'];
        } else {
            $tests[] = [$name, 'FAIL', 'Function missing'];
        }
    }
    
    // Display results
    echo '<div class="card" style="max-width: none;">';
    echo '<h3>Test Results</h3>';
    echo '<table class="widefat">';
    echo '<tr><th>Test</th><th>Status</th><th>Message</th></tr>';
    
    foreach ($tests as $test) {
        $color = $test[1] === 'PASS' ? 'green' : 'red';
        echo '<tr>';
        echo '<td>' . $test[0] . '</td>';
        echo '<td style="color: ' . $color . '; font-weight: bold;">' . $test[1] . '</td>';
        echo '<td>' . $test[2] . '</td>';
        echo '</tr>';
    }
    
    echo '</table>';
    echo '</div>';
}

// Add this to functions.php temporarily to enable the admin page
// Uncomment the line below:
// add_action('init', function() { if (is_admin()) include 'admin-migration-tool.php'; });
