<?php
/**
 * VTS Excerpt Functions
 * 
 * Custom excerpt functionality
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Custom excerpt function
 */
function get_excerpt($count) {
    global $post;
    $permalink = get_permalink($post->ID);
    $excerpt = get_the_content();
    $excerpt = strip_tags($excerpt);
    $excerpt = substr($excerpt, 0, $count);
    $excerpt = substr($excerpt, 0, strripos($excerpt, " "));
    $excerpt = '<p>' . $excerpt . '...</p>';
    return $excerpt;
}

/**
 * VTS Custom excerpt
 */
function vts_custom_excerpt() {
    $content = get_the_content('');
    $content = strip_shortcodes($content);
    $content = wp_strip_all_tags($content);

    // Set the desired length of the excerpt in terms of words
    $excerpt_length = 45;

    // Split content into sentences
    $sentences = preg_split('/(?<=[.!?])\s+/', $content, -1, PREG_SPLIT_NO_EMPTY);

    // Initialize an empty excerpt
    $excerpt = '';
    $total_words = 0;

    // Build the excerpt from complete sentences
    foreach ($sentences as $sentence) {
        $sentence_word_count = str_word_count($sentence);

        if (($total_words + $sentence_word_count) <= $excerpt_length) {
            $excerpt .= $sentence . ' ';
            $total_words += $sentence_word_count;
        } else {
            break; // Stop if the total words exceed the limit
        }
    }

    // Clean up the excerpt
    $excerpt = trim($excerpt);
    $excerpt = rtrim($excerpt, '.');

    return $excerpt;
}

/**
 * Custom hook to replace the default excerpt with a custom excerpt
 */
function vts_excerpt_hook($excerpt) {
    ob_start(); // Start output buffering

    // Get the custom excerpt and ensure no trailing period
    $custom_excerpt = rtrim(vts_custom_excerpt(), '.');

    // Print the custom excerpt within a paragraph tag
    echo '<p>' . $custom_excerpt . '.</p>';

    $output = ob_get_clean(); // Capture the output and clean the buffer
    return $output; // Return the modified excerpt
}

// Uncomment to enable custom excerpt
// add_filter('the_excerpt', 'vts_excerpt_hook');
