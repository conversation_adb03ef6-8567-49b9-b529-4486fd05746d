<?php
/**
 * VTS Theme System Test
 * 
 * Comprehensive testing script for the new theme structure
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class VTS_System_Test {
    
    private $results = [];
    private $errors = [];
    
    /**
     * Run all tests
     */
    public function run_all_tests() {
        echo "<h2>VTS Theme System Test</h2>";
        
        $this->test_autoloader();
        $this->test_core_functions();
        $this->test_frontend_functions();
        $this->test_admin_functions();
        $this->test_woocommerce_functions();
        $this->test_optimization_functions();
        $this->test_seo_functions();
        $this->test_security_functions();
        $this->test_performance();
        
        $this->display_results();
    }
    
    /**
     * Test autoloader functionality
     */
    private function test_autoloader() {
        echo "<h3>Testing Autoloader...</h3>";
        
        // Test autoloader class exists
        if (class_exists('VTS_Functions_Autoloader')) {
            $this->add_result('Autoloader class', 'PASS', 'Class exists and loaded');
            
            // Test loaded files
            $loaded_files = VTS_Functions_Autoloader::get_loaded_files();
            if (count($loaded_files) > 0) {
                $this->add_result('File loading', 'PASS', count($loaded_files) . ' files loaded');
            } else {
                $this->add_result('File loading', 'FAIL', 'No files loaded');
            }
        } else {
            $this->add_result('Autoloader class', 'FAIL', 'Class not found');
        }
    }
    
    /**
     * Test core functions
     */
    private function test_core_functions() {
        echo "<h3>Testing Core Functions...</h3>";
        
        // Test theme support
        $supports = [
            'post-thumbnails' => 'Post thumbnails',
            'title-tag' => 'Title tag',
            'automatic-feed-links' => 'Feed links',
            'custom-logo' => 'Custom logo',
        ];
        
        foreach ($supports as $feature => $name) {
            if (current_theme_supports($feature)) {
                $this->add_result($name, 'PASS', 'Theme support active');
            } else {
                $this->add_result($name, 'FAIL', 'Theme support missing');
            }
        }
        
        // Test menus
        $menus = get_registered_nav_menus();
        if (count($menus) >= 3) {
            $this->add_result('Navigation menus', 'PASS', count($menus) . ' menus registered');
        } else {
            $this->add_result('Navigation menus', 'FAIL', 'Not enough menus registered');
        }
        
        // Test image sizes
        global $_wp_additional_image_sizes;
        if (isset($_wp_additional_image_sizes['archive_thumb'])) {
            $this->add_result('Custom image sizes', 'PASS', 'Archive thumb size registered');
        } else {
            $this->add_result('Custom image sizes', 'FAIL', 'Archive thumb size missing');
        }
    }
    
    /**
     * Test frontend functions
     */
    private function test_frontend_functions() {
        echo "<h3>Testing Frontend Functions...</h3>";
        
        // Test if we're on frontend
        if (!is_admin()) {
            // Test pagination function
            if (function_exists('vts_page_navi')) {
                $this->add_result('Pagination function', 'PASS', 'Function exists');
            } else {
                $this->add_result('Pagination function', 'FAIL', 'Function missing');
            }
            
            // Test excerpt function
            if (function_exists('vts_custom_excerpt')) {
                $this->add_result('Custom excerpt', 'PASS', 'Function exists');
            } else {
                $this->add_result('Custom excerpt', 'FAIL', 'Function missing');
            }
            
            // Test view counter
            if (function_exists('vts_setPostViews')) {
                $this->add_result('View counter', 'PASS', 'Function exists');
            } else {
                $this->add_result('View counter', 'FAIL', 'Function missing');
            }
        } else {
            $this->add_result('Frontend tests', 'SKIP', 'Running in admin context');
        }
    }
    
    /**
     * Test admin functions
     */
    private function test_admin_functions() {
        echo "<h3>Testing Admin Functions...</h3>";
        
        if (is_admin()) {
            // Test admin styles
            if (function_exists('vts_admin_script')) {
                $this->add_result('Admin styles', 'PASS', 'Function exists');
            } else {
                $this->add_result('Admin styles', 'FAIL', 'Function missing');
            }
            
            // Test dashboard customizations
            if (function_exists('vts_remove_dashboard_widgets')) {
                $this->add_result('Dashboard customization', 'PASS', 'Function exists');
            } else {
                $this->add_result('Dashboard customization', 'FAIL', 'Function missing');
            }
        } else {
            $this->add_result('Admin tests', 'SKIP', 'Running in frontend context');
        }
    }
    
    /**
     * Test WooCommerce functions
     */
    private function test_woocommerce_functions() {
        echo "<h3>Testing WooCommerce Functions...</h3>";
        
        if (class_exists('WooCommerce')) {
            // Test WooCommerce support
            if (current_theme_supports('woocommerce')) {
                $this->add_result('WooCommerce support', 'PASS', 'Theme support active');
            } else {
                $this->add_result('WooCommerce support', 'FAIL', 'Theme support missing');
            }
            
            // Test custom functions
            if (function_exists('vts_custom_price')) {
                $this->add_result('Custom price function', 'PASS', 'Function exists');
            } else {
                $this->add_result('Custom price function', 'FAIL', 'Function missing');
            }
        } else {
            $this->add_result('WooCommerce tests', 'SKIP', 'WooCommerce not active');
        }
    }
    
    /**
     * Test optimization functions
     */
    private function test_optimization_functions() {
        echo "<h3>Testing Optimization Functions...</h3>";
        
        // Test heartbeat optimization
        if (function_exists('vts_optimize_heartbeat_for_basic_site')) {
            $this->add_result('Heartbeat optimization', 'PASS', 'Function exists');
        } else {
            $this->add_result('Heartbeat optimization', 'FAIL', 'Function missing');
        }
        
        // Test script optimization
        if (function_exists('vts_defer_js_except_jquery')) {
            $this->add_result('Script optimization', 'PASS', 'Function exists');
        } else {
            $this->add_result('Script optimization', 'FAIL', 'Function missing');
        }
    }
    
    /**
     * Test SEO functions
     */
    private function test_seo_functions() {
        echo "<h3>Testing SEO Functions...</h3>";
        
        // Test schema functions
        if (function_exists('vts_add_itemprop_menu')) {
            $this->add_result('Schema markup', 'PASS', 'Function exists');
        } else {
            $this->add_result('Schema markup', 'FAIL', 'Function missing');
        }
        
        // Test redirects
        if (function_exists('vts_redirect_search_results')) {
            $this->add_result('SEO redirects', 'PASS', 'Function exists');
        } else {
            $this->add_result('SEO redirects', 'FAIL', 'Function missing');
        }
    }
    
    /**
     * Test security functions
     */
    private function test_security_functions() {
        echo "<h3>Testing Security Functions...</h3>";
        
        // Test API blocking
        if (function_exists('vts_block_wp_rocket_api')) {
            $this->add_result('API blocking', 'PASS', 'Function exists');
        } else {
            $this->add_result('API blocking', 'FAIL', 'Function missing');
        }
        
        // Test feature disabling
        if (function_exists('vts_disable_comments_completely')) {
            $this->add_result('Feature disabling', 'PASS', 'Function exists');
        } else {
            $this->add_result('Feature disabling', 'FAIL', 'Function missing');
        }
    }
    
    /**
     * Test performance
     */
    private function test_performance() {
        echo "<h3>Testing Performance...</h3>";
        
        // Count loaded files
        if (class_exists('VTS_Functions_Autoloader')) {
            $loaded_files = VTS_Functions_Autoloader::get_loaded_files();
            $file_count = count($loaded_files);
            
            if ($file_count < 20) {
                $this->add_result('File loading efficiency', 'PASS', "$file_count files loaded (efficient)");
            } else {
                $this->add_result('File loading efficiency', 'WARNING', "$file_count files loaded (consider optimization)");
            }
        }
        
        // Test memory usage
        $memory_usage = memory_get_usage(true) / 1024 / 1024; // MB
        if ($memory_usage < 50) {
            $this->add_result('Memory usage', 'PASS', number_format($memory_usage, 2) . ' MB');
        } else {
            $this->add_result('Memory usage', 'WARNING', number_format($memory_usage, 2) . ' MB (high)');
        }
    }
    
    /**
     * Add test result
     */
    private function add_result($test, $status, $message) {
        $this->results[] = [
            'test' => $test,
            'status' => $status,
            'message' => $message
        ];
        
        if ($status === 'FAIL') {
            $this->errors[] = $test;
        }
    }
    
    /**
     * Display test results
     */
    private function display_results() {
        echo "<h3>Test Results Summary</h3>";
        
        $pass_count = count(array_filter($this->results, function($r) { return $r['status'] === 'PASS'; }));
        $fail_count = count(array_filter($this->results, function($r) { return $r['status'] === 'FAIL'; }));
        $warning_count = count(array_filter($this->results, function($r) { return $r['status'] === 'WARNING'; }));
        $skip_count = count(array_filter($this->results, function($r) { return $r['status'] === 'SKIP'; }));
        
        echo "<p><strong>Summary:</strong> $pass_count passed, $fail_count failed, $warning_count warnings, $skip_count skipped</p>";
        
        echo "<table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>";
        echo "<tr style='background: #f5f5f5;'><th style='padding: 10px; border: 1px solid #ddd;'>Test</th><th style='padding: 10px; border: 1px solid #ddd;'>Status</th><th style='padding: 10px; border: 1px solid #ddd;'>Message</th></tr>";
        
        foreach ($this->results as $result) {
            $color = '';
            switch ($result['status']) {
                case 'PASS': $color = 'color: green;'; break;
                case 'FAIL': $color = 'color: red;'; break;
                case 'WARNING': $color = 'color: orange;'; break;
                case 'SKIP': $color = 'color: gray;'; break;
            }
            
            echo "<tr>";
            echo "<td style='padding: 10px; border: 1px solid #ddd;'>{$result['test']}</td>";
            echo "<td style='padding: 10px; border: 1px solid #ddd; $color'><strong>{$result['status']}</strong></td>";
            echo "<td style='padding: 10px; border: 1px solid #ddd;'>{$result['message']}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        if (count($this->errors) > 0) {
            echo "<div style='background: #ffebee; padding: 15px; border-left: 4px solid #f44336; margin: 20px 0;'>";
            echo "<h4>❌ Critical Issues Found:</h4>";
            echo "<ul>";
            foreach ($this->errors as $error) {
                echo "<li>$error</li>";
            }
            echo "</ul>";
            echo "</div>";
        } else {
            echo "<div style='background: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50; margin: 20px 0;'>";
            echo "<h4>✅ All critical tests passed!</h4>";
            echo "<p>Your theme structure is working correctly.</p>";
            echo "</div>";
        }
    }
}

// Only run if accessed directly with proper authentication
if (isset($_GET['run_tests']) && current_user_can('administrator')) {
    $test = new VTS_System_Test();
    $test->run_all_tests();
} elseif (isset($_GET['show_test_panel']) && current_user_can('administrator')) {
    ?>
    <div style="max-width: 800px; margin: 20px auto; padding: 20px; background: #fff; border: 1px solid #ddd;">
        <h2>VTS Theme System Test Panel</h2>
        <p>This will run comprehensive tests on your new theme structure.</p>
        
        <p>
            <a href="?run_tests=1" class="button button-primary">Run All Tests</a>
        </p>
    </div>
    <?php
}
