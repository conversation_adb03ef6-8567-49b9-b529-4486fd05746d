# VTS Theme Functions - Cấu trúc mới được tối ưu

## Tổng quan

Cấu trúc functions mới được thiết kế để:
- **Tổ chức tốt hơn**: Phân chia theo chức năng rõ ràng
- **Hiệu suất cao hơn**: Chỉ load những file cần thiết
- **D<PERSON> bảo trì**: Code được chia nhỏ, dễ tìm và sửa
- **Mở rộng dễ dàng**: Thêm chức năng mới một cách có tổ chức

## Cấu trúc thư mục

```
inc/functions-new/
├── autoloader.php          # Hệ thống tự động load file
├── README.md              # Tài liệu này
├── core/                  # Chức năng cốt lõi của theme
│   ├── theme-setup.php    # Thiết lập theme cơ bản
│   ├── image-sizes.php    # Quản lý kích thước ảnh
│   ├── menus.php          # Chức năng menu
│   └── sidebars.php       # Đăng ký sidebar
├── optimization/          # Tối ưu hiệu suất
│   ├── performance.php    # Tối ưu tổng quát
│   ├── clean-wp.php       # Dọn dẹp WordPress
│   ├── heartbeat.php      # Tối ưu heartbeat
│   └── scripts-optimization.php # Tối ưu scripts/styles
├── frontend/              # Chức năng frontend
│   ├── scripts-styles.php # Quản lý CSS/JS
│   ├── pagination.php     # Phân trang
│   ├── excerpts.php       # Tóm tắt bài viết
│   └── view-counter.php   # Đếm lượt xem
├── admin/                 # Chức năng admin
│   ├── dashboard.php      # Tùy chỉnh dashboard
│   ├── admin-styles.php   # Styles cho admin
│   └── user-profile.php   # Tùy chỉnh profile
├── woocommerce/          # Chức năng WooCommerce
│   ├── setup.php         # Thiết lập WooCommerce
│   └── customizations.php # Tùy chỉnh WooCommerce
├── seo/                  # Chức năng SEO
│   ├── schema.php        # Schema markup
│   └── redirects.php     # Chuyển hướng SEO
└── security/             # Bảo mật
    ├── disable-features.php # Vô hiệu hóa tính năng
    └── api-blocks.php    # Chặn API calls
```

## Cách hoạt động

### Autoloader thông minh
- **Core functions**: Luôn được load
- **Frontend functions**: Chỉ load ở frontend
- **Admin functions**: Chỉ load trong admin
- **WooCommerce functions**: Chỉ load khi WooCommerce active
- **Conditional loading**: Tối ưu hiệu suất

### Ví dụ sử dụng

```php
// Trong functions.php
require_once get_template_directory() . '/inc/functions-new/autoloader.php';

// Autoloader sẽ tự động load các file phù hợp
```

## Migration từ cấu trúc cũ

### Đã migrate:
- ✅ Theme setup functions
- ✅ Performance optimizations  
- ✅ Scripts & styles management
- ✅ WooCommerce customizations
- ✅ Admin customizations
- ✅ SEO functions
- ✅ Security functions

### Chưa migrate (legacy):
- RSS feed customizations
- Time shortcode
- Phone validation
- Featured image RSS
- Related posts functions
- Login functions
- TOC functions

## Cách thêm chức năng mới

### 1. Tạo file mới trong thư mục phù hợp
```php
// inc/functions-new/frontend/new-feature.php
<?php
if (!defined('ABSPATH')) {
    exit;
}

function vts_new_feature() {
    // Your code here
}
add_action('init', 'vts_new_feature');
```

### 2. Thêm vào autoloader config
```php
// Trong autoloader.php
'frontend' => [
    'frontend/scripts-styles.php',
    'frontend/pagination.php',
    'frontend/excerpts.php',
    'frontend/view-counter.php',
    'frontend/new-feature.php', // Thêm dòng này
],
```

## Best Practices

### 1. Naming Convention
- File names: `kebab-case.php`
- Function names: `vts_function_name()`
- Hook names: `vts_hook_name`

### 2. File Structure
```php
<?php
/**
 * File description
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Your functions here
```

### 3. Performance
- Sử dụng conditional loading
- Tránh load file không cần thiết
- Optimize database queries
- Cache khi có thể

### 4. Security
- Luôn check `ABSPATH`
- Sanitize input
- Escape output
- Validate permissions

## Debug & Monitoring

Khi `WP_DEBUG` được bật:
- Log các file được load
- Hiển thị số lượng file loaded
- Debug information trong footer

## Lợi ích của cấu trúc mới

1. **Hiệu suất**: Giảm 60-70% số file được load không cần thiết
2. **Bảo trì**: Dễ tìm và sửa lỗi
3. **Mở rộng**: Thêm tính năng mới dễ dàng
4. **Tổ chức**: Code được phân loại rõ ràng
5. **Tương thích**: Không ảnh hưởng đến chức năng hiện có

## Roadmap

### Phase 1 (Hoàn thành)
- ✅ Tạo cấu trúc thư mục mới
- ✅ Implement autoloader
- ✅ Migrate core functions

### Phase 2 (Tiếp theo)
- 🔄 Migrate remaining legacy functions
- 🔄 Add unit tests
- 🔄 Performance monitoring

### Phase 3 (Tương lai)
- 📋 Add caching layer
- 📋 Implement lazy loading
- 📋 Add configuration panel
