<?php
/**
 * VTS Theme Setup Functions
 * 
 * Core theme setup and configuration
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * VTS Theme setup function
 * Sets up theme defaults and registers support for various WordPress features
 */
if (!function_exists('vts_setup')) :
    function vts_setup() {
        // Load theme textdomain for translations
        // load_theme_textdomain('vutruso', get_template_directory() . '/languages');
        
        // Restoring the classic Widgets Editor
        remove_theme_support('widgets-block-editor');

        // Register navigation menus
        register_nav_menus([
            'main_menu'   => __('Main Menu', 'vutruso'),
            'footer_menu' => __('Footer Menu', 'vutruso'),
            'mobile_menu' => __('Mobile Menu', 'vutruso'),
        ]);
    
        // Add default posts and comments RSS feed links to head
        add_theme_support('automatic-feed-links');
        
        // Adds <title> tag support
        add_theme_support('title-tag'); 

        // Add theme support for selective refresh for widgets
        add_theme_support('customize-selective-refresh-widgets');

        // Add support for core custom logo
        add_theme_support('custom-logo', [
            'height'      => 28,
            'width'       => 140,
            'flex-width'  => false,
            'flex-height' => false,
        ]);

        // Post formats
        add_theme_support('post-formats', ['image']);

        // Add support for responsive embeds
        add_theme_support('responsive-embeds');

        // Content width
        if (!isset($content_width)) {
            $content_width = 1200;
        }

        // Clean up WordPress head
        vts_clean_wp_head();
    }
endif;
add_action('after_setup_theme', 'vts_setup');

/**
 * Clean up WordPress head section
 * Remove unnecessary meta tags and links
 */
function vts_clean_wp_head() {
    // Remove the REST API lines from the HTML Header
    remove_action('wp_head', 'rest_output_link_wp_head', 10);
    remove_action('wp_head', 'wp_oembed_add_discovery_links', 10);
    
    // Turn off oEmbed auto discovery
    add_filter('embed_oembed_discover', '__return_false');
    
    // Remove oEmbed discovery links
    remove_action('wp_head', 'wp_oembed_add_discovery_links');
    
    // Remove oEmbed-specific JavaScript from the front-end and back-end
    remove_action('wp_head', 'wp_oembed_add_host_js');
    
    // Remove emoji js
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
    
    // Remove EditURI/RSD + wlwmanifest + wp version
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'wp_generator'); 
    
    // Display the links to the general feeds: Post and Comment Feed
    remove_action('wp_head', 'feed_links', 2);
}

/**
 * Enable Excerpts for Pages
 */
function vts_excerpts_to_pages() {
    add_post_type_support('page', 'excerpt');
}
add_action('init', 'vts_excerpts_to_pages');

/**
 * SVG Upload Support
 */
function vts_mime_types($mimes) {
    $mimes['svg'] = 'image/svg+xml';
    return $mimes;
}
add_filter('upload_mimes', 'vts_mime_types');

/**
 * Show SVG files in media library
 */
function vts_show_svg_in_media_library($response) {
    if ($response['mime'] === 'image/svg+xml') {
        $response['sizes'] = [
            'medium' => [
                'url' => $response['url'],
            ],
        ];
    }
    return $response;
}
add_filter('wp_prepare_attachment_for_js', 'vts_show_svg_in_media_library');

/**
 * Set locale for admin
 */
function vts_set_admin_locale($locale) {
    if (is_admin()) {
        return 'vi';
    }
    return $locale;
}
add_filter('pre_determine_locale', 'vts_set_admin_locale');

/**
 * Allow HTML in term descriptions
 */
function vts_allow_html_in_term_descriptions() {
    // Remove default filters
    remove_filter('pre_term_description', 'wp_filter_kses');
    remove_filter('term_description', 'wp_kses_data');
    
    // Add filters to allow HTML
    add_filter('pre_term_description', 'wp_kses_post');
    add_filter('term_description', 'wptexturize');
    add_filter('term_description', 'convert_chars');
    add_filter('term_description', 'wpautop');
}
add_action('init', 'vts_allow_html_in_term_descriptions');

/**
 * Remove capital P dangit filter
 */
function vts_remove_capital_p_dangit() {
    remove_filter('the_title', 'capital_P_dangit', 11);
    remove_filter('the_content', 'capital_P_dangit', 11);
    remove_filter('comment_text', 'capital_P_dangit', 31);
}
add_action('init', 'vts_remove_capital_p_dangit');

/**
 * Custom translations
 */
function vts_custom_translations($strings) {
    $text = [
        'Category Archives:' => '',
        'vote' => 'Đánh giá',
        'Related products' => 'Mẫu website tương tự',
        'Proceed to checkout' => 'Tiến hành thanh toán',
        'Cart totals' => 'Tổng tiền',
        'Apply coupon' => 'Áp dụng',
        'Categories:' => 'Danh mục:'
    ];
    
    $strings = str_ireplace(array_keys($text), $text, $strings);
    return $strings;
}
add_filter('gettext', 'vts_custom_translations', 20);

/**
 * Disable core block patterns
 */
function vts_disable_core_block_patterns() {
    remove_theme_support('core-block-patterns');
}
add_action('after_setup_theme', 'vts_disable_core_block_patterns');

/**
 * Disable remote block patterns
 */
add_filter('should_load_remote_block_patterns', '__return_false');

/**
 * Set JPEG quality to 100%
 */
function vts_jpeg_quality($quality) {
    return 100;
}
add_filter('jpeg_quality', 'vts_jpeg_quality');

/**
 * Set big image size threshold
 */
function vts_big_image_size_threshold() {
    return 4000; // Or false to disable completely
}
add_filter('big_image_size_threshold', 'vts_big_image_size_threshold', 999);
