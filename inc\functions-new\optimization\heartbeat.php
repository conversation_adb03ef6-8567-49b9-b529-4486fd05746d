<?php
/**
 * VTS Heartbeat Optimization
 * 
 * Optimize WordPress heartbeat for better performance
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Optimize heartbeat for basic site
 */
function vts_optimize_heartbeat_for_basic_site() {
    global $pagenow;
    
    // Disable completely on frontend (not needed for visitors)
    if (!is_admin()) {
        wp_deregister_script('heartbeat');
        return;
    }
    
    // In admin, only keep on edit post/product pages
    $allowed_pages = ['post.php', 'post-new.php'];
    
    if (!in_array($pagenow, $allowed_pages)) {
        wp_deregister_script('heartbeat');
    }
}
add_action('init', 'vts_optimize_heartbeat_for_basic_site');

/**
 * Slow down heartbeat when needed (on edit pages)
 */
function vts_slow_down_heartbeat($settings) {
    $settings['interval'] = 60; // 1 minute instead of 15 seconds
    return $settings;
}
add_filter('heartbeat_settings', 'vts_slow_down_heartbeat');
