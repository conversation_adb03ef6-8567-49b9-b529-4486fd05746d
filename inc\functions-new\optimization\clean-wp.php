<?php
/**
 * VTS Clean WordPress
 * 
 * Clean up WordPress core functionality and optimize performance
 * Based on the existing vts_clean.php but optimized and organized
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Disable Inter and Cardo fonts
 */
function vts_disable_inter_and_cardo_fonts($theme_json) {
    $theme_data = $theme_json->get_data();
    $font_data  = $theme_data['settings']['typography']['fontFamilies']['theme'] ?? [];

    // Fonts to be removed
    $fonts_to_remove = ['Inter', 'Cardo'];

    // Check and remove each font in $fonts_to_remove
    foreach ($font_data as $font_key => $font) {
        if (isset($font['name']) && in_array($font['name'], $fonts_to_remove, true)) {
            // Remove the font
            unset($font_data[$font_key]); 
        }
    }

    // Update theme JSON data if any fonts were removed
    if (count($font_data) !== count($theme_data['settings']['typography']['fontFamilies']['theme'] ?? [])) {
        $theme_json->update_with([
            'version'  => 1,
            'settings' => [
                'typography' => [
                    'fontFamilies' => [
                        'theme' => $font_data,
                    ],
                ],
            ],
        ]);
    }

    return $theme_json;
}
add_filter('wp_theme_json_data_theme', 'vts_disable_inter_and_cardo_fonts', 9999);

/**
 * Remove Freesoul Deactivate Plugins comment
 */
function vts_remove_eos_dp_comment() {
    remove_action('wp_footer', 'eos_dp_comment', 10);
}
add_action('init', 'vts_remove_eos_dp_comment');

/**
 * Clean WordPress header completely
 */
function vts_clean_wp_header() {
    // REST API
    remove_action('wp_head', 'rest_output_link_wp_head', 10);
    remove_action('wp_head', 'wp_oembed_add_discovery_links', 10);
    
    // Embed
    remove_action('rest_api_init', 'wp_oembed_register_route');
    add_filter('embed_oembed_discover', '__return_false');
    remove_filter('oembed_dataparse', 'wp_filter_oembed_result', 10);
    remove_action('wp_head', 'wp_oembed_add_discovery_links');
    remove_action('wp_head', 'wp_oembed_add_host_js');
    
    // Emoji
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
    
    // EditURI/RSD, WLW Manifest, WP Version
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'wp_generator');
    
    // Feed links
    remove_action('wp_head', 'feed_links_extra', 3);
    remove_action('wp_head', 'feed_links', 2);
}
add_action('init', 'vts_clean_wp_header');

// Note: Performance optimizations moved to optimization/performance.php to avoid conflicts
