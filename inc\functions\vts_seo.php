<?php

/* Schema menu link itemprop'] = 'url' optimize SEO
*===============================================================*/
add_filter( 'nav_menu_link_attributes', 'vutruso_add_itemprop_menu', 10, 3 );
function vutruso_add_itemprop_menu( $atts, $item, $args ){
  $atts['itemprop'] = 'url';
  return $atts;
}


/* Add itemprop image markup to img tags
*=====================================================================*/
add_filter('the_content', 'vutruso_add_itemprop_image_markup', 2);
function vutruso_add_itemprop_image_markup($content){
    //Replace the instance with the itemprop image markup.
    $string = '<img';
    $replace = '<img itemprop="image"';
    $content = str_replace( $string, $replace, $content );
    return $content;
}


/* Add itemprop url markup to <a itemprop="url"
*=====================================================================*/
add_filter('the_content', 'vutruso_add_itemprop_url_markup', 2);
function vutruso_add_itemprop_url_markup($content){
    //Replace the instance with the itemprop image markup.
    $string = '<a';
    $replace = '<a itemprop="url"';
    $content = str_replace( $string, $replace, $content );
    return $content;
}


/* Add alt tag to WordPress Gravatar images
*=====================================================================*/
function vutruso_gravatar_alt($vtsGravatar) {
	if (have_comments()) {
		$alt = get_comment_author();
	}
	else {
		$alt = get_the_author_meta('display_name');
	}
	$vtsGravatar = str_replace('alt=\'\'', 'alt=\'Avatar for ' . $alt . '\'', $vtsGravatar);
	return $vtsGravatar;
}
add_filter('get_avatar', 'vutruso_gravatar_alt');



// Reditect neu trong tu khoa co game
add_action( 'template_redirect', 'vutruso_redirect_search_results' );
function vutruso_redirect_search_results() {
    if ( isset( $_GET['s'] ) ) {
        // Define an array of keywords related to "Game".
        $game_related_keywords = array( 'game', 'gaming', 'play', 'arcade', 'video game', 'esports' ); // Add more keywords as needed.

        // Convert the search query to lowercase to make the search case-insensitive.
        $search_query = strtolower( $_GET['s'] );

        // Check if any part of the search query matches the game-related keywords.
        foreach ( $game_related_keywords as $keyword ) {
            if ( strpos( $search_query, $keyword ) !== false ) {
                // Redirect to the homepage if a match is found.
                wp_redirect( 'https://vutruso.com/' );
                exit;
            }
        }
    }
}