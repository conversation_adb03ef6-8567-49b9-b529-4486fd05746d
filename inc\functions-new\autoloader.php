<?php
/**
 * VTS Theme Functions Autoloader
 * 
 * Tự động load các file function theo cấu trúc thư mục được tổ chức
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class VTS_Functions_Autoloader {
    
    /**
     * Base path for functions directory
     */
    private static $base_path;
    
    /**
     * Array of loaded files to prevent duplicate loading
     */
    private static $loaded_files = [];
    
    /**
     * Configuration for which files to load in which contexts
     */
    private static $load_config = [
        'always' => [
            'core/theme-setup.php',
            'core/image-sizes.php',
            'core/menus.php',
            'core/sidebars.php',
        ],
        'frontend' => [
            'frontend/scripts-styles.php',
            'frontend/pagination.php',
            'frontend/excerpts.php',
            'frontend/view-counter.php',
        ],
        'admin' => [
            'admin/dashboard.php',
            'admin/admin-styles.php',
            'admin/user-profile.php',
        ],
        'optimization' => [
            'optimization/performance.php',
            'optimization/clean-wp.php',
            'optimization/heartbeat.php',
            'optimization/scripts-optimization.php',
        ],
        'woocommerce' => [
            'woocommerce/setup.php',
            'woocommerce/customizations.php',
        ],
        'seo' => [
            'seo/schema.php',
            'seo/redirects.php',
        ],
        'security' => [
            'security/disable-features.php',
            'security/api-blocks.php',
        ],
    ];
    
    /**
     * Initialize the autoloader
     */
    public static function init() {
        self::$base_path = get_template_directory() . '/inc/functions-new/';
        
        // Load files based on context
        self::load_always();
        
        if (is_admin()) {
            self::load_admin();
        } else {
            self::load_frontend();
        }
        
        // Load conditional modules
        if (class_exists('WooCommerce')) {
            self::load_woocommerce();
        }
        
        self::load_optimization();
        self::load_seo();
        self::load_security();
    }
    
    /**
     * Load files that should always be loaded
     */
    private static function load_always() {
        self::load_files(self::$load_config['always']);
    }
    
    /**
     * Load admin-specific files
     */
    private static function load_admin() {
        self::load_files(self::$load_config['admin']);
    }
    
    /**
     * Load frontend-specific files
     */
    private static function load_frontend() {
        self::load_files(self::$load_config['frontend']);
    }
    
    /**
     * Load WooCommerce-specific files
     */
    private static function load_woocommerce() {
        self::load_files(self::$load_config['woocommerce']);
    }
    
    /**
     * Load optimization files
     */
    private static function load_optimization() {
        self::load_files(self::$load_config['optimization']);
    }
    
    /**
     * Load SEO files
     */
    private static function load_seo() {
        self::load_files(self::$load_config['seo']);
    }
    
    /**
     * Load security files
     */
    private static function load_security() {
        self::load_files(self::$load_config['security']);
    }
    
    /**
     * Load an array of files
     * 
     * @param array $files Array of file paths relative to base path
     */
    private static function load_files($files) {
        foreach ($files as $file) {
            self::load_file($file);
        }
    }
    
    /**
     * Load a single file
     * 
     * @param string $file File path relative to base path
     */
    private static function load_file($file) {
        $full_path = self::$base_path . $file;
        
        // Check if file exists and hasn't been loaded yet
        if (file_exists($full_path) && !in_array($file, self::$loaded_files)) {
            require_once $full_path;
            self::$loaded_files[] = $file;
            
            // Log successful load in debug mode
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("VTS Autoloader: Loaded {$file}");
            }
        } elseif (!file_exists($full_path)) {
            // Log missing file in debug mode
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("VTS Autoloader: File not found - {$file}");
            }
        }
    }
    
    /**
     * Get list of loaded files
     * 
     * @return array
     */
    public static function get_loaded_files() {
        return self::$loaded_files;
    }
    
    /**
     * Check if a specific file has been loaded
     * 
     * @param string $file File path to check
     * @return bool
     */
    public static function is_loaded($file) {
        return in_array($file, self::$loaded_files);
    }
}

// Initialize the autoloader
VTS_Functions_Autoloader::init();
