<?php get_header(); ?>
<div class="innerContainer">
    <section class="news posts-grid layout-medium" style="margin-top: 2%;">
        <div class="text-left">
            <h1 class="title-search"><i class="fa fa-tags" aria-hidden="true"></i> <?php single_cat_title(); ?></h1>
        </div>



        <div class="row">
            <div class="container-fluid">
                <div class="col-12 col-xl-12">
                    <div class="row loop-content">

                        <?php while(have_posts()) : the_post(); ?>

                        <?php
                            // $get_cat = get_the_category();
                            // $first_cat = $get_cat[0];
                            // $category_name = $first_cat->cat_name;
                        ?>

                        <div class="col-md-4 col-xl-4 col-12">
                            <a href="<?php the_permalink(); ?>" class="blog">
                                <!-- <img decoding="async"
                                src=""> -->
                                <?php if(has_post_thumbnail()){ the_post_thumbnail('large', array('class'=>'pic', 'title' => get_the_title(), 'alt' => get_the_title(), 'itemprop' => 'image')); } else { ?>
                                <img height="340"
                                    src="<?php echo get_template_directory_uri(); ?>/img/nothumb-rc_big.jpg"
                                    alt="<?php the_title(); ?>" />
                                <?php } ?>
                                <div class="blog__content">
                                    <div class="blog__content__text">
                                        <div class="blog__content__text__category">
                                            <span><?php //echo esc_html( $category_name ); ?></span>
                                        </div>
                                        <h4><?php the_title(); ?></h4>
                                        <p class="blog__content__text__meta"><?php the_author(); ?></p>
                                        <p class="blog__content__text__meta"><?php the_date('d-m-Y'); ?></p>
                                        <div class="blog__content__text__arrow">
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>

                        <?php endwhile; ?>


                    </div><?php vts_page_navi(); ?>
                </div>
            </div>
        </div>


    </section>
</div>

<style>
.title-search {
    font-weight: 700;
    border-radius: 4px;
    line-height: 24px;
    padding-left: 17px;
    -webkit-box-shadow: 0 1px 15px rgba(17,21,25,.42);
    -moz-box-shadow: 0 1px 15px rgba(17, 21, 25, .42);
    box-shadow: 0 1px 15px rgba(17,21,25,.42);
    text-transform: uppercase;
    margin-bottom: 3%;
    padding: 5px 10px;
    font-size: 13px;
}

.blog{max-height:368px;height:368px;border-radius:10px;display:block;position:relative;overflow:hidden;}
.blog img{height:100%;width:100%;border-radius:10px; object-fit: contain;transform:scale(1);-webkit-animation:all 1s ease;transition:all .2s ease-in-out;}
.blog:hover img{transform:scale(1.2);transition:all .2s ease-in-out;}
.blog__content{position:absolute;bottom:0px;left:0px;min-height:100%;border-bottom-left-radius:10px;border-bottom-right-radius:10px;width:100%;}
.blog__content__text{position:absolute;bottom:0px;left:0px;width:100%;padding:30px 30px;background: linear-gradient(to top, hsla(183, 68%, 27%, 0.9), 70%, hsl(0deg 0% 0% / 35%)); padding-bottom: 10px;}
.blog__content__text h4{color:#ffffff;max-width:85%;line-height:1.2;font-size:17px;}
.blog__content__text p{color:#ffffff;max-width:65%;margin-top: 5px;}
.blog__content__text__meta{color:#EFF9F9;opacity:0.5;}
.blog__content__text__arrow{position:absolute;background-image:url('https://vutruso.com/wp-content/uploads/2024/04/arrow-right-white.svg');background-repeat:no-repeat;background-size:15px;background-position:center;border-radius:50%;border:1px solid #ffffff;color:#fff;width:40px;height:40px;right:30px;bottom:30px;}
.blog__content__text__arrow:before,.blog__content__text__arrow:after{content:'';display:block;position:absolute;top:0;right:0;bottom:0;left:0;border-radius:50%;border:1px solid #ffffff;}
.blog:hover .blog__content__text__arrow:before{-webkit-animation:ripple 1s linear infinite;animation:ripple 1s linear infinite;}
.blog__content__text__category span{font-size:16px;color:#ffffff;margin-bottom:10px;display:block;}

/*! CSS Used keyframes */
@-webkit-keyframes ripple{0%{-webkit-transform:scale(1);}75%{-webkit-transform:scale(1.25);opacity:1;}100%{-webkit-transform:scale(1.4);opacity:0;}}
@keyframes ripple{0%{transform:scale(1);}75%{transform:scale(1.25);opacity:1;}100%{transform:scale(1.4);opacity:0;}}


</style>
<?php get_footer(); ?>