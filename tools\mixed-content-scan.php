<?php
/*
 * Template Name: Mixed Content Scanner
 */
?>
<?php
/**
 * Enhanced Mixed Content Scanner - Multi-URL Support
 * Mở rộng để quét toàn bộ website với sitemap và crawling
 */

// AJAX Request Handler - XỬ LÝ TRƯỚC TIÊN
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_POST['website_url'])) {
    header('Content-Type: application/json; charset=utf-8');
    
    try {
        $url = trim($_POST['website_url']);
        $scan_method = $_POST['scan_method'] ?? 'single';
        $max_pages = $_POST['max_pages'] ?? '20';
        
        // Handle unlimited option
        if ($max_pages === 'unlimited') {
            $max_pages_int = PHP_INT_MAX; // No limit
            $is_unlimited = true;
        } else {
            $max_pages_int = intval($max_pages);
            $is_unlimited = false;
        }
        
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            throw new Exception('URL không hợp lệ');
        }
        
        if (!extension_loaded('curl')) {
            throw new Exception('cURL extension không được cài đặt');
        }
        
        // Increase limits for unlimited scans
        if ($is_unlimited) {
            set_time_limit(1800); // 30 minutes
            ini_set('memory_limit', '512M');
        }
        
        $scanner = new EnhancedMixedContentScanner();
        
        switch ($scan_method) {
            case 'sitemap':
                $result = $scanner->scanBySitemap($url, $max_pages_int, $is_unlimited);
                break;
            case 'crawl':
                // Crawl vẫn giới hạn để tránh vô hạn
                $crawl_limit = $is_unlimited ? 200 : $max_pages_int;
                $result = $scanner->scanByCrawling($url, $crawl_limit);
                break;
            case 'single':
            default:
                $result = $scanner->scanSingle($url);
                break;
        }
        
        echo json_encode($result, JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        echo json_encode([
            'error' => $e->getMessage(),
            'details' => 'Thử giảm số trang quét hoặc chọn phương thức khác'
        ], JSON_UNESCAPED_UNICODE);
    }
    exit;
}

class EnhancedMixedContentScanner {
    
    private $userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
    
    public function scanSingle($url) {
        $html = $this->fetchPage($url);
        
        if (!$html) {
            throw new Exception('Không thể tải trang. Kiểm tra URL hoặc kết nối internet.');
        }
        
        $mixedContent = $this->findMixedContent($html, $url);
        
        return [
            'success' => true,
            'scan_method' => 'single',
            'base_url' => $url,
            'total_urls' => 1,
            'mixed_content_count' => count($mixedContent),
            'pages_with_issues' => count($mixedContent) > 0 ? 1 : 0,
            'results' => [
                [
                    'url' => $url,
                    'mixed_content' => $mixedContent,
                    'status' => 'success'
                ]
            ]
        ];
    }
    
    public function scanBySitemap($baseUrl, $maxPages = 20, $isUnlimited = false) {
        $sitemapUrls = $this->getSitemapUrls($baseUrl);
        
        if (empty($sitemapUrls)) {
            // Fallback to single page if no sitemap
            return $this->scanSingle($baseUrl);
        }
        
        // Apply limit based on unlimited flag
        if ($isUnlimited) {
            $urlsToScan = $sitemapUrls; // No limit
        } else {
            $urlsToScan = array_slice($sitemapUrls, 0, $maxPages);
        }
        
        return $this->scanMultipleUrls($urlsToScan, $baseUrl, 'sitemap', count($sitemapUrls), $isUnlimited);
    }
    
    public function scanByCrawling($baseUrl, $maxPages = 15) {
        $urlsToScan = [$baseUrl];
        $discoveredUrls = [];
        $domain = parse_url($baseUrl, PHP_URL_HOST);
        
        // Crawl để tìm thêm URLs
        while (count($discoveredUrls) < $maxPages && !empty($urlsToScan)) {
            $currentUrl = array_shift($urlsToScan);
            
            if (in_array($currentUrl, $discoveredUrls)) {
                continue;
            }
            
            $discoveredUrls[] = $currentUrl;
            
            // Tìm thêm links nếu chưa đủ
            if (count($discoveredUrls) < $maxPages) {
                $newUrls = $this->extractLinksFromPage($currentUrl, $domain);
                foreach ($newUrls as $newUrl) {
                    if (!in_array($newUrl, $discoveredUrls) && !in_array($newUrl, $urlsToScan)) {
                        $urlsToScan[] = $newUrl;
                    }
                    
                    if (count($discoveredUrls) + count($urlsToScan) >= $maxPages) {
                        break;
                    }
                }
            }
        }
        
        return $this->scanMultipleUrls($discoveredUrls, $baseUrl, 'crawl');
    }
    
    private function scanMultipleUrls($urls, $baseUrl, $method, $totalFound = null, $isUnlimited = false) {
        $results = [];
        $mixedContentCount = 0;
        $pagesWithIssues = 0;
        $errors = 0;
        $processed = 0;
        
        // Batch processing for unlimited scans
        $batchSize = $isUnlimited ? 50 : count($urls);
        $totalUrls = count($urls);
        
        for ($batch = 0; $batch < ceil($totalUrls / $batchSize); $batch++) {
            $startIndex = $batch * $batchSize;
            $batchUrls = array_slice($urls, $startIndex, $batchSize);
            
            foreach ($batchUrls as $index => $url) {
                try {
                    $html = $this->fetchPage($url);
                    
                    if ($html) {
                        $mixedContent = $this->findMixedContent($html, $url);
                        
                        $results[] = [
                            'url' => $url,
                            'mixed_content' => $mixedContent,
                            'status' => 'success'
                        ];
                        
                        if (!empty($mixedContent)) {
                            $mixedContentCount += count($mixedContent);
                            $pagesWithIssues++;
                        }
                    } else {
                        $results[] = [
                            'url' => $url,
                            'mixed_content' => [],
                            'status' => 'error',
                            'error' => 'Không thể tải trang'
                        ];
                        $errors++;
                    }
                    
                    $processed++;
                    
                    // Delay để tránh overwhelming server (shorter for unlimited)
                    $delay = $isUnlimited ? 100000 : 300000; // 0.1s vs 0.3s
                    if (($startIndex + $index) < $totalUrls - 1) {
                        usleep($delay);
                    }
                    
                    // Memory cleanup for large scans
                    if ($isUnlimited && ($processed % 25 === 0)) {
                        unset($html, $mixedContent);
                        if (function_exists('gc_collect_cycles')) {
                            gc_collect_cycles();
                        }
                    }
                    
                } catch (Exception $e) {
                    $results[] = [
                        'url' => $url,
                        'mixed_content' => [],
                        'status' => 'error',
                        'error' => 'Timeout hoặc lỗi kết nối'
                    ];
                    $errors++;
                    $processed++;
                }
            }
            
            // Batch cleanup for unlimited scans
            if ($isUnlimited && function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }
        }
        
        return [
            'success' => true,
            'scan_method' => $method,
            'base_url' => $baseUrl,
            'total_urls' => $processed,
            'total_found' => $totalFound,
            'mixed_content_count' => $mixedContentCount,
            'pages_with_issues' => $pagesWithIssues,
            'errors' => $errors,
            'is_unlimited' => $isUnlimited,
            'results' => $results
        ];
    }
    
    private function getSitemapUrls($baseUrl) {
        $sitemapUrls = [
            $baseUrl . '/sitemap.xml',
            $baseUrl . '/sitemap_index.xml',
            $baseUrl . '/wp-sitemap.xml',
            $baseUrl . '/sitemap.xml.gz'
        ];
        
        $urls = [];
        
        // Try direct sitemap URLs
        foreach ($sitemapUrls as $sitemapUrl) {
            $content = $this->fetchPage($sitemapUrl);
            if ($content) {
                $extractedUrls = $this->parseSitemap($content);
                if (!empty($extractedUrls)) {
                    $urls = array_merge($urls, $extractedUrls);
                    break; // Found working sitemap
                }
            }
        }
        
        // Try robots.txt
        if (empty($urls)) {
            $robotsContent = $this->fetchPage($baseUrl . '/robots.txt');
            if ($robotsContent && preg_match_all('/Sitemap:\s*(.+)/i', $robotsContent, $matches)) {
                foreach ($matches[1] as $sitemapUrl) {
                    $content = $this->fetchPage(trim($sitemapUrl));
                    if ($content) {
                        $extractedUrls = $this->parseSitemap($content);
                        $urls = array_merge($urls, $extractedUrls);
                    }
                }
            }
        }
        
        return array_unique(array_filter($urls));
    }
    
    private function parseSitemap($content) {
        $urls = [];
        
        // Handle gzipped content
        if (substr($content, 0, 2) === "\x1f\x8b") {
            $content = gzdecode($content);
        }
        
        if (!$content) return $urls;
        
        // Remove namespaces for easier parsing
        $content = preg_replace('/xmlns[^=]*="[^"]*"/i', '', $content);
        
        // Try XML parsing
        libxml_use_internal_errors(true);
        $xml = simplexml_load_string($content);
        
        if ($xml === false) {
            // Fallback: extract URLs with regex
            if (preg_match_all('/<loc[^>]*>([^<]+)<\/loc>/i', $content, $matches)) {
                return $matches[1];
            }
            return $urls;
        }
        
        // Parse sitemap index
        if (isset($xml->sitemap)) {
            foreach ($xml->sitemap as $sitemap) {
                $sitemapUrl = (string)$sitemap->loc;
                if (!empty($sitemapUrl)) {
                    $sitemapContent = $this->fetchPage($sitemapUrl);
                    if ($sitemapContent) {
                        $subUrls = $this->parseSitemap($sitemapContent);
                        $urls = array_merge($urls, $subUrls);
                    }
                }
            }
        }
        
        // Parse regular sitemap
        if (isset($xml->url)) {
            foreach ($xml->url as $url) {
                $loc = (string)$url->loc;
                if (!empty($loc)) {
                    $urls[] = $loc;
                }
            }
        }
        
        return $urls;
    }
    
    private function extractLinksFromPage($url, $domain) {
        $html = $this->fetchPage($url);
        if (!$html) return [];
        
        $links = [];
        
        if (preg_match_all('/<a[^>]+href=["\']([^"\'#]+)["\'][^>]*>/i', $html, $matches)) {
            foreach ($matches[1] as $link) {
                $absoluteUrl = $this->makeAbsoluteUrl($link, $url);
                
                if ($absoluteUrl && 
                    parse_url($absoluteUrl, PHP_URL_HOST) === $domain &&
                    !$this->isFileUrl($absoluteUrl)) {
                    $links[] = $absoluteUrl;
                }
            }
        }
        
        return array_unique($links);
    }
    
    private function makeAbsoluteUrl($url, $baseUrl) {
        if (empty($url)) return null;
        
        // Already absolute
        if (preg_match('/^https?:\/\//', $url)) {
            return $url;
        }
        
        // Protocol relative
        if (strpos($url, '//') === 0) {
            return 'https:' . $url;
        }
        
        // Root relative
        if ($url[0] === '/') {
            $parsedBase = parse_url($baseUrl);
            return $parsedBase['scheme'] . '://' . $parsedBase['host'] . $url;
        }
        
        // Relative to current directory
        $basePath = dirname($baseUrl);
        return $basePath . '/' . ltrim($url, './');
    }
    
    private function isFileUrl($url) {
        $extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'pdf', 'doc', 'docx', 'zip', 'rar', 'mp3', 'mp4'];
        $path = parse_url($url, PHP_URL_PATH);
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        return in_array($extension, $extensions);
    }
    
    private function fetchPage($url) {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 20,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_USERAGENT => $this->userAgent,
            CURLOPT_HTTPHEADER => [
                'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language: en-US,en;q=0.5',
                'Cache-Control: no-cache'
            ]
        ]);
        
        $html = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($html === false || !empty($error) || $httpCode >= 400) {
            return false;
        }
        
        return $html;
    }
    
    private function findMixedContent($html, $pageUrl) {
        $mixedContent = [];
        
        // Chỉ kiểm tra HTTPS pages
        if (strpos($pageUrl, 'https://') !== 0) {
            return $mixedContent;
        }
        
        // Các pattern tìm mixed content
        $patterns = [
            'IMAGE' => '/<img[^>]+src=["\']http:\/\/([^"\']+)["\'][^>]*>/i',
            'SCRIPT' => '/<script[^>]+src=["\']http:\/\/([^"\']+)["\'][^>]*>/i',
            'CSS' => '/<link[^>]+href=["\']http:\/\/([^"\']+)["\'][^>]*>/i',
            'IFRAME' => '/<iframe[^>]+src=["\']http:\/\/([^"\']+)["\'][^>]*>/i',
            'VIDEO' => '/<video[^>]+src=["\']http:\/\/([^"\']+)["\'][^>]*>/i',
            'AUDIO' => '/<audio[^>]+src=["\']http:\/\/([^"\']+)["\'][^>]*>/i',
            'FORM' => '/<form[^>]+action=["\']http:\/\/([^"\']+)["\'][^>]*>/i',
            'OBJECT' => '/<object[^>]+data=["\']http:\/\/([^"\']+)["\'][^>]*>/i'
        ];
        
        foreach ($patterns as $type => $pattern) {
            if (preg_match_all($pattern, $html, $matches, PREG_SET_ORDER)) {
                foreach ($matches as $match) {
                    $httpUrl = 'http://' . $match[1];
                    $mixedContent[] = [
                        'type' => $type,
                        'url' => $httpUrl,
                        'tag' => htmlspecialchars(substr($match[0], 0, 100)) . '...'
                    ];
                }
            }
        }
        
        // Tìm CSS background-image HTTP
        if (preg_match_all('/background-image:\s*url\(["\']?http:\/\/([^"\')\s]+)/i', $html, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $httpUrl = 'http://' . $match[1];
                $mixedContent[] = [
                    'type' => 'CSS_BACKGROUND',
                    'url' => $httpUrl,
                    'tag' => htmlspecialchars($match[0])
                ];
            }
        }
        
        return $mixedContent;
    }
}

// Debug info (chỉ khi GET request với ?debug)
if (isset($_GET['debug'])) {
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Debug Info</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
            .success { background: #d4edda; color: #155724; }
            .error { background: #f8d7da; color: #721c24; }
        </style>
    </head>
    <body>
        <h2>🔧 Debug Information</h2>
        
        <div class="info">
            <strong>PHP Version:</strong> <?= PHP_VERSION ?><br>
            <strong>cURL Support:</strong> <?= extension_loaded('curl') ? '✅ Enabled' : '❌ Disabled' ?><br>
            <strong>OpenSSL Support:</strong> <?= extension_loaded('openssl') ? '✅ Enabled' : '❌ Disabled' ?><br>
            <strong>allow_url_fopen:</strong> <?= ini_get('allow_url_fopen') ? '✅ Enabled' : '❌ Disabled' ?><br>
            <strong>Max Execution Time:</strong> <?= ini_get('max_execution_time') ?> seconds<br>
            <strong>Memory Limit:</strong> <?= ini_get('memory_limit') ?>
        </div>
        
        <h3>🌐 Connectivity Test</h3>
        <?php
        $testUrls = ['https://example.com', 'https://google.com'];
        foreach ($testUrls as $testUrl) {
            echo "<div class='info'>";
            echo "<strong>Testing:</strong> $testUrl<br>";
            
            $scanner = new SimpleMixedContentScanner();
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $testUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 10,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_NOBODY => true
            ]);
            
            $result = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($result !== false && empty($error) && $httpCode < 400) {
                echo "<span class='success'>✅ Connection successful (HTTP $httpCode)</span>";
            } else {
                echo "<span class='error'>❌ Connection failed: $error (HTTP $httpCode)</span>";
            }
            echo "</div>";
        }
        ?>
        
        <p><a href="?" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">⬅️ Back to Scanner</a></p>
    </body>
    </html>
    <?php
    exit;
}
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔒 Mixed Content là gì? Công cụ quét tìm Mixed Content trên website</title>
    <meta property="og:url" content="https://vutruso.com/mixed-content-scan/">
    <link rel="canonical" href="https://vutruso.com/mixed-content-scan/">
    <meta name="description" content="Mixed Content là gì? Tìm hiểu nguyên nhân, tác động và cách nhận biết Mixed Content trên website HTTPS. Hướng dẫn chi tiết với FAQ đầy đủ cho người dùng.">
    <meta name="keywords" content="mixed content, https, ssl, bảo mật website, not secure, mixed content error, website security, ssl certificate">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:site_name" content="🔒 Mixed Content là gì? Tác động và cách nhận biết - Hướng dẫn HTTPS">
    <meta property="og:locale" content="vi_VN">
    <meta property="og:type" content="article">
    <meta property="og:title" content="🔒 Mixed Content là gì? Tác động và cách nhận biết - Hướng dẫn HTTPS">
    <meta property="og:description" content="Mixed Content là gì? Tìm hiểu nguyên nhân, tác động và cách nhận biết Mixed Content trên website HTTPS. Hướng dẫn chi tiết với FAQ đầy đủ cho người dùng.">
    <meta property="og:image" content="https://vutruso.com/wp-content/uploads/2025/08/mixed-content-scanner.png">
    <meta property="og:image:secure_url" content="https://vutruso.com/wp-content/uploads/2025/08/mixed-content-scanner.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:alt" content="Mixed Content - Hướng dẫn nhận biết và xử lý website HTTPS">
    <meta property="article:author" content="https://www.facebook.com/vutruso">
    <meta property="article:publisher" content="https://www.facebook.com/vutruso">
    <meta property="article:section" content="Website Security">
    <meta property="article:tag" content="Mixed Content, HTTPS, SSL, Website Security">
    <meta property="fb:admins" content="100002603951764">
    <meta property="fb:app_id" content="***************">
    
    <!-- Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@vutruso">
    <meta name="twitter:creator" content="@vutruso">
    <meta name="twitter:title" content="🔒 Mixed Content là gì? Tác động và cách nhận biết">
    <meta name="twitter:description" content="Mixed Content là gì? Tìm hiểu nguyên nhân, tác động và cách nhận biết Mixed Content trên website HTTPS. Hướng dẫn chi tiết với FAQ đầy đủ.">
    <meta name="twitter:image" content="https://vutruso.com/wp-content/uploads/2025/08/mixed-content-scanner.png">
    <meta name="twitter:image:alt" content="Mixed Content - Hướng dẫn nhận biết và xử lý website HTTPS">
    
    <!-- Additional SEO Meta Tags -->
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta name="author" content="Vũ Trụ Số">
    <meta name="theme-color" content="#007bff">
    
    <!-- Structured Data JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Article",
        "headline": "Mixed Content là gì? Tác động và cách nhận biết",
        "description": "Mixed Content là gì? Tìm hiểu nguyên nhân, tác động và cách nhận biết Mixed Content trên website HTTPS. Hướng dẫn chi tiết với FAQ đầy đủ cho người dùng.",
        "image": {
            "@type": "ImageObject",
            "url": "https://vutruso.com/wp-content/uploads/2025/08/mixed-content-scanner.png",
            "width": 1200,
            "height": 630
        },
        "author": {
            "@type": "Organization",
            "name": "Vũ Trụ Số",
            "url": "https://vutruso.com"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Vũ Trụ Số",
            "url": "https://vutruso.com",
            "logo": {
                "@type": "ImageObject",
                "url": "https://vutruso.com/wp-content/uploads/2020/07/vutruso_login.jpg",
                "width": 300,
                "height": 60
            }
        },
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": "https://vutruso.com/mixed-content-scan/"
        },
        "datePublished": "2025-08-07T10:00:00+07:00",
        "dateModified": "2025-08-07T10:00:00+07:00",
        "articleSection": "Website Security",
        "keywords": "mixed content, https, ssl, bảo mật website, not secure, mixed content error",
        "wordCount": 2500,
        "url": "https://vutruso.com/mixed-content-scan/",
        "inLanguage": "vi-VN",
        "isAccessibleForFree": true,
        "copyrightHolder": {
            "@type": "Organization",
            "name": "Vũ Trụ Số"
        }
    }
    </script>
    
    <!-- FAQ Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": [
            {
                "@type": "Question",
                "name": "Website tôi đã có SSL nhưng vẫn hiện \"Not Secure\", tại sao?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "SSL chỉ bảo vệ kết nối chính của website. Nếu website load nội dung từ các nguồn HTTP không an toàn khác, trình duyệt vẫn sẽ cảnh báo. Cần kiểm tra và chuyển tất cả các link sang HTTPS."
                }
            },
            {
                "@type": "Question",
                "name": "Mixed Content có nguy hiểm không?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Mixed Content có thể nguy hiểm vì: Dữ liệu có thể bị nghe lén qua kết nối HTTP, Hacker có thể chèn mã độc vào nội dung HTTP, Thông tin người dùng có thể bị đánh cắp."
                }
            },
            {
                "@type": "Question",
                "name": "Tại sao Google Analytics hoặc Facebook Pixel không hoạt động?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Nhiều khả năng code tracking đang sử dụng link HTTP cũ. Các dịch vụ này đều đã hỗ trợ HTTPS, cần cập nhật link mới."
                }
            },
            {
                "@type": "Question",
                "name": "Mixed Content có ảnh hưởng đến thứ hạng Google không?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Có. Google ưu tiên website HTTPS hoàn toàn và an toàn. Mixed Content có thể làm giảm điểm SEO và thứ hạng tìm kiếm."
                }
            },
            {
                "@type": "Question",
                "name": "Website tôi load chậm, có liên quan đến Mixed Content không?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Có thể. Trình duyệt phải kiểm tra và xử lý các cảnh báo bảo mật, có thể làm chậm tốc độ load trang."
                }
            },
            {
                "@type": "Question",
                "name": "Làm sao biết chính xác link nào đang gây Mixed Content?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Cách kiểm tra: 1) Mở DevTools (F12), 2) Vào tab Security, 3) Click \"View requests in Network Panel\", 4) Tìm các request có giao thức HTTP."
                }
            },
            {
                "@type": "Question",
                "name": "CDN có thể gây Mixed Content không?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Có, nếu CDN sử dụng link HTTP hoặc chưa cấu hình SSL đúng cách. Cần đảm bảo tất cả link CDN đều sử dụng HTTPS."
                }
            },
            {
                "@type": "Question",
                "name": "Mixed Content có tự khắc phục được không?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Không. Cần có sự can thiệp kỹ thuật để: Cập nhật tất cả link HTTP thành HTTPS, Cấu hình server redirect tự động, Sử dụng Content Security Policy."
                }
            },
            {
                "@type": "Question",
                "name": "Website ecommerce bị Mixed Content có nguy hiểm không?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Rất nguy hiểm vì: Khách hàng mất niềm tin khi thanh toán, Thông tin thẻ tín dụng có thể bị đánh cắp, Có thể vi phạm các chuẩn bảo mật PCI DSS."
                }
            },
            {
                "@type": "Question",
                "name": "Tôi có nên tắt HTTPS để tránh Mixed Content không?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Tuyệt đối không! HTTPS là tiêu chuẩn bảo mật hiện tại. Thay vào đó, nên khắc phục Mixed Content để có website vừa an toàn vừa không có cảnh báo."
                }
            }
        ]
    }
    </script>

    <!-- HowTo Schema (Optional) -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "HowTo",
        "name": "Cách nhận biết Mixed Content trên website",
        "description": "Hướng dẫn chi tiết cách nhận biết Mixed Content trên website HTTPS",
        "image": "https://vutruso.com/wp-content/uploads/2025/08/mixed-content-scanner.png",
        "totalTime": "PT5M",
        "estimatedCost": {
            "@type": "MonetaryAmount",
            "currency": "VND",
            "value": "0"
        },
        "supply": [
            {
                "@type": "HowToSupply",
                "name": "Trình duyệt web (Chrome, Firefox, Safari)"
            },
            {
                "@type": "HowToSupply", 
                "name": "Website có SSL certificate"
            }
        ],
        "tool": [
            {
                "@type": "HowToTool",
                "name": "Developer Tools (DevTools)"
            }
        ],
        "step": [
            {
                "@type": "HowToStep",
                "name": "Kiểm tra thanh địa chỉ",
                "text": "Kiểm tra thanh địa chỉ trình duyệt xem có biểu tượng cảnh báo hay không",
            },
            {
                "@type": "HowToStep",
                "name": "Mở Developer Tools",
                "text": "Nhấn F12 để mở Developer Tools và kiểm tra tab Console",
            },
            {
                "@type": "HowToStep",
                "name": "Kiểm tra Security tab",
                "text": "Vào tab Security trong DevTools và click 'View requests in Network Panel'",
            },
            {
                "@type": "HowToStep",
                "name": "Tìm HTTP requests",
                "text": "Tìm các request có giao thức HTTP trong danh sách Network requests",
            }
        ]
    }
    </script>
    
    <style>
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}
select {
    width: 100%;
    padding: 15px 40px 15px 20px; /* padding-right: 40px */
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
    background: white;
}
body {
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	min-height: 100vh;
	padding: 20px;
}

.container {
	max-width: 969px;
	margin: 0 auto;
	background: white;
	border-radius: 15px;
	box-shadow: 0 20px 40px rgba(0,0,0,0.1);
	overflow: hidden;
}

.header {
	background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
	color: white;
	padding: 30px;
	text-align: center;
	position: relative;
}

.debug-link {
	position: absolute;
	top: 15px;
	right: 20px;
	background: rgba(255,255,255,0.2);
	color: white;
	padding: 8px 15px;
	border-radius: 20px;
	text-decoration: none;
	font-size: 14px;
	transition: background 0.3s;
    display:none
}

.debug-link:hover {
	background: rgba(255,255,255,0.3);
}

.header h1 {
	font-size: 2.2em;
	margin-bottom: 10px;
}

.header p {
	font-size: 1.1em;
	opacity: 0.9;
}

.main-content {
	padding: 10px 30px;
}

h2 {
	margin-bottom: 14px;
	font-size: 16px;
	font-weight: 600;
}

h3 {
	margin-bottom: 14px;
	font-weight: 600;
}

ol li,
.tips li {
	line-height: 30px;
	list-style: none;
}

strong {
	margin-bottom: 10px;
}

.form-section {
	background: #f8f9fa;
	border-radius: 10px;
	padding: 15px;
	margin-bottom: 30px;
	border: 2px solid #e9ecef;
}

.form-group {
	margin-bottom: 25px;
}

label {
	display: block;
	margin-bottom: 8px;
	font-weight: 600;
	color: #2c3e50;
	font-size: 16px;
}

input[type="url"], select {
	width: 100%;
	padding: 15px 20px;
	border: 2px solid #ddd;
	border-radius: 8px;
	font-size: 16px;
	transition: border-color 0.3s;
	background: white;
}

input[type="url"]:focus, select:focus {
	outline: none;
	border-color: #667eea;
	box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	padding: 15px 40px;
	border: none;
	border-radius: 8px;
	font-size: 18px;
	font-weight: 600;
	cursor: pointer;
	transition: transform 0.3s, box-shadow 0.3s;
}

.btn:hover {
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.btn:disabled {
	opacity: 0.6;
	transform: none;
	cursor: not-allowed;
	box-shadow: none;
}

.loading {
	text-align: center;
	padding: 40px;
	display: none;
}

.spinner {
	border: 4px solid #f3f3f3;
	border-top: 4px solid #667eea;
	border-radius: 50%;
	width: 50px;
	height: 50px;
	animation: spin 1s linear infinite;
	margin: 0 auto 20px;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

.alert {
	padding: 20px;
	border-radius: 8px;
	margin-bottom: 20px;
	font-size: 16px;
}

.alert-success {
	background-color: #d4edda;
	border: 1px solid #c3e6cb;
	color: #155724;
}

.alert-warning {
	background-color: #fff3cd;
	border: 1px solid #ffeeba;
	color: #856404;
	padding: 10px 15px;
	border-radius: 8px;
	margin-bottom: 10px;
}

.alert-error {
	background-color: #f8d7da;
	border: 1px solid #f5c6cb;
	color: #721c24;
}

.result-item {
	background: white;
	border: 1px solid #ddd;
	border-radius: 8px;
	margin-bottom: 15px;
	padding: 15px;
}

.mixed-content-item {
	background: #fff5f5;
	border-left: 4px solid #dc3545;
	padding: 10px 20px;
	margin: 0;
	border-radius: 0 6px 6px 0;
    padding-bottom: 10px!importance
}

.content-type {
	display: inline-block;
	background: #667eea;
	color: white;
	padding: 3px 10px;
	border-radius: 15px;
	font-size: 11px;
	font-weight: 600;
}

.example-urls {
	background: #f8f9fa;
	border-radius: 6px;
	padding: 10px;
	margin-top: 8px;
	font-size: 14px;
	color: #6c757d;
}

.url-link {
	color: #667eea;
	text-decoration: none;
	margin-right: 15px;
}

.url-link:hover {
	text-decoration: underline;
}

.alert-warning span {
	margin-left: 26px;
}

.btn-submit{
    margin-bottom: 15px
}

#results>div>div{
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 8px;
    padding-bottom: 0
}

h4 {
    margin-bottom: 10px;
}


.definition-section, .causes-section, .impact-section, 
.detection-section, .faq-section, .conclusion-section {
	margin-bottom: 0px;
	padding: 10px 0;
}

.example-box {
	background: #f8f9fa;
	border-left: 4px solid #007bff;
	padding: 15px;
	margin: 20px 0;
}

.causes-grid, .importance-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(48%, 2fr));
	gap: 5px;
	margin: 20px 0;
}

.cause-item, .importance-item {
	background: #fff;
	border: 1px solid #e9ecef;
	border-radius: 8px;
	padding: 20px;
	box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-indicators > div {
	padding: 8px 12px;
	margin: 5px 0;
	border-radius: 4px;
}

.status-safe {
	background: #d4edda;
	color: #155724;
}

.status-mixed {
	background: #fff3cd;
	color: #856404;
}

.status-unsafe {
	background: #f8d7da;
	color: #721c24;
}

.console-example {
	background: #2d3748;
	color: #e2e8f0;
	padding: 15px;
	border-radius: 4px;
	font-family: monospace;
	margin: 10px 0;
}

.faq-item {
	border-bottom: 1px solid #e9ecef;
	padding: 0;
}

.faq-item:last-child {
	border-bottom: none;
}

.call-to-action {
	background: #e7f3ff;
	border-radius: 8px;
	padding: 20px;
	text-align: center;
	margin: 30px 0;
	margin-bottom: 0;
}

.box-content ul {
	margin-left: 20px;
    margin-bottom: 10px;
}

.cause-item h3 {
	font-size: 16px;
}

.box-content ul li {
	line-height: 25px;
}

.box-content h2 {
	font-size: 20px;
}

.status-indicators {
	margin-bottom: 15px;
}

.box-content p {
	margin-bottom: 10px;
}

.box-content p:last-child {
	margin-bottom: 0px;
}


/* FAQ Accordion Styles */

.faq-item {
	border: 1px solid #e9ecef;
	border-radius: 8px;
	margin-bottom: 10px;
	overflow: hidden;
	background: #fff;
	box-shadow: 0 2px 4px rgba(0,0,0,0.05);
	transition: box-shadow 0.3s ease;
}

.faq-item:hover {
	box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.faq-question {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px;
	background: #f8f9fa;
	cursor: pointer;
	user-select: none;
	transition: background-color 0.3s ease;
	border-bottom: 1px solid #e9ecef;
}

.faq-question:hover {
	background: #e9ecef;
}

.faq-question.active {
	background: #007bff;
	color: white;
}

.question-text {
	font-weight: 600;
	font-size: 16px;
	flex: 1;
	margin-right: 15px;
}

.toggle-icon {
	font-size: 20px;
	font-weight: bold;
	transition: transform 0.3s ease;
	min-width: 24px;
	text-align: center;
}

.faq-question.active .toggle-icon {
	transform: rotate(45deg);
}

.faq-answer {
	padding: 0 20px;
	max-height: 0;
	overflow: hidden;
	transition: max-height 0.4s ease, padding 0.4s ease;
	background: #fff;
}

.faq-answer.open {
	max-height: 500px;
	padding: 20px;
}

.faq-answer p {
	margin-bottom: 15px;
	line-height: 1.6;
}

.faq-answer ul, .faq-answer ol {
	margin: 10px 0;
	padding-left: 25px;
}

.faq-answer li {
	margin-bottom: 8px;
	line-height: 1.5;
}

/* Responsive Design */





@media screen and ( max-width: 650px ) { 
    .main-content {
        padding: 10px 15px;
    }
    .header {
        padding: 30px 10px;
    }
    .form-section {
        margin-bottom: 15px;
    }
    .form-group {
        margin-bottom: 15px;
    }
    #max-pages-group{
        margin-bottom: 20px
    }
    #results div {
        padding: 10px;
        margin-bottom: 0;
        border-radius: 8px;
    }
    #results>div {
        gap: 5px;
    }
    #alert-warning .alert-warning{
    margin-bottom: 10px;
    }

	.faq-question {
		padding: 15px;
	}

	.question-text {
		font-size: 14px;
		margin-right: 10px;
	}

	.toggle-icon {
		font-size: 18px;
	}

	.faq-answer.open {
		padding: 15px;
	}
}

/* Animation for smooth opening */
@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(-10px);
	}

	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.faq-answer.open {
	animation: fadeIn 0.3s ease;
}

    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="?debug=1" class="debug-link">🔧 Debug</a>
            <h1>🔍 Mixed Content Scanner</h1>
            <p>Công cụ giúp quét một trang, sitemap hoặc crawl toàn bộ website để tìm ra link mixed content</p>
        </div>
        
        <div class="main-content">
            <div class="form-section">
                <h2 style="color: #2c3e50; margin-bottom: 20px;">🎯 Quét Mixed Content</h2>
                
                <form id="scanForm">
                    <div class="form-group">
                        <label for="website_url">
                            <span style="color: #e74c3c;">*</span> URL Website (HTTPS):
                        </label>
                        <input type="url" 
                               id="website_url" 
                               name="website_url" 
                               placeholder="https://example.com" 
                               value=""
                               required>
                        
                        <div class="example-urls">
                            <strong>Thử với:</strong>
                            <a href="#" class="url-link" onclick="setUrl('https://googlesamples.github.io/web-fundamentals/fundamentals/security/prevent-mixed-content/simple-example.html')">simple-example</a>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="scan_method">Phương thức quét:</label>
                        <select id="scan_method" name="scan_method" onchange="updateMethodInfo()">
                            <option value="single">📋 Quét một trang (Nhanh nhất)</option>
                            <option value="sitemap">📄 Quét theo Sitemap (Khuyến nghị)</option>
                            <option value="crawl">🕷️ Crawl từ homepage (Tự động tìm link)</option>
                        </select>
                        
                        <div id="method-info" style="background: #e8f4fd; padding: 10px; border-radius: 6px; margin-top: 8px; font-size: 14px; color: #0c5460;">
                            <strong>Quét một trang:</strong> Chỉ kiểm tra URL bạn nhập - Nhanh và ổn định
                        </div>
                    </div>
                    
                    <div class="form-group" id="max-pages-group" style="display: none;">
                        <label for="max_pages">Số trang quét:</label>
                        <select id="max_pages" name="max_pages">
                            <option value="10">10 trang (Nhanh - 30 giây)</option>
                            <option value="20" selected>20 trang (Khuyến nghị - 1 phút)</option>
                            <option value="50">50 trang (Chậm - 2-3 phút)</option>
                            <option value="100">100 trang (Rất chậm - 5-8 phút)</option>
                            <option value="unlimited">🚀 Toàn bộ sitemap (Không giới hạn)</option>
                        </select>
                        <small style="color: #6c757d; font-size: 14px; display: block; margin-top: 5px;">
                            <span id="pages-note">Số trang càng nhiều thì thời gian quét càng lâu</span>
                        </small>
                    </div>
                    
                    <div class="btn-submit" style="text-align: center;">
                        <button type="submit" class="btn">
                            🚀 <span id="btn-text">Quét Mixed Content</span>
                        </button>
                    </div>
                </form>
                
                <div class="tips">
                    <h4 style="margin: 0 0 10px 0; color: #0c5460;">💡 Phương thức quét:</h4>
                    <ul style="margin: 0; padding-left: 1px; color: #0c5460;">
                        <li><strong>📋 Một trang:</strong> Nhanh nhất, kiểm tra URL cụ thể</li>
                        <li><strong>📄 Sitemap:</strong> Đọc sitemap.xml để quét toàn bộ website</li>
                        <li><strong>🕷️ Crawl:</strong> Bắt đầu từ homepage, tự động tìm và quét các link</li>
                        <li><strong>🚀 Toàn bộ sitemap:</strong> Quét không giới hạn - có thể hàng nghìn trang</li>
                        <li><strong>⏱️ Thời gian:</strong> 1 trang (~3s), 20 trang (~1 phút), Toàn bộ (10-30 phút)</li>
                    </ul>
                </div>
            </div>
            
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>Đang quét website... Vui lòng đợi</p>
                <div style="max-width: 400px; margin: 20px auto;">
                    <div style="background: #f0f0f0; border-radius: 10px; height: 20px; overflow: hidden;">
                        <div id="progress-bar" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); height: 100%; width: 0%; transition: width 0.3s;"></div>
                    </div>
                    <div id="progress-text" style="text-align: center; margin-top: 10px; font-size: 14px; color: #666;">Đang chuẩn bị...</div>
                </div>
            </div>
            
            <div id="results"></div>

            <div class="box-content" style="background: #f8f9fa;border-radius: 15px;padding: 15px 25px;margin-bottom: 0px;box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                    <h3 style="margin-bottom: 10px; font-size: 1.5em;">Mixed content là gì?</h3>

                    <div class="definition-section">
                        <h2>Mixed Content là gì?</h2>
                        <p><strong>Mixed Content</strong> (nội dung hỗn hợp) là hiện tượng xảy ra khi một website có chứng chỉ SSL (hiển thị https://) nhưng lại tải một số nội dung qua kết nối không bảo mật (http://).</p>
                        
                        <p>Nói đơn giản: Website của bạn vừa có phần <strong>an toàn</strong> (https) vừa có phần <strong>không an toàn</strong> (http) trộn lẫn với nhau.</p>
                        
                        <div class="example-box">
                            <h3>Ví dụ thực tế:</h3>
                            <ul>
                                <li>Website chính: <code>https://example.com</code> (an toàn)</li>
                                <li>Nhưng có hình ảnh từ: <code>http://images.example.com/logo.jpg</code> (không an toàn)</li>
                                <li>Hoặc load font từ: <code>http://fonts.googleapis.com/css</code> (không an toàn)</li>
                            </ul>
                        </div>
                    </div>     

                    <div class="causes-section">
                        <h2>Tại sao lại có Mixed Content?</h2>
                        <p>Mixed Content thường xảy ra khi:</p>
                        
                        <div class="causes-grid">
                            <div class="cause-item">
                                <h3>1. Website mới chuyển từ HTTP sang HTTPS</h3>
                                <p>Nhưng chưa cập nhật hết các link nội bộ</p>
                            </div>
                            
                            <div class="cause-item">
                                <h3>2. Sử dụng dịch vụ bên thứ 3</h3>
                                <p>Chưa hỗ trợ HTTPS (như CDN cũ, widget, analytics)</p>
                            </div>
                            
                            <div class="cause-item">
                                <h3>3. Copy content từ website khác</h3>
                                <p>Có chứa link HTTP</p>
                            </div>
                            
                            <div class="cause-item">
                                <h3>4. Code cũ</h3>
                                <p>Được viết từ thời chưa có HTTPS phổ biến</p>
                            </div>
                        </div>
                    </div>

                    <div class="impact-section">
                        <h2>Tác động của Mixed Content</h2>
                        
                        <div class="impact-tabs">
                            <div class="tab-content" id="user-impact">
                                <h3>1. Đối với người dùng</h3>
                                <ul>
                                    <li><strong>Cảnh báo bảo mật</strong>: Trình duyệt hiển thị "Not Secure" hoặc biểu tượng cảnh báo màu đỏ</li>
                                    <li><strong>Mất niềm tin</strong>: Người dùng có thể nghĩ website không an toàn</li>
                                    <li><strong>Trải nghiệm kém</strong>: Một số nội dung có thể không hiển thị</li>
                                </ul>
                            </div>
                            
                            <div class="tab-content" id="website-impact">
                                <h3>2. Đối với website</h3>
                                <ul>
                                    <li><strong>Giảm uy tín</strong>: Khách hàng có thể rời khỏi website</li>
                                    <li><strong>Ảnh hưởng SEO</strong>: Google ưu tiên website an toàn trong kết quả tìm kiếm</li>
                                    <li><strong>Giảm conversion</strong>: Khách hàng ngại nhập thông tin thanh toán</li>
                                </ul>
                            </div>
                            
                            <div class="tab-content" id="technical-impact">
                                <h3>3. Những gì bị ảnh hưởng</h3>
                                <ul>
                                    <li><strong>Hình ảnh</strong> load từ HTTP có thể hiển thị cảnh báo</li>
                                    <li><strong>JavaScript, CSS</strong> từ HTTP có thể bị chặn hoàn toàn</li>
                                    <li><strong>Iframe, Video</strong> từ HTTP có thể không hoạt động</li>
                                    <li><strong>Form, API calls</strong> có thể gặp lỗi bảo mật</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="detection-section">
                        <h2>Cách nhận biết Mixed Content</h2>
                        
                        <div class="detection-methods">
                            <div class="method">
                                <h3>1. Kiểm tra thanh địa chỉ trình duyệt</h3>
                                <div class="status-indicators">
                                    <div class="status-safe">✅ <strong>An toàn</strong>: Biểu tượng ổ khóa xanh + "Secure"</div>
                                    <div class="status-mixed">⚠️ <strong>Mixed Content</strong>: Biểu tượng cảnh báo + "Not Secure"</div>
                                    <div class="status-unsafe">❌ <strong>Không an toàn</strong>: Biểu tượng cảnh báo đỏ</div>
                                </div>
                            </div>
                            
                            <div class="method">
                                <h3>2. Thông báo trong Console</h3>
                                <p>Mở Developer Tools (F12) và xem tab Console, bạn sẽ thấy cảnh báo như:</p>
                                <div class="console-example">
                                    <code>Mixed Content: The page at 'https://example.com' was loaded over HTTPS, but requested an insecure resource 'http://...'</code>
                                </div>
                                <img style=" max-width: 100%; "src="https://vutruso.com/wp-content/uploads/2025/08/mixed-content-the-page-at.webp" alt="Mixed Content Errors in Chrome" itemprop="image">
                            </div>
                            
                            <div class="method">
                                <h3>3. Các trường hợp thường gặp</h3>
                                <ul>
                                    <li>Website hiển thị "Not Secure" dù đã có SSL</li>
                                    <li>Một số hình ảnh không hiển thị</li>
                                    <li>Widget social media (Facebook, Twitter) không hoạt động</li>
                                    <li>Google Analytics hoặc tracking code lỗi</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="faq-section">
                        <h2>FAQ - Câu hỏi thường gặp</h2>
                        
                        <div class="faq-accordion">
                            <div class="faq-item">
                                <div class="faq-question" onclick="toggleFAQ(this)">
                                    <span class="question-text">❓ Website tôi đã có SSL nhưng vẫn hiện "Not Secure", tại sao?</span>
                                    <span class="toggle-icon">+</span>
                                </div>
                                <div class="faq-answer">
                                    <p>SSL chỉ bảo vệ kết nối chính của website. Nếu website load nội dung từ các nguồn HTTP không an toàn khác, trình duyệt vẫn sẽ cảnh báo. Cần kiểm tra và chuyển tất cả các link sang HTTPS.</p>
                                </div>
                            </div>
                            
                            <div class="faq-item">
                                <div class="faq-question" onclick="toggleFAQ(this)">
                                    <span class="question-text">❓ Mixed Content có nguy hiểm không?</span>
                                    <span class="toggle-icon">+</span>
                                </div>
                                <div class="faq-answer">
                                    <p>Có thể nguy hiểm vì:</p>
                                    <ul>
                                        <li>Dữ liệu có thể bị nghe lén qua kết nối HTTP</li>
                                        <li>Hacker có thể chèn mã độc vào nội dung HTTP</li>
                                        <li>Thông tin người dùng có thể bị đánh cắp</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="faq-item">
                                <div class="faq-question" onclick="toggleFAQ(this)">
                                    <span class="question-text">❓ Tại sao Google Analytics hoặc Facebook Pixel không hoạt động?</span>
                                    <span class="toggle-icon">+</span>
                                </div>
                                <div class="faq-answer">
                                    <p>Nhiều khả năng code tracking đang sử dụng link HTTP cũ. Các dịch vụ này đều đã hỗ trợ HTTPS, cần cập nhật link mới.</p>
                                </div>
                            </div>
                            
                            <div class="faq-item">
                                <div class="faq-question" onclick="toggleFAQ(this)">
                                    <span class="question-text">❓ Mixed Content có ảnh hưởng đến thứ hạng Google không?</span>
                                    <span class="toggle-icon">+</span>
                                </div>
                                <div class="faq-answer">
                                    <p>Có. Google ưu tiên website HTTPS hoàn toàn và an toàn. Mixed Content có thể làm giảm điểm SEO và thứ hạng tìm kiếm.</p>
                                </div>
                            </div>
                            
                            <div class="faq-item">
                                <div class="faq-question" onclick="toggleFAQ(this)">
                                    <span class="question-text">❓ Website tôi load chậm, có liên quan đến Mixed Content không?</span>
                                    <span class="toggle-icon">+</span>
                                </div>
                                <div class="faq-answer">
                                    <p>Có thể. Trình duyệt phải kiểm tra và xử lý các cảnh báo bảo mật, có thể làm chậm tốc độ load trang.</p>
                                </div>
                            </div>
                            
                            <div class="faq-item">
                                <div class="faq-question" onclick="toggleFAQ(this)">
                                    <span class="question-text">❓ Làm sao biết chính xác link nào đang gây Mixed Content?</span>
                                    <span class="toggle-icon">+</span>
                                </div>
                                <div class="faq-answer">
                                    <ol>
                                        <li>Mở DevTools (F12)</li>
                                        <li>Vào tab Security</li>
                                        <li>Click "View requests in Network Panel"</li>
                                        <li>Tìm các request có giao thức HTTP</li>
                                    </ol>
                                </div>
                            </div>
                            
                            <div class="faq-item">
                                <div class="faq-question" onclick="toggleFAQ(this)">
                                    <span class="question-text">❓ CDN có thể gây Mixed Content không?</span>
                                    <span class="toggle-icon">+</span>
                                </div>
                                <div class="faq-answer">
                                    <p>Có, nếu CDN sử dụng link HTTP hoặc chưa cấu hình SSL đúng cách. Cần đảm bảo tất cả link CDN đều sử dụng HTTPS.</p>
                                </div>
                            </div>
                            
                            <div class="faq-item">
                                <div class="faq-question" onclick="toggleFAQ(this)">
                                    <span class="question-text">❓ Mixed Content có tự khắc phục được không?</span>
                                    <span class="toggle-icon">+</span>
                                </div>
                                <div class="faq-answer">
                                    <p>Không. Cần có sự can thiệp kỹ thuật để:</p>
                                    <ul>
                                        <li>Cập nhật tất cả link HTTP thành HTTPS</li>
                                        <li>Cấu hình server redirect tự động</li>
                                        <li>Sử dụng Content Security Policy</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="faq-item">
                                <div class="faq-question" onclick="toggleFAQ(this)">
                                    <span class="question-text">❓ Website ecommerce bị Mixed Content có nguy hiểm không?</span>
                                    <span class="toggle-icon">+</span>
                                </div>
                                <div class="faq-answer">
                                    <p>Rất nguy hiểm vì:</p>
                                    <ul>
                                        <li>Khách hàng mất niềm tin khi thanh toán</li>
                                        <li>Thông tin thẻ tín dụng có thể bị đánh cắp</li>
                                        <li>Có thể vi phạm các chuẩn bảo mật PCI DSS</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="faq-item">
                                <div class="faq-question" onclick="toggleFAQ(this)">
                                    <span class="question-text">❓ Tôi có nên tắt HTTPS để tránh Mixed Content không?</span>
                                    <span class="toggle-icon">+</span>
                                </div>
                                <div class="faq-answer">
                                    <p>Tuyệt đối không! HTTPS là tiêu chuẩn bảo mật hiện tại. Thay vào đó, nên khắc phục Mixed Content để có website vừa an toàn vừa không có cảnh báo.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="conclusion-section">
                        <h2>Tầm quan trọng của việc khắc phục</h2>
                        
                        <p>Mixed Content không chỉ là vấn đề kỹ thuật mà còn ảnh hưởng trực tiếp đến:</p>
                        
                        <div class="importance-grid">
                            <div class="importance-item">
                                <h3>Uy tín thương hiệu</h3>
                                <p>Khách hàng tin tưởng website an toàn</p>
                            </div>
                            
                            <div class="importance-item">
                                <h3>Doanh thu</h3>
                                <p>Giảm tỷ lệ khách hàng rời bỏ giỏ hàng</p>
                            </div>
                            
                            <div class="importance-item">
                                <h3>SEO</h3>
                                <p>Cải thiện thứ hạng tìm kiếm</p>
                            </div>
                            
                            <div class="importance-item">
                                <h3>Tuân thủ pháp luật</h3>
                                <p>Đáp ứng các quy định về bảo mật dữ liệu</p>
                            </div>
                        </div>
                        
                        <div class="call-to-action">
                            <p><em>Lưu ý: Nếu website của bạn đang gặp vấn đề Mixed Content, hãy liên hệ với đội ngũ kỹ thuật để được hỗ trợ khắc phục một cách chuyên nghiệp và an toàn.</em></p>
                        </div>
                    </div>
                    
            </div>

        </div>
    </div>

    <script>
        function simulateProgress(method, maxPages) {
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');
            
            let progress = 0;
            let interval;
            
            if (method === 'single') {
                // Fast progress for single page
                interval = setInterval(() => {
                    progress += Math.random() * 20 + 10;
                    if (progress >= 90) {
                        progress = 90;
                        clearInterval(interval);
                    }
                    progressBar.style.width = progress + '%';
                    progressText.textContent = `${Math.round(progress)}% - Đang tải trang...`;
                }, 500);
            } else if (maxPages === 'unlimited') {
                // Special handling for unlimited scans
                let currentPage = 0;
                interval = setInterval(() => {
                    currentPage += Math.floor(Math.random() * 3) + 1; // 1-3 pages per interval
                    progress += Math.random() * 2 + 1; // Slower progress increase
                    
                    if (progress >= 85) {
                        progress = 85;
                        clearInterval(interval);
                        progressText.textContent = '85% - Đang hoàn thiện quét toàn bộ sitemap...';
                    } else {
                        progressText.textContent = `${Math.round(progress)}% - Đã quét ${currentPage} trang (Toàn bộ sitemap)`;
                    }
                    
                    progressBar.style.width = progress + '%';
                }, 1500); // Slower updates for unlimited
            } else {
                // Normal progress for limited pages
                const totalPages = parseInt(maxPages);
                let currentPage = 0;
                
                interval = setInterval(() => {
                    const pageProgress = Math.random() * 5 + 2;
                    progress += pageProgress;
                    
                    if (progress >= 85) {
                        progress = 85;
                        clearInterval(interval);
                        progressText.textContent = '85% - Đang hoàn thiện...';
                    } else {
                        currentPage = Math.floor((progress / 85) * totalPages);
                        progressText.textContent = `${Math.round(progress)}% - Đang quét trang ${currentPage}/${totalPages}`;
                    }
                    
                    progressBar.style.width = progress + '%';
                }, 800);
            }
        }
        
        function updateMethodInfo() {
            const method = document.getElementById('scan_method').value;
            const methodInfo = document.getElementById('method-info');
            const maxPagesGroup = document.getElementById('max-pages-group');
            const btnText = document.getElementById('btn-text');
            
            switch(method) {
                case 'single':
                    methodInfo.innerHTML = '<strong>Quét một trang:</strong> Chỉ kiểm tra URL bạn nhập - Nhanh và ổn định';
                    maxPagesGroup.style.display = 'none';
                    btnText.textContent = 'Quét một trang';
                    break;
                case 'sitemap':
                    methodInfo.innerHTML = '<strong>Quét theo Sitemap:</strong> Tự động đọc sitemap.xml để quét toàn bộ website - Nhanh và đầy đủ';
                    maxPagesGroup.style.display = 'block';
                    btnText.textContent = 'Quét theo Sitemap';
                    break;
                case 'crawl':
                    methodInfo.innerHTML = '<strong>Crawl từ homepage:</strong> Bắt đầu từ trang chủ, tự động tìm và quét các link - Có thể chậm';
                    maxPagesGroup.style.display = 'block';
                    btnText.textContent = 'Crawl website';
                    break;
            }
        }
        
        // Update pages note when selection changes
        document.addEventListener('DOMContentLoaded', function() {
            const maxPagesSelect = document.getElementById('max_pages');
            const pagesNote = document.getElementById('pages-note');
            
            maxPagesSelect.addEventListener('change', function() {
                const value = this.value;
                if (value === 'unlimited') {
                    pagesNote.innerHTML = '<strong style="color: #e74c3c;">⚠️ Toàn bộ sitemap:</strong> Có thể mất 10-30 phút tùy theo kích thước website';
                    pagesNote.style.background = '#fff3cd';
                    pagesNote.style.padding = '8px';
                    pagesNote.style.borderRadius = '4px';
                    pagesNote.style.border = '1px solid #ffeeba';
                } else {
                    pagesNote.innerHTML = 'Số trang càng nhiều thì thời gian quét càng lâu';
                    pagesNote.style.background = 'transparent';
                    pagesNote.style.padding = '0';
                    pagesNote.style.borderRadius = '0';
                    pagesNote.style.border = 'none';
                }
            });
        });
        
        function setUrl(url) {
            document.getElementById('website_url').value = url;
        }
        
        document.getElementById('scanForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const url = document.getElementById('website_url').value.trim();
            const method = document.getElementById('scan_method').value;
            const maxPages = document.getElementById('max_pages').value;
            
            if (!url) {
                alert('Vui lòng nhập URL website!');
                return;
            }
            
            if (!url.startsWith('https://')) {
                if (!confirm('URL không bắt đầu bằng https://.\n\nMixed content chỉ xảy ra trên trang HTTPS.\nBạn có muốn tiếp tục?')) {
                    return;
                }
            }
            
            // Estimate time based on method and pages
            let estimatedTime = '';
            let isUnlimited = (maxPages === 'unlimited');
            
            if (method === 'single') {
                estimatedTime = '3-5 giây';
            } else if (isUnlimited) {
                estimatedTime = '10-30 phút (hoặc hơn tùy kích thước website)';
            } else if (method === 'sitemap') {
                estimatedTime = `${Math.ceil(maxPages * 0.5)} giây - ${Math.ceil(maxPages * 1)} phút`;
            } else {
                estimatedTime = `${Math.ceil(maxPages * 1)} phút - ${Math.ceil(maxPages * 2)} phút`;
            }
            
            if (method !== 'single') {
                let confirmMessage = '';
                if (isUnlimited) {
                    confirmMessage = `🚀 QUÉT TOÀN BỘ SITEMAP\n\n` +
                                   `⚠️ CẢNH BÁO: Đây là quét không giới hạn!\n\n` +
                                   `• Thời gian: ${estimatedTime}\n` +
                                   `• Website lớn có thể có hàng nghìn trang\n` +
                                   `• Có thể gây tải cho server của bạn\n` +
                                   `• Đề nghị chạy vào giờ ít traffic\n\n` +
                                   `Bạn có chắc chắn muốn tiếp tục?`;
                } else {
                    confirmMessage = `Bạn đang chọn quét ${method === 'sitemap' ? 'theo sitemap' : 'crawl'} với tối đa ${maxPages} trang.\n\nThời gian ước tính: ${estimatedTime}\n\nTiếp tục?`;
                }
                
                if (!confirm(confirmMessage)) {
                    return;
                }
            }
            
            const formData = new FormData();
            formData.append('website_url', url);
            formData.append('scan_method', method);
            formData.append('max_pages', maxPages);
            
            const loadingDiv = document.getElementById('loading');
            const resultsDiv = document.getElementById('results');
            const form = document.getElementById('scanForm');
            
            // Show loading with progress
            form.style.display = 'none';
            loadingDiv.style.display = 'block';
            resultsDiv.innerHTML = '';
            
            // Simulate progress based on method and estimated time
            simulateProgress(method, maxPages);
            
            // Update loading text based on method
            const loadingText = loadingDiv.querySelector('p');
            if (method === 'single') {
                loadingText.textContent = 'Đang quét trang...';
            } else if (method === 'sitemap') {
                if (maxPages === 'unlimited') {
                    loadingText.textContent = 'Đang quét toàn bộ sitemap (không giới hạn)...';
                } else {
                    loadingText.textContent = `Đang quét theo sitemap (tối đa ${maxPages} trang)...`;
                }
            } else {
                loadingText.textContent = `Đang crawl website (tối đa ${maxPages} trang)...`;
            }
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                loadingDiv.style.display = 'none';
                displayResults(data);
                showBackButton();
            })
            .catch(error => {
                loadingDiv.style.display = 'none';
                
                // Complete progress bar on error
                const progressBar = document.getElementById('progress-bar');
                const progressText = document.getElementById('progress-text');
                progressBar.style.width = '100%';
                progressText.textContent = '100% - Lỗi!';
                
                resultsDiv.innerHTML = `
                    <div class="alert alert-error">
                        ❌ <strong>Lỗi kết nối:</strong> ${error.message}<br>
                        <small>Thử với URL khác hoặc click "🔧 Debug" để kiểm tra cấu hình.</small>
                    </div>
                `;
                showBackButton();
            });
        });
        
        function displayResults(data) {
            const resultsDiv = document.getElementById('results');
            
            // Complete progress bar
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');
            progressBar.style.width = '100%';
            progressText.textContent = '100% - Hoàn thành!';
            
            if (data.error) {
                resultsDiv.innerHTML = `
                    <div class="alert alert-error">
                        ❌ <strong>Lỗi:</strong> ${data.error}<br>
                        ${data.details ? '<small>' + data.details + '</small>' : ''}
                    </div>
                `;
                return;
            }
            
            const mixedCount = data.mixed_content_count;
            const statusClass = mixedCount > 0 ? 'alert-warning' : 'alert-success';
            const statusIcon = mixedCount > 0 ? '⚠️' : '✅';
            
            // Method info
            let methodText = '';
            if (data.scan_method === 'single') {
                methodText = 'Quét một trang';
            } else if (data.scan_method === 'sitemap') {
                if (data.is_unlimited) {
                    methodText = `Quét toàn bộ sitemap (${data.total_found ? data.total_found + ' URLs tìm thấy' : 'Không giới hạn'})`;
                } else {
                    methodText = `Quét theo sitemap${data.total_found ? ` (${data.total_found} URLs tìm thấy)` : ''}`;
                }
            } else {
                methodText = 'Crawl từ homepage';
            }
            
            let html = `
                <h2>📊 Kết quả: ${data.base_url}</h2>
                
                <div class="${statusClass}">
                    <h3 style="margin: 0 0 15px 0;">${statusIcon} Tổng quan</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 15px;">
                        <div><strong>🔍 Phương thức:</strong><br><span>${methodText}</span></div>
                        <div><strong>📄 Trang quét:</strong><br><span>${data.total_urls} trang</span></div>
                        <div><strong>🚨 Mixed content:</strong><br><span>${mixedCount} vấn đề</span></div>
                        <div><strong>📊 Trang có vấn đề:</strong><br><span>${data.pages_with_issues} trang</span></div>
                        ${data.errors ? `<div><strong>❌ Lỗi:</strong><br>${data.errors} trang</div>` : ''}
                    </div>
                </div>
            `;
            
            if (mixedCount > 0) {
                html += `
                    <div class="alert alert-warning">
                        <h4>🔧 Cách khắc phục:</h4>
                        <ol style="margin: 10px 0; padding-left: 25px;">
                            <li>Thay đổi tất cả URL HTTP thành HTTPS</li>
                            <li>Cập nhật cấu hình CDN sang HTTPS</li>
                            <li>Kiểm tra hardcoded URLs trong code/database</li>
                            <li>Test lại sau khi sửa để đảm bảo không còn mixed content</li>
                        </ol>
                    </div>
                `;
                
                // Show detailed results
                html += '<h3>📋 Chi tiết các trang có vấn đề:</h3>';
                
                data.results.forEach((page, pageIndex) => {
                    if (page.mixed_content && page.mixed_content.length > 0) {
                        html += '<div class="result-item">';
                        html += `<div style="font-weight: 600; color: #2c3e50; margin-bottom: 15px; padding-bottom: 10px; border-bottom: 2px solid #eee;">
                                    <span style="background: #f8f9fa; padding: 4px 8px; border-radius: 4px; font-size: 12px; ">
                                        #${pageIndex + 1}
                                    </span>
                                    📄 ${page.url}
                                    <span style="font-size: 14px; color: #dc3545; font-weight: normal;">
                                        (${page.mixed_content.length} vấn đề)
                                    </span>
                                 </div>`;
                        
                        page.mixed_content.forEach((item, index) => {
                            html += `
                                <div class="mixed-content-item">
                                    <div style="margin-bottom: 10px;">
                                        <span class="content-type">${item.type}</span>
                                        <span style="font-size: 14px; color: #6c757d;">#${index + 1}</span>
                                    </div>
                                    <div><strong>🔗 HTTP URL:</strong></div>
                                    <div style="background: #fff;padding: 10px;border-radius: 4px;font-family: monospace;font-size: 14px;word-break: break-all;margin: 5px 0;border: 1px solid #ddd;max-width: 100%;overflow-x: auto;white-space: nowrap;display: block;scrollbar-width: thin;scrollbar-color: #888 #f1f1f1;">
                                        ${item.url}
                                    </div>
                                    <div style="font-size: 12px; color: #6c757d; margin-top: 8px;">
                                        <strong>Tag:</strong> ${item.tag}
                                    </div>
                                </div>
                            `;
                        });
                        
                        html += '</div>';
                    }
                });
            } else {
                html += `
                    <div class="alert alert-success">
                        <h4>🎉 Tuyệt vời!</h4>
                        <p>Không tìm thấy mixed content nào${data.total_urls > 1 ? ` trên ${data.total_urls} trang đã quét` : ' trên trang này'}. 
                           Website của bạn đã được cấu hình HTTPS đúng cách.</p>
                    </div>
                `;
            }
            
            // Show summary for pages with errors
            if (data.errors && data.errors > 0) {
                html += `
                    <div class="alert alert-warning">
                        <p>Có ${data.errors} trang không thể tải được. Điều này có thể do:</p>
                        <ul style="margin: 10px 0; padding-left: 25px;">
                            <li>Trang bị lỗi 404, 500 hoặc các lỗi server khác</li>
                            <li>Timeout do trang tải chậm</li>
                            <li>Blocking/firewall từ phía website</li>
                        </ul>
                    </div>
                `;
            }
            
            resultsDiv.innerHTML = html;
        }
        
        function showBackButton() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML += `
                <div style="text-align: center;">
                    <button onclick="resetForm()" class="btn">🔄 Quét website khác</button>
                </div>
            `;
        }
        
        function resetForm() {
            document.getElementById('scanForm').style.display = 'block';
            document.getElementById('loading').style.display = 'none';
            document.getElementById('results').innerHTML = '';
            
            // Reset progress bar
            document.getElementById('progress-bar').style.width = '0%';
            document.getElementById('progress-text').textContent = 'Đang chuẩn bị...';
        }
        
        // Initialize
        updateMethodInfo();
    </script>

<script>
// JavaScript cho FAQ Accordion
function toggleFAQ(element) {
    const faqItem = element.parentElement;
    const answer = faqItem.querySelector('.faq-answer');
    const isOpen = answer.classList.contains('open');
    
    // Đóng tất cả FAQ khác (nếu muốn chỉ mở 1 cái)
    // document.querySelectorAll('.faq-answer.open').forEach(item => {
    //     if (item !== answer) {
    //         item.classList.remove('open');
    //         item.previousElementSibling.classList.remove('active');
    //     }
    // });
    
    // Toggle FAQ hiện tại
    if (isOpen) {
        answer.classList.remove('open');
        element.classList.remove('active');
    } else {
        answer.classList.add('open');
        element.classList.add('active');
    }
}

// Keyboard support
document.addEventListener('keydown', function(e) {
    if (e.target.classList.contains('faq-question')) {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            toggleFAQ(e.target);
        }
    }
});

// Close FAQ when clicking outside
document.addEventListener('click', function(e) {
    if (!e.target.closest('.faq-item')) {
        // Uncomment để đóng tất cả FAQ khi click bên ngoài
        // document.querySelectorAll('.faq-answer.open').forEach(answer => {
        //     answer.classList.remove('open');
        //     answer.previousElementSibling.classList.remove('active');
        // });
    }
});

</script>
</body>
</html>