<?php
/**
 * <PERSON><PERSON> - Clean WordPress Functions
 * Tối ưu hóa hiệu suất và làm sạch WordPress core
 *
 * @package WordPress
 * @subpackage VTS_Theme
 */

/**
 * <PERSON><PERSON> hiệu hóa kiểm tra cập nhật tự động khi vào admin panel
 * Cải thiện hiệu suất đăng nhập khi kết nối internet chậm
 * Các kiểm tra vẫn diễn ra qua cron hoặc khi vào trang Updates
 */
function vts_disable_auto_updates() {
    if (is_admin()) {
        // Disable update checks when entering admin panel
        remove_action('admin_init', '_maybe_update_core');
        remove_action('admin_init', '_maybe_update_plugins');
        remove_action('admin_init', '_maybe_update_themes');

        // Disable update checks when entering specific admin pages
        remove_action('load-plugins.php', 'wp_update_plugins');
        remove_action('load-themes.php', 'wp_update_themes');
        remove_action('load-update.php', 'wp_update_plugins');
        remove_action('load-update.php', 'wp_update_themes');

        // Don't touch the cron events
        remove_action('wp_version_check', 'wp_version_check');
        remove_action('wp_update_plugins', 'wp_update_plugins');
        remove_action('wp_update_themes', 'wp_update_themes');

        // Disable browser update check in console
        add_filter('pre_site_transient_browser_' . md5($_SERVER['HTTP_USER_AGENT']), '__return_empty_array');
    }
}
add_action('init', 'vts_disable_auto_updates');

/**
 * Loại bỏ fonts không cần thiết 
 */
function vts_disable_inter_and_cardo_fonts($theme_json) {
    $theme_data = $theme_json->get_data();
    $font_data  = $theme_data['settings']['typography']['fontFamilies']['theme'] ?? array();

    // Fonts to be removed
    $fonts_to_remove = ['Inter', 'Cardo'];

    // Check and remove each font in $fonts_to_remove
    foreach ($font_data as $font_key => $font) {
        if (isset($font['name']) && in_array($font['name'], $fonts_to_remove, true)) {
            // Remove the font
            unset($font_data[$font_key]); 
        }
    }

    // Update theme JSON data if any fonts were removed
    if (count($font_data) !== count($theme_data['settings']['typography']['fontFamilies']['theme'] ?? [])) {
        $theme_json->update_with(array(
            'version'  => 1,
            'settings' => array(
                'typography' => array(
                    'fontFamilies' => array(
                        'theme' => $font_data,
                    ),
                ),
            ),
        ));
    }

    return $theme_json;
}
add_filter('wp_theme_json_data_theme', 'vts_disable_inter_and_cardo_fonts', 9999);

/**
 * Tối ưu hóa hiệu suất bằng cách vô hiệu hóa các tính năng không cần thiết
 */
function vts_performance_optimizations() {
    // Disable load text domain to save resources
    add_filter('pre_load_textdomain', '__return_false', -42);
    
    // Disable text domain error messages
    add_filter('doing_it_wrong_trigger_error', function($status, $function_name) {
        if ('_load_textdomain_just_in_time' === $function_name) {
            return false;
        }
        return $status;
    }, 10, 2);
    
    // Remove DNS prefetch
    remove_action('wp_head', 'wp_resource_hints', 2);
    
    // Disable admin email verification
    add_filter('admin_email_check_interval', '__return_false');
    
    // Remove SVG in body
    remove_action('wp_body_open', 'wp_global_styles_render_svg_filters');
    remove_action('wp_enqueue_scripts', 'wp_enqueue_global_styles');
    
    // Disable sitemap
    add_filter('wp_sitemaps_enabled', '__return_false');
    
    // Disable widgets block editor
    add_filter('gutenberg_use_widgets_block_editor', '__return_false');
    add_filter('use_widgets_block_editor', '__return_false');
    
    // Disable Gutenberg
    add_filter('use_block_editor_for_post', '__return_false', 10);
    
    // Disable language dropdown in login page
    add_filter('login_display_language_dropdown', '__return_false');
}
add_action('init', 'vts_performance_optimizations');

/**
 * Vô hiệu hóa hoàn toàn tính năng bình luận
 */
function vts_disable_comments_completely() {
    // Remove support for comments in post types
    add_action('init', function() {
        remove_post_type_support('post', 'comments');
        
        // For all post types
        foreach (get_post_types() as $post_type) {
            if (post_type_supports($post_type, 'comments')) {
                remove_post_type_support($post_type, 'comments');
                remove_post_type_support($post_type, 'trackbacks');
            }
        }
    });
    
    // Hide comments menu in admin
    add_action('wp_before_admin_bar_render', function() {
        global $wp_admin_bar;
        $wp_admin_bar->remove_menu('comments');
    });
    
    // Close comments on frontend
    add_filter('comments_open', '__return_false', 20, 2);
    add_filter('pings_open', '__return_false', 20, 2);
    
    // Redirect comment feeds
    add_action('template_redirect', function() {
        if ((is_comment_feed() && get_post_type() === 'post') || (is_comment_feed() && get_post_type() === 'product')) {
            header('Content-Type: text/html; charset=utf-8');
            wp_redirect(home_url('/feed'));
            exit();
        }
    });
    
    // Remove comment-related dashboard widgets
    add_action('admin_init', function() {
        remove_meta_box('dashboard_recent_comments', 'dashboard', 'normal');
    });
}
add_action('after_setup_theme', 'vts_disable_comments_completely');

/**
 * Làm sạch HTML trong menu
 */
function vts_clean_menu_classes($var) {
    return is_array($var) ? array_intersect($var, array('current-menu-item')) : '';
}
add_filter('nav_menu_item_id', 'vts_clean_menu_classes', 100, 1);
add_filter('page_css_class', 'vts_clean_menu_classes', 100, 1);

/**
 * Loại bỏ query strings từ static resources
 */
function vts_remove_query_strings($src) {
    $parts = explode('?', $src);
    if (strpos($src, '?ver=')) {
        // Giữ lại version parameter cho caching hiệu quả
        $version = strpos($src, '?ver=') ? substr(strrchr($src, '?ver='), 5) : '';
        return $parts[0] . '?ver=' . $version;
    }
    return $parts[0];
}
add_filter('script_loader_src', 'vts_remove_query_strings', 999);
add_filter('style_loader_src', 'vts_remove_query_strings', 999);

/**
 * Chỉ cho phép feed chính
 */
function vts_only_main_feed() {
    if (is_feed() && '/feed/' !== trailingslashit($_SERVER['REQUEST_URI'])) {
        wp_redirect(home_url() . '/feed');
        exit();
    }
}
add_action('template_redirect', 'vts_only_main_feed');

/**
 * Loại bỏ comment từ Freesoul Deactivate Plugins
 */
function vts_remove_eos_dp_comment() {
    remove_action('wp_footer', 'eos_dp_comment', 10);
}
add_action('init', 'vts_remove_eos_dp_comment');

/**
 * Loại bỏ phiên bản WordPress từ RSS feeds
 */
function vts_remove_wp_version_from_rss() {
    return '';
}
add_filter('the_generator', 'vts_remove_wp_version_from_rss');

/**
 * Loại bỏ Contact Form 7 auto p tags
 */
add_filter('wpcf7_autop_or_not', '__return_false');

/**
 * Loại bỏ noscript của WooCommerce gallery
 */
function vts_remove_wc_noscript() {
    remove_action('wp_head', 'wc_gallery_noscript');
}
add_action('init', 'vts_remove_wc_noscript');

/**
 * Loại bỏ chân trang admin
 */
function vts_remove_footer_admin() {
    echo '<span id="footer-thankyou"></span>';
}
add_filter('admin_footer_text', 'vts_remove_footer_admin');

/**
 * Ẩn menu WordPress Dashboard không sử dụng
 */
function vts_hide_admin_menus() {
    // Remove Comments menu
    remove_menu_page('edit-comments.php');
    
    // Có thể bổ sung thêm các menu khác cần ẩn
    // remove_menu_page('plugins.php');
    // remove_menu_page('tools.php');
}
add_action('admin_head', 'vts_hide_admin_menus');

/**
 * Hủy đăng ký các widget WordPress mặc định không sử dụng
 */
function vts_unregister_default_widgets() {
    // WordPress default widgets
    $wp_widgets = [
        'WP_Widget_Pages', 'WP_Widget_Calendar', 'WP_Widget_Archives',
        'WP_Widget_Links', 'WP_Widget_Meta', 'WP_Widget_Search',
        'WP_Widget_Text', 'WP_Widget_Categories', 'WP_Widget_Recent_Posts',
        'WP_Widget_Recent_Comments', 'WP_Widget_RSS', 'WP_Widget_Tag_Cloud',
        'WP_Widget_Media_Video', 'WP_Widget_Media_Audio', 'WP_Widget_Media_Gallery',
        'Block'
    ];
    
    foreach ($wp_widgets as $widget) {
        unregister_widget($widget);
    }
    
    // WooCommerce widgets
    $woo_widgets = [
        'WC_Widget_Featured_Products', 'WC_Widget_Product_Tag_Cloud',
        'WC_Widget_Cart', 'WC_Widget_Layered_Nav', 'WC_Widget_Layered_Nav_Filters',
        'WC_Widget_Rating_Filter', 'WC_Widget_Price_Filter', 'WC_Widget_Product_Search',
        'WC_Widget_Top_Rated_Products', 'WC_Widget_Recent_Reviews',
        'WC_Widget_Recently_Viewed', 'WC_Widget_Best_Sellers',
        'WC_Widget_Onsale', 'WC_Widget_Random_Products'
    ];
    
    foreach ($woo_widgets as $widget) {
        unregister_widget($widget);
    }
}
add_action('widgets_init', 'vts_unregister_default_widgets', 15);

/**
 * Loại bỏ styles Gutenberg và WP Block không cần thiết
 */
function vts_remove_block_styles() {
    wp_dequeue_style('wp-block-library');
    wp_deregister_style('wc-block-style');
}
add_action('wp_print_styles', 'vts_remove_block_styles', 100);

/**
 * Loại bỏ logo WordPress khỏi admin bar
 */
function vts_remove_wp_logo($wp_admin_bar) {
    $wp_admin_bar->remove_node('wp-logo');
}
add_action('admin_bar_menu', 'vts_remove_wp_logo', 999);

/**
 * Loại bỏ image sizes không sử dụng
 */
function vts_remove_unused_image_sizes() {
    remove_image_size('medium_large');
    remove_image_size('large');
    remove_image_size('woocommerce_single');
    remove_image_size('shop_single');
    remove_image_size('shop_catalog');
}
add_action('init', 'vts_remove_unused_image_sizes', 9999);

/**
 * Làm sạch phân trang - loại bỏ heading và role
 */
function vts_clean_pagination($content) {
    $content = str_replace('role="navigation"', '', $content);
    $content = preg_replace('#<h2.*?>(.*?)<\ /h2>#si', '', $content);
    return $content;
}
add_action('navigation_markup_template', 'vts_clean_pagination');

/**
 * Loại bỏ widgets không cần thiết từ dashboard
 */
function vts_remove_dashboard_widgets() {
    global $wp_meta_boxes;
    
    $widgets_to_remove = [
        'dashboard_quick_press',
        'dashboard_incoming_links',
        'dashboard_plugins',
        'dashboard_recent_drafts',
        'dashboard_recent_comments',
        'dashboard_primary'
    ];
    
    foreach ($widgets_to_remove as $widget) {
        // Xóa từ các vị trí có thể có
        unset($wp_meta_boxes['dashboard']['normal']['core'][$widget]);
        unset($wp_meta_boxes['dashboard']['side']['core'][$widget]);
    }
}
add_action('wp_dashboard_setup', 'vts_remove_dashboard_widgets', 11);

/**
 * Làm sạch hoàn toàn header WordPress
 */
function vts_clean_wp_header() {
    // REST API
    remove_action('wp_head', 'rest_output_link_wp_head', 10);
    remove_action('wp_head', 'wp_oembed_add_discovery_links', 10);
    
    // Embed
    remove_action('rest_api_init', 'wp_oembed_register_route');
    add_filter('embed_oembed_discover', '__return_false');
    remove_filter('oembed_dataparse', 'wp_filter_oembed_result', 10);
    remove_action('wp_head', 'wp_oembed_add_discovery_links');
    remove_action('wp_head', 'wp_oembed_add_host_js');
    
    // Emoji
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
    
    // EditURI/RSD, WLW Manifest, WP Version
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'wp_generator');
    
    // Feed links
    remove_action('wp_head', 'feed_links_extra', 3);
    remove_action('wp_head', 'feed_links', 2);
}
add_action('init', 'vts_clean_wp_header');