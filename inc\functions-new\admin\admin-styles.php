<?php
/**
 * VTS Admin Styles
 * 
 * Custom styles for WordPress admin
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Admin styles and scripts
 */
function vts_admin_script() {
    if (is_admin()) {
        wp_enqueue_style('vts-admin-style', get_template_directory_uri() . '/css/vts-admin-style.css');
        
        // CSS for admin interface
        echo '<style type="text/css">
        #wp-sp-welcome-box, #wpr-deactivation-modal, .wpr-Modal, #toplevel_page_wpintense{display:none!important}
        #toplevel_page_eos_dp_menu, #toplevel_page_wpintense{display:none!important}
        #toplevel_page_image-regenerate-select-crop-settings,
        #toplevel_page_admin-page-wc-settings-tab-checkout-from-PAYMENTS_MENU_ITEM,
        #toplevel_page_woocommerce-marketing,
        #toplevel_page_admin-page-wc-admin-task-payments, a[href="site-editor.php?path=/patterns"]{display: none!important;}
        </style>';
        
        // JavaScript to inject CSS into TinyMCE when it loads
        echo '<script type="text/javascript">
        jQuery(document).ready(function($) {
            // Wait for TinyMCE to initialize
            $(document).on("tinymce-editor-init", function(event, editor) {
                if (editor.id === "content") {
                    editor.dom.addStyle("body, p, div, span, h1, h2, h3, h4, h5, h6 { font-family: \'Montserrat\', serif !important; }");
                }
            });
            
            // Fallback: check TinyMCE every 500ms
            var checkTinyMCE = setInterval(function() {
                if (typeof tinymce !== "undefined" && tinymce.get("content")) {
                    var editor = tinymce.get("content");
                    if (editor && editor.dom) {
                        editor.dom.addStyle("body, p, div, span, h1, h2, h3, h4, h5, h6 { font-family: \'Montserrat\', serif !important; }");
                        clearInterval(checkTinyMCE);
                    }
                }
            }, 500);
        });
        </script>';
    }   
}
add_action('admin_head', 'vts_admin_script');
