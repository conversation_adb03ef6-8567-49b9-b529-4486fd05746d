<?php
/**
 * VTS Admin Dashboard Customizations
 * 
 * Customize WordPress admin dashboard
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Hide unused admin menus
 */
function vts_hide_admin_menus() {
    // Remove Comments menu
    remove_menu_page('edit-comments.php');
    
    // Can add more menus to hide as needed
    // remove_menu_page('plugins.php');
    // remove_menu_page('tools.php');
}
add_action('admin_head', 'vts_hide_admin_menus');

/**
 * Remove dashboard widgets
 */
function vts_remove_dashboard_widgets() {
    global $wp_meta_boxes;
    
    $widgets_to_remove = [
        'dashboard_quick_press',
        'dashboard_incoming_links',
        'dashboard_plugins',
        'dashboard_recent_drafts',
        'dashboard_recent_comments',
        'dashboard_primary'
    ];
    
    foreach ($widgets_to_remove as $widget) {
        // Remove from possible positions
        unset($wp_meta_boxes['dashboard']['normal']['core'][$widget]);
        unset($wp_meta_boxes['dashboard']['side']['core'][$widget]);
    }
}
add_action('wp_dashboard_setup', 'vts_remove_dashboard_widgets', 11);

/**
 * Remove admin footer
 */
function vts_remove_footer_admin() {
    echo '<span id="footer-thankyou"></span>';
}
add_filter('admin_footer_text', 'vts_remove_footer_admin');

/**
 * Remove WordPress logo from admin bar
 */
function vts_remove_wp_logo($wp_admin_bar) {
    $wp_admin_bar->remove_node('wp-logo');
}
add_action('admin_bar_menu', 'vts_remove_wp_logo', 999);

/**
 * Disable installation of new themes/plugins
 */
function vts_disable_installation() {
    // Hide menu plugin and theme install
    remove_menu_page('plugin-install.php');
    remove_menu_page('theme-install.php');
    
    // Block all users from installing plugins and themes
    add_filter('user_has_cap', function($allcaps, $cap, $args) {
        if (isset($args[0]) && ('install_plugins' === $args[0] || 'install_themes' === $args[0])) {
            $allcaps[$cap[0]] = false;
        }
        return $allcaps;
    }, 10, 3);

    // Redirect from install pages if someone tries to access directly
    global $pagenow;
    if ($pagenow == 'plugin-install.php' || $pagenow == 'theme-install.php') {
        wp_die(__('You do not have sufficient permissions to access this page.'));
    }
}
add_action('admin_init', 'vts_disable_installation');

/**
 * Optimize admin scripts
 */
function vts_optimize_admin_scripts() {
    if (!is_admin()) {
        return;
    }

    // Array of scripts to dequeue/deregister
    $scripts_to_remove = [
        'seopress-pro-ga',
        'seopress-pro-ga-embed',
        'wc-reports',
        'seopress-pro-matomo'
    ];

    // Array of styles to dequeue/deregister if needed
    $styles_to_remove = [
        // 'style-handle'  // Uncomment if needed
    ];

    // Remove scripts
    foreach ($scripts_to_remove as $script) {
        wp_dequeue_script($script);
        wp_deregister_script($script);
    }

    // Remove styles
    foreach ($styles_to_remove as $style) {
        wp_dequeue_style($style);
        wp_deregister_style($style);
    }
}
add_action('admin_enqueue_scripts', 'vts_optimize_admin_scripts', 100);

/**
 * Remove WP Rocket clear cache message
 */
function vts_remove_rocket_warning() {
    remove_action('admin_notices', 'rocket_warning_plugin_modification');
}
add_action('admin_notices', 'vts_remove_rocket_warning', 1);
