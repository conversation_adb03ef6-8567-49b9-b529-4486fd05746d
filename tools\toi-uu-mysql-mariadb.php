<?php
/*
 * Template Name: Mariadb - MYSQL
 */
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MySQL/MariaDB Calculator - Tối ưu hóa cấu hình database server</title>
    <meta property="og:url" content="https://vutruso.com/cong-cu-tinh-toan-mysql-mariadb/">
    <link rel="canonical" href="https://vutruso.com/cong-cu-tinh-toan-mysql-mariadb/">
    <meta name="description" content="Công cụ tối ưu hóa cấu hình MySQL/MariaDB cho server của bạn. Tính toán innodb_buffer_pool_size, max_connections, query_cache_size và các thông số quan trọng khác">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:site_name" content="MySQL/MariaDB Calculator">
    <meta property="og:locale" content="vi_VN">
    <meta property="og:type" content="website">
    <meta property="og:title" content="MySQL/MariaDB Calculator - Tối ưu hóa cấu hình database server">
    <meta property="og:description" content="Công cụ tối ưu hóa cấu hình MySQL/MariaDB cho server của bạn. Tính toán innodb_buffer_pool_size, max_connections, query_cache_size và các thông số quan trọng khác">
    <meta property="og:image" content="https://vutruso.com/wp-content/uploads/2025/07/mysql-mariadb-calculator.png">
    <meta property="og:image:secure_url" content="https://vutruso.com/wp-content/uploads/2025/07/mysql-mariadb-calculator.png">
    <meta property="og:image:width" content="405">
    <meta property="og:image:height" content="270">
    <meta property="og:image:alt" content="MySQL/MariaDB Calculator - Vutruso">
    <meta property="fb:admins" content="100002603951764">
    <meta property="fb:app_id" content="***************">
    
    <!-- Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@vutruso">
    <meta name="twitter:title" content="MySQL/MariaDB Calculator - Tối ưu hóa cấu hình database server">
    <meta name="twitter:description" content="Công cụ tối ưu hóa cấu hình MySQL/MariaDB cho server của bạn. Tính toán innodb_buffer_pool_size, max_connections, query_cache_size và các thông số quan trọng khác">
    <meta name="twitter:image" content="https://vutruso.com/wp-content/uploads/2025/07/mysql-mariadb-calculator.png">
    <!-- Structured Data JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "MySQL/MariaDB Calculator",
        "url": "https://vutruso.com/cong-cu-tinh-toan-mysql-mariadb/",
        "description": "Công cụ tối ưu hóa cấu hình MySQL/MariaDB cho server của bạn. Tính toán innodb_buffer_pool_size, max_connections, query_cache_size và các thông số quan trọng khác",
        "applicationCategory": "DeveloperApplication",
        "operatingSystem": "Any",
        "browserRequirements": "Requires JavaScript. Requires HTML5.",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "VND"
        },
        "featureList": [
            "Tính toán InnoDB Buffer Pool Size tối ưu",
            "Cấu hình Max Connections phù hợp",
            "Tối ưu Query Cache và Key Buffer",
            "Hỗ trợ cả MySQL và MariaDB",
            "Tạo file my.cnf hoàn chỉnh",
            "Phân tích workload database",
            "Hoàn toàn miễn phí"
        ],
        "screenshot": "https://vutruso.com/wp-content/uploads/2025/07/mysql-mariadb-calculator.png",
        "creator": {
            "@type": "Organization",
            "name": "Vũ Trụ Số",
            "url": "https://vutruso.com",
            "alternateName": "Vũ Trụ Số",
            "description": "Vũ Trụ Số chuyên cung cấp các giải pháp công nghệ, thiết kế website, quản trị website cho doanh nghiệp, cung cấp máy chủ ảo VPS/Hosting, dịch vụ chạy quảng cáo Google, quảng cáo Facebook, quảng cáo Zalo, các dịch vụ bảo mật website WordPress, tăng tốc website WordPress và SEO mới từ khóa lên top Google",
            "logo": "https://vutruso.com/wp-content/uploads/2020/07/vutruso_login.jpg",
            "sameAs": [
                "https://www.facebook.com/vutruso",
                "https://twitter.com/@vutruso",
                "https://www.pinterest.com/vutruso/",
                "https://www.instagram.com/vutruso",
                "https://www.youtube.com/channel/UCOEXvu-rxMQ6Tvqm1kZJUrg/about",
                "https://www.linkedin.com/in/vutruso",
                "https://g.page/vutruso",
                "https://vutruso.business.site/",
                "https://sites.google.com/view/vutruweb",
                "https://vutruso.tumblr.com/",
                "https://ok.ru/profile/589668477610"
            ],
            "vatID": "0317358676",
            "contactPoint": [
                {
                    "@type": "ContactPoint",
                    "telephone": "+***********",
                    "email": "<EMAIL>",
                    "contactOption": "TollFree",
                    "contactType": "customer support"
                }
            ]
        }
    }
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 50%, #4facfe 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            backdrop-filter: blur(15px);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .header h1 {
            font-size: 2.8em;
            margin-bottom: 15px;
            font-weight: 300;
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .mysql-calculator {
            padding: 40px;
        }

        .controls-section {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
            padding: 35px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
        }

        .controls-section h3 {
            color: #333;
            margin-bottom: 25px;
            font-size: 1.5em;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .memory-input {
            background: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .memory-input:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: #667eea;
        }

        .memory-input:last-child {
            margin-bottom: 0;
        }

        .memory-input label {
            display: block;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .input-group {
            display: grid;
            grid-template-columns: 1fr auto auto;
            gap: 15px;
            align-items: center;
        }

        .input-group input[type="range"] {
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: linear-gradient(to right, #667eea, #764ba2);
            outline: none;
            -webkit-appearance: none;
            cursor: pointer;
        }

        .input-group input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: white;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            border: 3px solid #667eea;
            transition: all 0.3s ease;
        }

        .input-group input[type="range"]::-webkit-slider-thumb:hover {
            transform: scale(1.2);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
        }

        .input-group input[type="range"]::-moz-range-thumb {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: white;
            cursor: pointer;
            border: 3px solid #667eea;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .input-group input[type="number"] {
            width: 80px;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            text-align: center;
            font-weight: bold;
            font-size: 16px;
            background: #f8f9ff;
            transition: all 0.3s ease;
        }

        .input-group input[type="number"]:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .memory-label {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: 600;
            min-width: 80px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .results-section {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
        }

        .results-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            text-align: center;
        }

        .results-header h3 {
            font-size: 1.4em;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .memory-allocation {
            padding: 30px;
        }

        .allocation-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 0;
        }

        .allocation-table th,
        .allocation-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .allocation-table th {
            font-weight: 600;
            color: #555;
            width: 60%;
        }

        .allocation-table td {
            font-weight: bold;
            color: #333;
            text-align: right;
        }

        .allocation-table tr:last-child th,
        .allocation-table tr:last-child td {
            border-bottom: none;
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
            font-size: 1.1em;
        }

        .allocation-table tr:hover {
            background: #f8f9ff;
        }

        .config-section {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
        }

        .config-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 25px;
            text-align: center;
        }

        .config-header h3 {
            font-size: 1.4em;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .config-content {
            padding: 30px;
        }

        .config-display {
            background: #f8f9ff;
            border: 2px solid #e1e8ed;
            border-radius: 15px;
            padding: 25px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.8;
            color: #333;
            white-space: pre-line;
            margin-bottom: 20px;
            overflow-x: auto;
        }

        .note {
            background: #fff3cd;
            border: 1px solid #ffecb5;
            border-radius: 12px;
            padding: 15px;
            color: #856404;
            font-style: italic;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .copy-button {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 35px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 0 auto;
        }

        .copy-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
        }

        .copy-button:active {
            transform: translateY(0);
        }

        @media (max-width: 768px) {
            .input-group {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            
            .input-group input[type="number"] {
                width: 100%;
            }
            
            .memory-label {
                text-align: center;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .mysql-calculator {
                padding: 25px;
            }
            
            .allocation-table th,
            .allocation-table td {
                padding: 12px 8px;
                font-size: 14px;
            }
            .header {
                padding: 10px;    
                font-size: 12px;
            }

            .controls-section {
                padding: 0;
            }
            
            .memory-allocation {
                padding: 15px;
            }

            .config-content {
                padding: 15px;
            }

            .config-display {
                padding: 5px;

            }
            .mysql-calculator {
                padding: 10px;
            }

        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 15px 25px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .notification.show {
            transform: translateX(0);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ MySQL/MariaDB Calculator</h1>
            <p>Tối ưu hóa cấu hình MySQL/MariaDB cho server của bạn</p>
        </div>

        <div class="mysql-calculator">
            <div class="controls-section">
                <h3>⚙️ Cài đặt bộ nhớ</h3>
                
                <div class="memory-input">
                    <label for="totalMemory">🖥️ Tổng bộ nhớ Server (GB):</label>
                    <div class="input-group">
                        <input type="range" id="totalMemory" min="1" max="128" value="8">
                        <input type="number" id="totalMemoryInput" value="8" min="1" max="128">
                        <span id="totalMemoryLabel" class="memory-label">8 GB</span>
                    </div>
                </div>

                <div class="memory-input">
                    <label for="reservedMemory">🔒 Dành riêng cho OS (GB):</label>
                    <div class="input-group">
                        <input type="range" id="reservedMemory" min="1" max="32" value="2">
                        <input type="number" id="reservedMemoryInput" value="2" min="1" max="32">
                        <span id="reservedMemoryLabel" class="memory-label">2 GB</span>
                    </div>
                </div>

                <div class="memory-input">
                    <label for="otherTasksMemory">⚡ Các tác vụ khác (GB):</label>
                    <div class="input-group">
                        <input type="range" id="otherTasksMemory" min="0" max="64" value="2">
                        <input type="number" id="otherTasksMemoryInput" value="2" min="0" max="64">
                        <span id="otherTasksMemoryLabel" class="memory-label">2 GB</span>
                    </div>
                </div>
            </div>

            <div class="results-section">
                <div class="results-header">
                    <h3>📊 Phân bổ bộ nhớ</h3>
                </div>
                <div class="memory-allocation">
                    <table class="allocation-table">
                        <tbody>
                            <tr>
                                <th>Tổng bộ nhớ Server:</th>
                                <td id="totalMemoryResult">8.00 GB</td>
                            </tr>
                            <tr>
                                <th>Dành riêng cho OS:</th>
                                <td id="reservedMemoryResult">2.00 GB</td>
                            </tr>
                            <tr>
                                <th>Các tác vụ khác:</th>
                                <td id="otherTasksMemoryResult">2.00 GB</td>
                            </tr>
                            <tr>
                                <th>Khả dụng cho MySQL/MariaDB:</th>
                                <td id="availableMemoryResult">4.00 GB</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="config-section">
                <div class="config-header">
                    <h3>🛠️ Cài đặt được đề xuất</h3>
                </div>
                <div class="config-content">
                    <div id="recommendedSettings" class="config-display"></div>
                    
                    <div class="note">
                        ⚠️ <strong>Lưu ý:</strong> Đây là các đề xuất chung, vui lòng điều chỉnh theo nhu cầu cụ thể của bạn
                    </div>
                    
                    <button id="copySettingsBtn" class="copy-button">
                        📋 Sao chép cấu hình
                    </button>
                </div>
            </div>

            <footer class="seo-footer" style="background: #f8f9fa; border-radius: 8px; padding: 20px; margin-top: 20px; text-align: center; border-top: 3px solid #4285f4;margin:10px">
              <div class="footer-content">
                <p style="margin-bottom: 10px; color: #666;"><strong>🔧 MySQL/MariaDB Calculator </strong> giúp tối ưu hóa cấu hình MySQL/MariaDB cho server của bạn</p>
                <p style="margin-bottom: 10px; color: #666;">Phát triển bởi <a style="color: #4285f4;" href="https://vutruso.com/">Vũ Trụ Số</a> để hỗ trợ cộng đồng Việt Nam.</p>
              </div>
            </footer>


        </div>
    </div>

    <div id="notification" class="notification">
        ✅ Đã sao chép cấu hình thành công!
    </div>

    <script>
   function _0x3d9e(_0xd55cd4,_0x55099e){const _0x31b3a8=_0x4209();return _0x3d9e=function(_0x489877,_0x19908c){_0x489877=_0x489877-(0x1f*0xc9+-0x12e*-0x1+0x383*-0x7);let _0xcda977=_0x31b3a8[_0x489877];return _0xcda977;},_0x3d9e(_0xd55cd4,_0x55099e);}const _0x6b1223=_0x3d9e;(function(_0x5dab61,_0x8a39f7){const _0x4b0347=_0x3d9e,_0x2cb47f=_0x5dab61();while(!![]){try{const _0x438584=-parseInt(_0x4b0347(0x116))/(0xdd4+-0xaf1+-0x3*0xf6)+parseInt(_0x4b0347(0xf5))/(-0x11c1+-0xb30+0x1cf3)+parseInt(_0x4b0347(0xf1))/(-0x834+-0x5bd+-0x6fa*-0x2)+parseInt(_0x4b0347(0x1aa))/(0x5*-0xb5+-0xf83+0x5*0x3d0)*(parseInt(_0x4b0347(0x157))/(0x1*-0xe68+-0x1808+0x2675))+parseInt(_0x4b0347(0x131))/(-0x14a1+0x257b+-0x1*0x10d4)+parseInt(_0x4b0347(0x14b))/(-0x1*0x16e5+0x712+0x7ed*0x2)+-parseInt(_0x4b0347(0x18f))/(0x261+-0x1d54+-0x1afb*-0x1);if(_0x438584===_0x8a39f7)break;else _0x2cb47f['push'](_0x2cb47f['shift']());}catch(_0x43d464){_0x2cb47f['push'](_0x2cb47f['shift']());}}}(_0x4209,0x48497*-0x2+0x167c57+-0xf*0x466),document[_0x6b1223(0x163)+_0x6b1223(0x118)](_0x6b1223(0x151)+_0x6b1223(0xfa),function(){const _0x1c7a88=_0x6b1223,_0xc03607={'zpOyX':function(_0x51e6b3,_0x5187ce){return _0x51e6b3+_0x5187ce;},'nbFQR':_0x1c7a88(0x13b),'QAzvV':_0x1c7a88(0x141),'BPHkF':function(_0xa59aff,_0x275f80){return _0xa59aff<_0x275f80;},'ULFYm':function(_0x360a46,_0x222900){return _0x360a46*_0x222900;},'lyKsY':function(_0x563e30,_0x5d0d9a){return _0x563e30(_0x5d0d9a);},'gUSaL':_0x1c7a88(0x111)+'y','pIcnF':function(_0x5e3759,_0x5aca85){return _0x5e3759(_0x5aca85);},'AojFv':_0x1c7a88(0x19a)+_0x1c7a88(0x1b6),'lREHn':function(_0x1938ad,_0x26f49a){return _0x1938ad(_0x26f49a);},'uNHjl':_0x1c7a88(0x16c)+_0x1c7a88(0x13e),'aqAnW':function(_0x471d23,_0x597000){return _0x471d23-_0x597000;},'eVeBH':_0x1c7a88(0x111)+_0x1c7a88(0x185),'XaKtr':_0x1c7a88(0x19a)+_0x1c7a88(0x154),'QJetv':function(_0x5bb551,_0x1ae5bd){return _0x5bb551(_0x1ae5bd);},'ETRmu':_0x1c7a88(0x16c)+_0x1c7a88(0x15b)+'lt','qjtRF':_0x1c7a88(0x15d)+_0x1c7a88(0x1b3)+'t','nNMlI':function(_0x67ce32,_0x23f864){return _0x67ce32(_0x23f864);},'FcyFf':function(_0x4d8743,_0x299ef4){return _0x4d8743*_0x299ef4;},'jjydu':function(_0x2ce28d,_0x59948a){return _0x2ce28d*_0x59948a;},'IMbjw':function(_0x52dfb4,_0x2fffc7){return _0x52dfb4*_0x2fffc7;},'ebwXX':function(_0x58453a,_0x4cdc2a){return _0x58453a(_0x4cdc2a);},'eOtuO':function(_0x4e0f46,_0x1c4700){return _0x4e0f46(_0x1c4700);},'iyePj':function(_0x1306f1,_0x3d5376){return _0x1306f1(_0x3d5376);},'EagaA':_0x1c7a88(0x1a1)+_0x1c7a88(0x13f),'BejAF':_0x1c7a88(0xfc),'bpPRD':_0x1c7a88(0x181)+'on','ltihr':function(_0xa50ca6,_0x2e0e86,_0x1efbaa){return _0xa50ca6(_0x2e0e86,_0x1efbaa);},'Rkfnp':function(_0x190201){return _0x190201();},'kyVKE':_0x1c7a88(0x174)+_0x1c7a88(0x17c)+'\x20','bibeO':function(_0x50da26,_0xccf8d){return _0x50da26(_0xccf8d);},'VsGdX':_0x1c7a88(0x195),'Edcmt':_0x1c7a88(0x120),'QuWgR':_0x1c7a88(0x160),'vnbRs':_0x1c7a88(0x172)+_0x1c7a88(0x1b8)+_0x1c7a88(0x10e)+_0x1c7a88(0x13c)+_0x1c7a88(0x18d),'bgfvm':_0x1c7a88(0x147)+_0x1c7a88(0x10b)+_0x1c7a88(0x189),'xGdRh':function(_0x525499,_0x42782f,_0x5c4e3a){return _0x525499(_0x42782f,_0x5c4e3a);},'RSIcS':_0x1c7a88(0x146),'CaDRf':function(_0x417d0c,_0x1ae2ad){return _0x417d0c(_0x1ae2ad);},'XIwqi':_0x1c7a88(0x1b0),'KDXIo':_0x1c7a88(0x12a),'QAhhg':function(_0x8e1c6c,_0x4cbc19){return _0x8e1c6c||_0x4cbc19;},'LhzMo':function(_0xf2cb44,_0x2852cb,_0x5e1938){return _0xf2cb44(_0x2852cb,_0x5e1938);},'oxIWw':function(_0x321b2c){return _0x321b2c();},'YnLYe':_0x1c7a88(0x138)+_0x1c7a88(0x13d),'IPoZM':_0x1c7a88(0x138)+_0x1c7a88(0x14f),'yaZFp':_0x1c7a88(0x19d)+_0x1c7a88(0xf8),'TKnsx':_0x1c7a88(0x114),'vwWVD':function(_0x2b9ce4){return _0x2b9ce4();}};function _0x378753(_0x4e72dd,_0x43f198){const _0x39d62d=_0x1c7a88,_0x1a2360=document[_0x39d62d(0x198)+_0x39d62d(0x158)](_0x4e72dd),_0x33e43c=document[_0x39d62d(0x198)+_0x39d62d(0x158)](_0xc03607[_0x39d62d(0x176)](_0x4e72dd,_0xc03607[_0x39d62d(0x135)])),_0x441911=document[_0x39d62d(0x198)+_0x39d62d(0x158)](_0xc03607[_0x39d62d(0x176)](_0x4e72dd,_0xc03607[_0x39d62d(0x1ac)]));if(_0x1a2360)_0x1a2360[_0x39d62d(0x17e)]=_0x43f198;if(_0x33e43c)_0x33e43c[_0x39d62d(0x17e)]=_0x43f198;if(_0x441911)_0x441911[_0x39d62d(0x10a)+'t']=_0x43f198+_0x39d62d(0x17b);}function _0x5d3886(_0x5837e8){const _0x583a0f=_0x1c7a88;if(_0xc03607[_0x583a0f(0x132)](_0x5837e8,-0x2414+-0x1a3d+0x3e52)){const _0x3b69de=_0xc03607[_0x583a0f(0x133)](_0x5837e8,-0x1f4+0x1219+-0x1*0xc25);return Math[_0x583a0f(0x109)](_0x3b69de)+_0x583a0f(0x15a);}else return _0x5837e8[_0x583a0f(0x15f)](0xd4f*-0x2+0xb21+0xf7f*0x1)+_0x583a0f(0x17b);}function _0x4a7e4a(){const _0x156f54=_0x1c7a88,_0x1b6d21=_0xc03607[_0x156f54(0x107)](parseFloat,document[_0x156f54(0x198)+_0x156f54(0x158)](_0xc03607[_0x156f54(0x12f)])[_0x156f54(0x17e)])||0x1d33+0x17*0x80+-0x28b3,_0x4d9ada=_0xc03607[_0x156f54(0x11d)](parseFloat,document[_0x156f54(0x198)+_0x156f54(0x158)](_0xc03607[_0x156f54(0x1b1)])[_0x156f54(0x17e)])||0x1809*0x1+-0xc14+0xbf5*-0x1,_0x399869=_0xc03607[_0x156f54(0x1a6)](parseFloat,document[_0x156f54(0x198)+_0x156f54(0x158)](_0xc03607[_0x156f54(0x15e)])[_0x156f54(0x17e)])||0x19e6+0x1ee+-0x1*0x1bd4,_0x2a4b91=Math[_0x156f54(0x12a)](-0x1*0x1591+-0xe7d+0xd*0x2c6,_0xc03607[_0x156f54(0x150)](_0xc03607[_0x156f54(0x150)](_0x1b6d21,_0x4d9ada),_0x399869));document[_0x156f54(0x198)+_0x156f54(0x158)](_0xc03607[_0x156f54(0x1a7)])[_0x156f54(0x10a)+'t']=_0xc03607[_0x156f54(0x1a6)](_0x5d3886,_0x1b6d21),document[_0x156f54(0x198)+_0x156f54(0x158)](_0xc03607[_0x156f54(0x182)])[_0x156f54(0x10a)+'t']=_0xc03607[_0x156f54(0x170)](_0x5d3886,_0x4d9ada),document[_0x156f54(0x198)+_0x156f54(0x158)](_0xc03607[_0x156f54(0x156)])[_0x156f54(0x10a)+'t']=_0xc03607[_0x156f54(0x170)](_0x5d3886,_0x399869),document[_0x156f54(0x198)+_0x156f54(0x158)](_0xc03607[_0x156f54(0x1a8)])[_0x156f54(0x10a)+'t']=_0xc03607[_0x156f54(0x152)](_0x5d3886,_0x2a4b91);const _0x2ca9fe={'innodbBufferPoolSize':_0xc03607[_0x156f54(0x133)](_0x2a4b91,0x1*-0x2039+-0xe13*-0x1+0x913*0x2+0.7),'maxConnections':Math[_0x156f54(0x109)](_0xc03607[_0x156f54(0x192)](_0x2a4b91,-0xba7*0x1+-0x1c6d+0xa*0x404)),'keyBufferSize':_0xc03607[_0x156f54(0x133)](_0x2a4b91,0xaf*0x1d+-0x1393*0x1+0x20*-0x2+0.1),'innodbLogFileSize':_0xc03607[_0x156f54(0x133)](_0x2a4b91,-0x1103+0xc*0xb2+0x8ab+0.2),'queryCacheSize':_0xc03607[_0x156f54(0x171)](_0x2a4b91,0x1b*-0x75+0x2568+-0xcf*0x1f+0.05),'tmpTableSize':_0xc03607[_0x156f54(0x102)](_0x2a4b91,-0x246a+0x551*-0x4+0x39ae+0.03),'innodbIoCapacity':Math[_0x156f54(0x109)](_0xc03607[_0x156f54(0x171)](_0x2a4b91,0x2*-0xf20+0x147e+0xa26))},_0x1a9c9a=_0x156f54(0x10c)+_0x156f54(0x125)+_0x156f54(0x129)+_0xc03607[_0x156f54(0x159)](_0x5d3886,_0x2ca9fe[_0x156f54(0x161)+_0x156f54(0x1a3)])+(_0x156f54(0x18b)+_0x156f54(0x1ab))+_0x2ca9fe[_0x156f54(0x143)+_0x156f54(0x19e)]+(_0x156f54(0x16e)+_0x156f54(0x184))+_0xc03607[_0x156f54(0x134)](_0x5d3886,_0x2ca9fe[_0x156f54(0xf0)+_0x156f54(0x12d)])+(_0x156f54(0x18a)+_0x156f54(0x130)+_0x156f54(0x168))+_0xc03607[_0x156f54(0x165)](_0x5d3886,_0x2ca9fe[_0x156f54(0x142)+_0x156f54(0x19f)])+(_0x156f54(0x101)+_0x156f54(0xfb))+_0xc03607[_0x156f54(0x134)](_0x5d3886,_0x2ca9fe[_0x156f54(0x14c)+_0x156f54(0x16f)])+(_0x156f54(0x12e)+_0x156f54(0x127))+_0xc03607[_0x156f54(0x11d)](_0x5d3886,_0x2ca9fe[_0x156f54(0x17d)+'ze'])+(_0x156f54(0x18a)+_0x156f54(0x11e)+_0x156f54(0x14a)+_0x156f54(0x12c)+_0x156f54(0x1b2)+_0x156f54(0x190)+_0x156f54(0x1b9)+_0x156f54(0x19b)+_0x156f54(0x15c)+_0x156f54(0x173)+_0x156f54(0x144)+_0x156f54(0x177)+_0x156f54(0x11b)+_0x156f54(0x191)+_0x156f54(0x11a))+_0x2ca9fe[_0x156f54(0xfd)+_0x156f54(0x1a4)]+(_0x156f54(0x140)+_0x156f54(0x105)+_0x156f54(0x1a9)+_0x156f54(0x103)+_0x156f54(0x137)+_0x156f54(0x10d)+_0x156f54(0x164)+_0x156f54(0x12b)+_0x156f54(0x166)+_0x156f54(0x153)+_0x156f54(0x167)+_0x156f54(0x19c)+_0x156f54(0x10f)+_0x156f54(0x187)+_0x156f54(0x112)+_0x156f54(0x10f)+_0x156f54(0x16d)+_0x156f54(0x1ae)+_0x156f54(0x16b)+_0x156f54(0x15a));document[_0x156f54(0x198)+_0x156f54(0x158)](_0xc03607[_0x156f54(0x175)])[_0x156f54(0x10a)+'t']=_0x1a9c9a;}function _0x1b1ff0(){const _0x845a60=_0x1c7a88,_0x27f344=document[_0x845a60(0x198)+_0x845a60(0x158)](_0xc03607[_0x845a60(0x1af)]);_0x27f344[_0x845a60(0x178)][_0x845a60(0xf2)](_0xc03607[_0x845a60(0x179)]),_0xc03607[_0x845a60(0x180)](setTimeout,()=>{const _0x226766=_0x845a60;_0x27f344[_0x226766(0x178)][_0x226766(0x115)](_0xc03607[_0x226766(0x179)]);},-0x1f21+-0x8*-0x3aa+-0x15*-0xa5);}function _0x24dbe9(){const _0xed1fbd=_0x1c7a88,_0x2f1603=document[_0xed1fbd(0x198)+_0xed1fbd(0x158)](_0xc03607[_0xed1fbd(0x175)])[_0xed1fbd(0x10a)+'t'];navigator[_0xed1fbd(0xfe)]&&navigator[_0xed1fbd(0xfe)][_0xed1fbd(0xff)]?navigator[_0xed1fbd(0xfe)][_0xed1fbd(0xff)](_0x2f1603)[_0xed1fbd(0x193)](()=>{const _0x329233=_0xed1fbd;_0xc03607[_0x329233(0x136)](_0x1b1ff0);})[_0xed1fbd(0x117)](_0x225b1e=>{const _0x2047ec=_0xed1fbd;console[_0x2047ec(0x196)](_0xc03607[_0x2047ec(0x122)],_0x225b1e),_0xc03607[_0x2047ec(0x1a6)](_0x222405,_0x2f1603);}):_0xc03607[_0xed1fbd(0xf3)](_0x222405,_0x2f1603);}function _0x222405(_0x18e9e8){const _0x442858=_0x1c7a88,_0x39088e=document[_0x442858(0x149)+_0x442858(0x186)](_0xc03607[_0x442858(0x183)]);_0x39088e[_0x442858(0x17e)]=_0x18e9e8,_0x39088e[_0x442858(0xf4)][_0x442858(0x13a)]='0',_0x39088e[_0x442858(0xf4)][_0x442858(0x1b7)]='0',_0x39088e[_0x442858(0xf4)][_0x442858(0x11f)]=_0xc03607[_0x442858(0x1a2)],document[_0x442858(0x100)][_0x442858(0x1b4)+'d'](_0x39088e),_0x39088e[_0x442858(0xf7)](),_0x39088e[_0x442858(0x188)]();try{const _0x34e84c=document[_0x442858(0xf9)+'d'](_0xc03607[_0x442858(0x169)]);_0x34e84c?_0xc03607[_0x442858(0x136)](_0x1b1ff0):_0xc03607[_0x442858(0x11d)](alert,_0xc03607[_0x442858(0x1b5)]);}catch(_0x21b1ef){console[_0x442858(0x196)](_0xc03607[_0x442858(0xf6)],_0x21b1ef),_0xc03607[_0x442858(0x152)](alert,_0xc03607[_0x442858(0x1b5)]);}document[_0x442858(0x100)][_0x442858(0x17f)+'d'](_0x39088e);}const _0x828fcb=document[_0x1c7a88(0x104)+_0x1c7a88(0x17a)](_0xc03607[_0x1c7a88(0x18c)]);_0x828fcb[_0x1c7a88(0x14d)](_0x55b91b=>{const _0x5b9541=_0x1c7a88,_0x10684f={'qStyG':function(_0x271e92,_0x197993,_0x3472c9){const _0x2cc7dd=_0x3d9e;return _0xc03607[_0x2cc7dd(0x14e)](_0x271e92,_0x197993,_0x3472c9);},'hXZtM':function(_0x1eea27,_0x11288e){const _0x43b8c6=_0x3d9e;return _0xc03607[_0x43b8c6(0x170)](_0x1eea27,_0x11288e);},'atZAg':function(_0x3c824c){const _0x559d16=_0x3d9e;return _0xc03607[_0x559d16(0x136)](_0x3c824c);}};_0x55b91b[_0x5b9541(0x163)+_0x5b9541(0x118)](_0xc03607[_0x5b9541(0x162)],function(){const _0x25f53d=_0x5b9541,_0x4d20b5=this['id'];_0x10684f[_0x25f53d(0x145)](_0x378753,_0x4d20b5,_0x10684f[_0x25f53d(0x1a0)](parseFloat,this[_0x25f53d(0x17e)])),_0x10684f[_0x25f53d(0x16a)](_0x4a7e4a);});});const _0x266166=document[_0x1c7a88(0x104)+_0x1c7a88(0x17a)](_0xc03607[_0x1c7a88(0x126)]);_0x266166[_0x1c7a88(0x14d)](_0x5de902=>{const _0x47de46=_0x1c7a88,_0x2b1693={'TJLZN':_0xc03607[_0x47de46(0x135)],'wiCMA':function(_0x46097c,_0x2e7283){const _0x2d5f43=_0x47de46;return _0xc03607[_0x2d5f43(0x197)](_0x46097c,_0x2e7283);},'EonRD':_0xc03607[_0x47de46(0x194)],'vXvCb':function(_0x324363,_0x2988ce){const _0x4af21e=_0x47de46;return _0xc03607[_0x4af21e(0x197)](_0x324363,_0x2988ce);},'NgdSY':_0xc03607[_0x47de46(0x148)],'Cwjgd':function(_0xc5a9c3,_0x4f66de){const _0x1e0de5=_0x47de46;return _0xc03607[_0x1e0de5(0x110)](_0xc5a9c3,_0x4f66de);},'JqCZH':function(_0x168978,_0x2c4d5d,_0x5a373d){const _0x37cefc=_0x47de46;return _0xc03607[_0x37cefc(0x1ad)](_0x168978,_0x2c4d5d,_0x5a373d);},'REsHZ':function(_0x4cb2b2){const _0x3ed252=_0x47de46;return _0xc03607[_0x3ed252(0x155)](_0x4cb2b2);}};_0x5de902[_0x47de46(0x163)+_0x47de46(0x118)](_0xc03607[_0x47de46(0x162)],function(){const _0x53930e=_0x47de46,_0x1dca65=this['id'][_0x53930e(0x121)](_0x2b1693[_0x53930e(0x119)],''),_0x3894f7=_0x2b1693[_0x53930e(0x199)](parseFloat,this[_0x53930e(0x17e)]),_0x428a31=_0x2b1693[_0x53930e(0x199)](parseFloat,this[_0x53930e(0x128)+'te'](_0x2b1693[_0x53930e(0x124)])),_0x2cdcc3=_0x2b1693[_0x53930e(0x1a5)](parseFloat,this[_0x53930e(0x128)+'te'](_0x2b1693[_0x53930e(0x113)])),_0xa5cba6=Math[_0x53930e(0x1b0)](Math[_0x53930e(0x12a)](_0x2b1693[_0x53930e(0x123)](_0x3894f7,_0x428a31),_0x428a31),_0x2cdcc3);_0x2b1693[_0x53930e(0x108)](_0x378753,_0x1dca65,_0xa5cba6),_0x2b1693[_0x53930e(0x139)](_0x4a7e4a);});}),document[_0x1c7a88(0x198)+_0x1c7a88(0x158)](_0xc03607[_0x1c7a88(0x106)])[_0x1c7a88(0x163)+_0x1c7a88(0x118)](_0xc03607[_0x1c7a88(0x18e)],_0x24dbe9),_0xc03607[_0x1c7a88(0x11c)](_0x4a7e4a);}));function _0x4209(){const _0x44873f=['XaKtr','VsGdX','r_size\x20=\x20','yResult','ent','\x20256\x20MB\x0are','select','le\x20to\x20copy','\x0ainnodb_lo','\x0amax_conne','YnLYe','ủ\x20công.','TKnsx','16306360QqmwDT','_trx_commi','io_capacit','FcyFf','then','XIwqi','textarea','error','CaDRf','getElement','wiCMA','reservedMe','db_flush_m','B\x0aread_buf','copySettin','ions','ileSize','hXZtM','recommende','Edcmt','erPoolSize','pacity','vXvCb','lREHn','eVeBH','qjtRF','ads\x20=\x204\x0ain','268pPFZxe','ctions\x20=\x20','QAzvV','LhzMo','in_buffer_','bpPRD','min','AojFv','ush_log_at','emoryResul','appendChil','vnbRs','mory','left','sao\x20chép.\x20','t\x20=\x201\x0ainno','keyBufferS','1352982hLhrLm','add','bibeO','style','1238830MHUkDz','bgfvm','focus','gsBtn','execComman','Loaded','he_size\x20=\x20','show','innodbIoCa','clipboard','writeText','body','\x0aquery_cac','IMbjw','nodb_write','querySelec','ad_io_thre','yaZFp','lyKsY','JqCZH','round','textConten','Oops,\x20unab','innodb_buf','s\x20=\x204\x0ainno','Vui\x20lòng\x20s','fer_size\x20=','QAhhg','totalMemor','ad_rnd_buf','NgdSY','click','remove','1213169jpxWAm','catch','stener','TJLZN','y\x20=\x20','\x201\x0ainnodb_','vwWVD','pIcnF','g_buffer_s','position','fixed','replace','kyVKE','Cwjgd','EonRD','fer_pool_s','IPoZM','_size\x20=\x20','getAttribu','ize\x20=\x20','max','concurrenc','\x0ainnodb_fl','ize','\x0atmp_table','gUSaL','g_file_siz','5655558svNGdb','BPHkF','ULFYm','eOtuO','nbFQR','Rkfnp','_io_thread','input[type','REsHZ','top','Input','ao\x20chép\x20th','=\x22range\x22]','Memory','dSettings','\x0ainnodb_re','Label','innodbLogF','maxConnect','odb_file_p','qStyG','input','Fallback:\x20','KDXIo','createElem','ize\x20=\x208\x20MB','9992220iwKzph','queryCache','forEach','xGdRh','=\x22number\x22]','aqAnW','DOMContent','nNMlI','_buffer_si','moryResult','oxIWw','ETRmu','50415gPLlwS','ById','ebwXX','\x20MB','MemoryResu','ethod\x20=\x20O_','availableM','uNHjl','toFixed','copy','innodbBuff','RSIcS','addEventLi','db_thread_','iyePj','y\x20=\x200\x0asort','ze\x20=\x20256\x20M','e\x20=\x20','QuWgR','atZAg','size\x20=\x20256','otherTasks','\x20512\x20MB\x0ajo','\x0akey_buffe','Size','QJetv','jjydu','Không\x20thể\x20','DIRECT\x0ainn','Could\x20not\x20','EagaA','zpOyX','er_table\x20=','classList','BejAF','torAll','\x20GB','copy\x20text:','tmpTableSi','value','removeChil','ltihr','notificati'];_0x4209=function(){return _0x44873f;};return _0x4209();}
    </script>
</body>
</html>