<?php
/**
 * VTS SEO Redirects
 * 
 * SEO-related redirects and URL management
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Redirect search results containing game-related keywords
 */
function vts_redirect_search_results() {
    if (isset($_GET['s'])) {
        // Define an array of keywords related to "Game"
        $game_related_keywords = ['game', 'gaming', 'play', 'arcade', 'video game', 'esports']; // Add more keywords as needed

        // Convert the search query to lowercase to make the search case-insensitive
        $search_query = strtolower($_GET['s']);

        // Check if any part of the search query matches the game-related keywords
        foreach ($game_related_keywords as $keyword) {
            if (strpos($search_query, $keyword) !== false) {
                // Redirect to the homepage if a match is found
                wp_redirect('https://vutruso.com/');
                exit;
            }
        }
    }
}
add_action('template_redirect', 'vts_redirect_search_results');
