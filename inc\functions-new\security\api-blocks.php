<?php
/**
 * VTS Security - API Blocks
 * 
 * Block specific API calls and external requests
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Block WP Rocket API calls
 */
function vts_block_wp_rocket_api($pre, $parsed_args, $url) {
    if (strpos($url, 'api.wp-rocket.me/check_update.php') !== false) {
        return new WP_Error('blocked', 'API call blocked');
    }
    return $pre;
}
add_filter('pre_http_request', 'vts_block_wp_rocket_api', 10, 3);

/**
 * Block specific API calls
 */
function vts_block_specific_api_calls($args, $url) {
    $blocked_urls = [
        'https://objectcache.pro/api/plugin/update',
        'https://public-api.wordpress.com/wpcom/v2/wcpay/incentives',
    ];

    if (in_array($url, $blocked_urls)) {
        // Return false to block completely instead of timeout 0
        return new WP_Error('blocked', 'API call blocked');
    }
    return $args;
}
add_filter('http_request_args', 'vts_block_specific_api_calls', 10, 2);

/**
 * Filter slow HTTP requests
 */
function vts_filter_slow_http_requests($pre, $parsed_args, $url) {
    // Add logic here if needed to filter slow requests
    return $pre;
}
add_filter('pre_http_request', 'vts_filter_slow_http_requests', 99, 3);
