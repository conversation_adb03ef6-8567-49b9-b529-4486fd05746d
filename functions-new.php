<?php
/**
 * VTS Theme Functions - Optimized & Organized
 * 
 * This file has been completely restructured for better organization,
 * performance, and maintainability.
 * 
 * @package VTS_Theme
 * @version 2.0.0
 * <AUTHOR> <PERSON>
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Constants
 */
define('VTS_THEME_VERSION', '2.0.0');
define('VTS_THEME_PATH', get_template_directory());
define('VTS_THEME_URL', get_template_directory_uri());

/**
 * Load the optimized functions autoloader
 * 
 * The autoloader intelligently loads function files based on context:
 * - Core functions (always loaded)
 * - Frontend functions (only on frontend)
 * - Admin functions (only in admin)
 * - WooCommerce functions (only when WooCommerce is active)
 * - Optimization functions (performance tweaks)
 * - SEO functions (SEO-related functionality)
 * - Security functions (security hardening)
 */
require_once VTS_THEME_PATH . '/inc/functions-new/autoloader.php';

/**
 * Include legacy functions that haven't been migrated yet
 * These will be gradually moved to the new structure
 */

// RSS Feed customizations
function vts_include_pages_in_rss_feed($query) {
    if ($query->is_feed()) {
        $exclude_page_ids = [22166, 30197, 3, 29, 627, 27, 28, 639, 3274, 3035, 479, 3280, 7886, 3277, 626, 3202, 2653, 6568, 2678, 28702];
        $query->set('post_type', ['post', 'page']);
        $query->set('post__not_in', $exclude_page_ids);
    }
    return $query;
}
add_filter('pre_get_posts', 'vts_include_pages_in_rss_feed');

// Custom RSS feed limit
function vts_custom_rss_feed_limit($query) {
    if ($query->is_feed) {
        $query->set('posts_per_rss', 12);
    }
}
add_action('pre_get_posts', 'vts_custom_rss_feed_limit');

// Time shortcode [vts_time]
function vts_publish() {
    global $post;	
    $published_time = get_the_time('d-m-Y', $post->ID);
    $modified_time = get_the_modified_time('d-m-Y', $post->ID);
    $author = get_the_author();
    $author_url = get_author_posts_url(get_the_author_meta('ID')); 

    return '<div class="vts_time post_on"><i class="fa fa-calendar" aria-hidden="true"></i> <span content="'.$published_time.'" itemprop="datePublished">'.$published_time.'</span> <i class="fa fa-clock-o" aria-hidden="true"></i> <span content="'.$modified_time.'"  itemprop="dateModified">'.$modified_time.'</span>  <i class="fa fa-user" aria-hidden="true"></i> <span><a href="'.$author_url.'">'.$author.'</a></span></div>';
}
add_shortcode('vts_time', 'vts_publish');

// Valid Phone (Contact Form 7)
function vts_custom_filter_wpcf7_is_tel($result, $tel) { 
    $result = preg_match(
        '/^(0|\+84)(\s|\.)?((3[2-9])|(5[6689])|(7[0679])|(8[1-9])|(9[9]))(\d)(\s|\.)?(\d{3})(\s|\.)?(\d{3})$/',
        $tel
    );
    return $result; 
}
add_filter('wpcf7_is_tel', 'vts_custom_filter_wpcf7_is_tel', 10, 2);

// Featured image in RSS
function vts_featured_image_rss($content) {
    global $post;

    $thumb_id = get_post_thumbnail_id();
    $thumb_url = wp_get_attachment_image_src($thumb_id, 'large', true);

    if (has_post_thumbnail($post->ID)) {
        $content = '<a href="' . get_permalink($post->ID) . '" title="' . $post->post_title . '">' . 
                   '<img alt="' . $post->post_title . '" style="max-width: 100%; width: 100%;" src="' . $thumb_url[0] . '" />' . 
                   '</a><br/><br/>' . $content;
    }
    return $content;
}
add_filter('the_excerpt_rss', 'vts_featured_image_rss');
add_filter('the_content_feed', 'vts_featured_image_rss');

// Remove Contact Form 7 auto p tags
add_filter('wpcf7_autop_or_not', '__return_false');

/**
 * Include existing function files that are still being used
 * These will be gradually migrated to the new structure
 *
 * NOTE: Commented out files that have been migrated to avoid conflicts
 */

// Include files that are still needed from the old structure
$legacy_includes = [
    '/inc/functions/vts_related_post_in_content.php',
    '/inc/functions/vts_related_posts_cat.php',
    '/inc/functions/vts_featured_image_column.php',
    '/inc/functions/vts_login.php',
    '/inc/functions/vts_fixed_toc.php',
    '/inc/functions/vts_pre_btn.php',
    '/inc/widgets/custom_menu.php',

    // MIGRATED - DO NOT INCLUDE TO AVOID CONFLICTS:
    // '/inc/functions/vts_clean.php',           // → optimization/clean-wp.php
    // '/inc/functions/vts_performance.php',     // → optimization/performance.php
    // '/inc/functions/vts_woocommerce.php',     // → woocommerce/setup.php + customizations.php
    // '/inc/functions/vts_seo.php',             // → seo/schema.php + redirects.php
    // '/inc/functions/vts_secure.php',          // → security/disable-features.php + api-blocks.php
    // '/inc/functions/vts_register_sidebar.php', // → core/sidebars.php
];

foreach ($legacy_includes as $file) {
    $file_path = VTS_THEME_PATH . $file;
    if (file_exists($file_path)) {
        require_once $file_path;
    }
}

/**
 * Theme initialization hook
 * 
 * This hook can be used by child themes or plugins to extend functionality
 */
do_action('vts_theme_loaded');

/**
 * Debug information (only in WP_DEBUG mode)
 */
if (defined('WP_DEBUG') && WP_DEBUG) {
    add_action('wp_footer', function() {
        if (current_user_can('administrator')) {
            $loaded_files = VTS_Functions_Autoloader::get_loaded_files();
            echo '<!-- VTS Theme Debug: ' . count($loaded_files) . ' function files loaded -->';
        }
    });
}
