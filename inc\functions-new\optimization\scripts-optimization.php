<?php
/**
 * VTS Scripts & Styles Optimization
 * 
 * Optimize loading of scripts and styles
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Defer JavaScript files except critical scripts
 */
function vts_defer_js_except_jquery($tag, $handle, $src) {
    // Don't apply in admin
    if (is_admin()) {
        return $tag;
    }
    
    // List of scripts that should not be deferred
    $excluded_handles = [
        'wp-polyfill',
        // 'jquery',
        // 'jquery-core',
        // 'jquery-migrate',
        // 'wp-hooks',
    ];
    
    // Check if handle is in exclusion list
    foreach ($excluded_handles as $excluded) {
        if (strpos($handle, $excluded) !== false) {
            return $tag;
        }
    }
    
    // Check if script already has defer or async
    if (strpos($tag, 'defer') !== false || strpos($tag, 'async') !== false) {
        return $tag;
    }
    
    // Only apply to .js files and not inline scripts
    if (strpos($tag, '.js') !== false && !empty($src)) {
        return str_replace(' src', ' defer src', $tag);
    }
    
    return $tag;
}
add_filter('script_loader_tag', 'vts_defer_js_except_jquery', 10, 3);

/**
 * Defer specific CSS files
 */
function vts_defer_css($html, $handle) {
    $handles = ['vts-fontawesome', 'vts-mobile-menu', 'venobox-css'];
    
    if (in_array($handle, $handles)) {
        $html = str_replace('media=\'all\'', 'media=\'print\' onload="this.onload=null;this.media=\'all\'"', $html);
    }
    
    return $html;
}
add_filter('style_loader_tag', 'vts_defer_css', 10, 2);

/**
 * Remove jQuery migrate
 */
function vts_remove_jquery_migrate($scripts) {
    if (!is_admin() && isset($scripts->registered['jquery'])) {
        $script = $scripts->registered['jquery'];
        
        if ($script->deps) { // Check whether the script has any dependencies
            $script->deps = array_diff($script->deps, [
                'jquery-migrate'
            ]);
        }
    }
}
add_action('wp_default_scripts', 'vts_remove_jquery_migrate');

/**
 * Preload current product thumbnail for WooCommerce
 */
function vts_preload_current_product_thumbnail() {
    if (!is_product()) return;

    global $product;

    if (!$product || !is_a($product, 'WC_Product')) return;

    $thumbnail_id = $product->get_image_id();
    if (!$thumbnail_id) return;

    $image_url = wp_get_attachment_image_url($thumbnail_id, 'woocommerce_thumbnail');

    if ($image_url) {
        echo '<link rel="preload" as="image" href="' . esc_url($image_url) . '" fetchpriority="high">' . "\n";
    }
}

/**
 * Add preload action for product pages
 */
function vts_add_product_preload() {
    if (is_product() && function_exists('is_woocommerce')) {
        vts_preload_current_product_thumbnail();
    }
}
add_action('wp_head', 'vts_add_product_preload', 1);
