<svg width="88" height="88" viewBox="0 0 88 88" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)" filter="url(#filter0_d)">
<path d="M4.25303 7.33846L9.58839 16.6748C9.76836 16.9903 10.0696 17.2185 10.4218 17.3069L15.3995 18.5513L32.4716 35.6234L34.358 33.7376L17.0186 16.3988C16.848 16.2276 16.634 16.1064 16.3997 16.0476L11.6169 14.8477L7.07434 6.89403L10.9689 2.9995L18.9258 7.54641L20.1263 12.3292C20.1845 12.5641 20.3063 12.7786 20.4769 12.9493L37.8163 30.2886L39.7021 28.4023L22.63 11.3301L21.3856 6.35522C21.2972 6.00242 21.069 5.70065 20.7535 5.52013L11.4166 0.18532C10.8959 -0.110967 10.2408 -0.023178 9.8161 0.399854L4.48128 5.73522C4.05222 6.15715 3.95894 6.81337 4.25303 7.33846V7.33846Z" fill="#0C49C4"/>
<path d="M59.7054 26.2867L30.3626 55.6301L28.4762 53.7437L57.8196 24.4009L59.7054 26.2867Z" fill="#0C49C4"/>
<path d="M25.2269 56.6681C24.9854 56.2665 24.552 56.0212 24.0834 56.0212H16.0809C15.6129 56.0212 15.1789 56.2665 14.938 56.6681L10.9365 63.3368C10.6825 63.7593 10.6825 64.2877 10.9365 64.7107L14.938 71.3794C15.1789 71.7804 15.6129 72.0262 16.0809 72.0262H24.0834C24.552 72.0262 24.9854 71.7804 25.2269 71.3794L29.2278 64.7107C29.4819 64.2877 29.4819 63.7593 29.2278 63.3368L25.2269 56.6681ZM23.329 69.3586H16.8359L13.6349 64.0237L16.8359 58.6884H23.329L26.53 64.0237L23.329 69.3586Z" fill="#0C49C4"/>
<path d="M68.098 32.0132C76.907 32.0428 84.0728 24.9254 84.1024 16.1163C84.1068 14.7754 83.9422 13.4399 83.6124 12.14C83.4346 11.4251 82.7109 10.99 81.996 11.1678C81.7612 11.2265 81.5472 11.3478 81.376 11.5184L73.0734 19.8155L66.4859 17.6203L64.289 11.0345L72.5916 2.7302C73.1123 2.20895 73.1118 1.36453 72.5905 0.843838C72.4188 0.672101 72.2026 0.550295 71.9661 0.492135C63.4232 -1.67131 54.7447 3.49999 52.5813 12.0424C52.2526 13.3384 52.0886 14.6706 52.093 16.0077C52.0963 17.0579 52.2043 18.1047 52.4172 19.133L23.2072 48.3425C22.1789 48.1301 21.1321 48.022 20.0824 48.0182C11.2427 48.0182 4.0769 55.1839 4.0769 64.0237C4.0769 72.8629 11.2427 80.0287 20.0824 80.0287C28.9217 80.0287 36.0874 72.8629 36.0874 64.0237C36.0841 62.9735 35.9755 61.9267 35.7632 60.8984L42.7561 53.9055L45.8144 56.9639C46.3357 57.4846 47.1795 57.4846 47.7008 56.9639L48.3674 56.2967C48.9523 55.7156 49.8977 55.7156 50.4826 56.2967C51.0669 56.8794 51.068 57.8253 50.4853 58.4096C50.4848 58.4102 50.4837 58.4113 50.4826 58.4124L49.8159 59.079C49.2952 59.5997 49.2952 60.4441 49.8159 60.9648L65.7266 76.8754C69.9113 81.0986 76.727 81.1293 80.9502 76.9446C85.1729 72.7598 85.2036 65.9435 81.0188 61.7209C80.9963 61.6979 80.9733 61.6748 80.9502 61.6518L65.0396 45.7417C64.5189 45.221 63.6745 45.221 63.1538 45.7417L62.4872 46.4084C61.9017 46.99 60.9569 46.99 60.3714 46.4084C59.7876 45.8257 59.786 44.8797 60.3687 44.2959C60.3698 44.2949 60.3709 44.2943 60.3714 44.2932L61.0386 43.626C61.5593 43.1053 61.5593 42.2609 61.0386 41.7402L57.9803 38.6819L64.9732 31.6889C66.0014 31.9013 67.0483 32.0099 68.098 32.0132V32.0132ZM73.3384 77.3616C72.3694 77.3599 71.4087 77.1849 70.5017 76.8442L80.9184 66.4269C82.4904 70.6084 80.3752 75.2722 76.1937 76.8442C75.2813 77.1871 74.3139 77.3627 73.3384 77.3616ZM58.2283 42.6965C56.7513 44.4584 56.9828 47.0843 58.7446 48.5608C60.293 49.8585 62.5497 49.8574 64.0964 48.557L79.0644 63.5381C79.2268 63.7011 79.3788 63.8728 79.5259 64.0473L68.1221 75.4511C67.9471 75.3046 67.7753 75.1526 67.6124 74.9896L52.6329 60.009C54.1099 58.2472 53.8784 55.6212 52.1166 54.1447C50.5682 52.8466 48.3109 52.8482 46.7642 54.148L44.6304 52.0142L56.0939 40.5677L58.2283 42.6965ZM63.6141 29.2819L33.3572 59.5328C33.0252 59.8642 32.8919 60.3454 33.0049 60.8002C34.8348 67.9863 30.4925 75.2952 23.3059 77.1245C16.1199 78.9544 8.81091 74.6121 6.98107 67.4255C5.15177 60.2395 9.49402 52.9305 16.6806 51.1007C18.8545 50.5476 21.1321 50.5476 23.3059 51.1007C23.7608 51.2121 24.2409 51.0787 24.5734 50.749L54.8232 20.4975C55.1546 20.1661 55.2879 19.6849 55.1738 19.2306C53.3566 12.1285 57.6407 4.89803 64.7428 3.08026C66.0848 2.73733 67.473 2.60565 68.8557 2.69179L61.82 9.73025C61.4628 10.0869 61.3377 10.6153 61.4973 11.0943L64.1645 19.0973C64.2978 19.4951 64.6105 19.8078 65.0089 19.9401L73.0119 22.6078C73.4904 22.7674 74.0188 22.6429 74.3759 22.2862L81.4117 15.2505C81.4276 15.5051 81.4358 15.7569 81.4358 16.0077C81.4731 23.3364 75.5627 29.3077 68.234 29.3455C67.101 29.351 65.9718 29.2117 64.8745 28.9307C64.4196 28.8172 63.9384 28.9499 63.607 29.2813H63.6141V29.2819Z" fill="#0C49C4"/>
<path d="M73.0437 67.0817L71.1574 68.9681L57.8201 55.6307L59.7059 53.7444L73.0437 67.0817Z" fill="#0C49C4"/>
</g>
<defs>
<filter id="filter0_d" x="0" y="0" width="88" height="88" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<clipPath id="clip0">
<rect width="80" height="80" fill="white" transform="translate(4)"/>
</clipPath>
</defs>
</svg>
