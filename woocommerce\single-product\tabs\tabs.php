<?php
/**
 * Single Product tabs
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * @package WooCommerce/Templates
 * @version 3.8.0
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

$tabs = apply_filters( 'woocommerce_product_tabs', array() );

// Hàm kiểm tra xem tab có nội dung hay không
function has_tab_content($key, $tab) {
    if (!isset($tab['callback'])) {
        return false;
    }
    
    // Lấy nội dung của tab
    ob_start();
    call_user_func($tab['callback'], $key, $tab);
    $content = ob_get_clean();
    
    // Kiểm tra xem nội dung có rỗng hay không
    return !empty(trim($content));
}

// Lọc các tab có nội dung
$active_tabs = array();
foreach ($tabs as $key => $tab) {
    if (has_tab_content($key, $tab)) {
        $active_tabs[$key] = $tab;
    }
}

if ( ! empty( $active_tabs ) ) : ?>
    <div class="vts-detail vts-reviews">
        <ul class="tabs wc-tabs" role="tablist">
            <?php $i=1; ?>
            <?php foreach ( $active_tabs as $key => $tab ) : ?>
                <li class="<?php echo esc_attr( $key ); ?>_tab" id="tab-title-<?php echo esc_attr( $key ); ?>" role="tab" aria-controls="tab-<?php echo esc_attr( $key ); ?>">
                    <a data="tab<?php echo $i++; ?>" href="#tab-<?php echo esc_attr( $key ); ?>" class="active">
                        <?php echo apply_filters( 'woocommerceproduct' . $key . '_tab_title', esc_html( $tab['title'] ), $key ); ?>
                    </a>
                </li>
            <?php endforeach; ?>
        </ul>
        <div class="clear"></div>
        <?php $j=1; ?>
        <?php foreach ( $active_tabs as $key => $tab ) : ?>
        <div class="tab<?php echo $j++; ?>">
            <div class="vts-describe vts-tab">
                <div class="single">
                    <?php if ( isset( $tab['callback'] ) ) { call_user_func( $tab['callback'], $key, $tab ); } ?>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
     </div>
<?php endif; ?>