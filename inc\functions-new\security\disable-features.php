<?php
/**
 * VTS Security - Disable Features
 * 
 * Disable unnecessary WordPress features for security
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Disable comments completely
 */
function vts_disable_comments_completely() {
    // Remove support for comments in post types
    add_action('init', function() {
        remove_post_type_support('post', 'comments');
        
        // For all post types
        foreach (get_post_types() as $post_type) {
            if (post_type_supports($post_type, 'comments')) {
                remove_post_type_support($post_type, 'comments');
                remove_post_type_support($post_type, 'trackbacks');
            }
        }
    });
    
    // Hide comments menu in admin
    add_action('wp_before_admin_bar_render', function() {
        global $wp_admin_bar;
        $wp_admin_bar->remove_menu('comments');
    });
    
    // Close comments on frontend
    add_filter('comments_open', '__return_false', 20, 2);
    add_filter('pings_open', '__return_false', 20, 2);
    
    // Redirect comment feeds
    add_action('template_redirect', function() {
        if ((is_comment_feed() && get_post_type() === 'post') || (is_comment_feed() && get_post_type() === 'product')) {
            header('Content-Type: text/html; charset=utf-8');
            wp_redirect(home_url('/feed'));
            exit();
        }
    });
    
    // Remove comment-related dashboard widgets
    add_action('admin_init', function() {
        remove_meta_box('dashboard_recent_comments', 'dashboard', 'normal');
    });
}
add_action('after_setup_theme', 'vts_disable_comments_completely');

/**
 * Disable auto updates
 */
function vts_disable_auto_updates() {
    if (is_admin()) {
        // Disable update checks when entering admin panel
        remove_action('admin_init', '_maybe_update_core');
        remove_action('admin_init', '_maybe_update_plugins');
        remove_action('admin_init', '_maybe_update_themes');

        // Disable update checks when entering specific admin pages
        remove_action('load-plugins.php', 'wp_update_plugins');
        remove_action('load-themes.php', 'wp_update_themes');
        remove_action('load-update.php', 'wp_update_plugins');
        remove_action('load-update.php', 'wp_update_themes');

        // Don't touch the cron events
        remove_action('wp_version_check', 'wp_version_check');
        remove_action('wp_update_plugins', 'wp_update_plugins');
        remove_action('wp_update_themes', 'wp_update_themes');

        // Disable browser update check in console
        add_filter('pre_site_transient_browser_' . md5($_SERVER['HTTP_USER_AGENT']), '__return_empty_array');
    }
}
add_action('init', 'vts_disable_auto_updates');

/**
 * Disable specific cron events
 */
function vts_disable_specific_cron_events() {
    // List of cron hooks to disable
    $cron_hooks_to_disable = [
        'seopress_google_analytics_cron',
        'seopress_matomo_analytics_cron',
        'wp_update_themes',
        'woocommerce_marketplace_cron_fetch_promotions',
        'seopress_page_speed_insights_cron',
        'jetpack_v2_heartbeat',
        'seopress_insights_gsc_cron',
        'woocommerce_geoip_updater',
        'jetpack_clean_nonces'
    ];

    foreach ($cron_hooks_to_disable as $hook) {
        // Clear all scheduled events for this hook
        wp_clear_scheduled_hook($hook);
        
        // Prevent this hook from being scheduled again in the future
        add_filter('schedule_event', function($event) use ($hook) {
            if (isset($event->hook) && $event->hook === $hook) {
                return false;
            }
            return $event;
        });
    }
}
add_action('init', 'vts_disable_specific_cron_events');

/**
 * Disable REST API for non-logged in users
 */
function vts_turn_off_rest_api_not_logged_in($errors) {
    // If there is already an error, just return it
    if (is_wp_error($errors)) {
        return $errors;
    }

    if (!is_user_logged_in()) {
        // Return WP_Error object if user is not logged in
        return new WP_Error('no_rest_api_sorry', 'REST API not allowed', ['status' => 401]);
    }
    
    return $errors;
}
// Uncomment to enable REST API restriction
// add_filter('rest_authentication_errors', 'vts_turn_off_rest_api_not_logged_in');

/**
 * Remove version from RSS feeds
 */
function vts_remove_wp_version_from_rss() {
    return '';
}
add_filter('the_generator', 'vts_remove_wp_version_from_rss');

/**
 * Unregister default widgets
 */
function vts_unregister_default_widgets() {
    // WordPress default widgets
    $wp_widgets = [
        'WP_Widget_Pages', 'WP_Widget_Calendar', 'WP_Widget_Archives',
        'WP_Widget_Links', 'WP_Widget_Meta', 'WP_Widget_Search',
        'WP_Widget_Text', 'WP_Widget_Categories', 'WP_Widget_Recent_Posts',
        'WP_Widget_Recent_Comments', 'WP_Widget_RSS', 'WP_Widget_Tag_Cloud',
        'WP_Widget_Media_Video', 'WP_Widget_Media_Audio', 'WP_Widget_Media_Gallery',
        'Block'
    ];
    
    foreach ($wp_widgets as $widget) {
        unregister_widget($widget);
    }
    
    // WooCommerce widgets
    $woo_widgets = [
        'WC_Widget_Featured_Products', 'WC_Widget_Product_Tag_Cloud',
        'WC_Widget_Cart', 'WC_Widget_Layered_Nav', 'WC_Widget_Layered_Nav_Filters',
        'WC_Widget_Rating_Filter', 'WC_Widget_Price_Filter', 'WC_Widget_Product_Search',
        'WC_Widget_Top_Rated_Products', 'WC_Widget_Recent_Reviews',
        'WC_Widget_Recently_Viewed', 'WC_Widget_Best_Sellers',
        'WC_Widget_Onsale', 'WC_Widget_Random_Products'
    ];
    
    foreach ($woo_widgets as $widget) {
        unregister_widget($widget);
    }
}
add_action('widgets_init', 'vts_unregister_default_widgets', 15);

/**
 * Remove block styles
 */
function vts_remove_block_styles() {
    wp_dequeue_style('wp-block-library');
    wp_deregister_style('wc-block-style');
}
add_action('wp_print_styles', 'vts_remove_block_styles', 100);
