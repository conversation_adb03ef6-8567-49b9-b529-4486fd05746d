<?php
/**
 * VTS Menu Functions
 * 
 * Menu-related functionality and customizations
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Clean menu classes - keep only essential classes
 */
function vts_clean_menu_classes($var) {
    return is_array($var) ? array_intersect($var, ['current-menu-item']) : '';
}
add_filter('nav_menu_item_id', 'vts_clean_menu_classes', 100, 1);
add_filter('page_css_class', 'vts_clean_menu_classes', 100, 1);

/**
 * Add itemprop="url" to menu links for SEO
 */
function vts_add_itemprop_menu($atts, $item, $args) {
    $atts['itemprop'] = 'url';
    return $atts;
}
add_filter('nav_menu_link_attributes', 'vts_add_itemprop_menu', 10, 3);
