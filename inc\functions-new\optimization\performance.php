<?php
/**
 * VTS Performance Optimization
 * 
 * Performance-related optimizations and tweaks
 * 
 * @package VTS_Theme
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Optimize database queries
 */
function vts_optimize_queries($query) {
    if (!is_admin() && $query->is_main_query()) {
        // Avoid SELECT * 
        $query->set('fields', 'ids');
        
        // Limit post revisions
        if ($query->is_post_type_archive()) {
            $query->set('no_found_rows', true);
        }
    }
}
add_action('pre_get_posts', 'vts_optimize_queries');

/**
 * Fix memory issues
 */
function vts_fix_memory_issues() {
    // Limit post revisions
    if (!defined('WP_POST_REVISIONS')) {
        define('WP_POST_REVISIONS', 5);
    }
    
    // Increase memory limit if needed
    if (WP_DEBUG && !defined('WP_MEMORY_LIMIT')) {
        define('WP_MEMORY_LIMIT', '256M');
    }
}
add_action('init', 'vts_fix_memory_issues');

/**
 * Disable text domain loading to save resources
 */
function vts_disable_text_domain() {
    add_filter('pre_load_textdomain', '__return_false', -42);
    
    // Disable text domain error messages
    add_filter('doing_it_wrong_trigger_error', function($status, $function_name) {
        if ('_load_textdomain_just_in_time' === $function_name) {
            return false;
        }
        return $status;
    }, 10, 2);
}
add_action('init', 'vts_disable_text_domain');

/**
 * Remove DNS prefetch
 */
function vts_remove_dns_prefetch() {
    remove_action('wp_head', 'wp_resource_hints', 2);
}
add_action('init', 'vts_remove_dns_prefetch');

/**
 * Disable admin email verification
 */
function vts_disable_admin_email_check() {
    add_filter('admin_email_check_interval', '__return_false');
}
add_action('init', 'vts_disable_admin_email_check');

/**
 * Remove SVG in body
 */
function vts_remove_svg_body() {
    remove_action('wp_body_open', 'wp_global_styles_render_svg_filters');
    remove_action('wp_enqueue_scripts', 'wp_enqueue_global_styles');
}
add_action('init', 'vts_remove_svg_body');

/**
 * Disable sitemap
 */
function vts_disable_sitemap() {
    add_filter('wp_sitemaps_enabled', '__return_false');
}
add_action('init', 'vts_disable_sitemap');

/**
 * Disable Gutenberg
 */
function vts_disable_gutenberg() {
    add_filter('use_block_editor_for_post', '__return_false', 10);
    add_filter('gutenberg_use_widgets_block_editor', '__return_false');
    add_filter('use_widgets_block_editor', '__return_false');
}
add_action('init', 'vts_disable_gutenberg');

/**
 * Disable language dropdown in login page
 */
function vts_disable_login_language_dropdown() {
    add_filter('login_display_language_dropdown', '__return_false');
}
add_action('init', 'vts_disable_login_language_dropdown');

/**
 * Disable auto-sizes CSS fix
 */
function vts_disable_auto_sizes_css() {
    remove_action('wp_head', 'wp_print_auto_sizes_contain_css_fix', 1);
}
add_action('init', 'vts_disable_auto_sizes_css');

/**
 * Disable Speculative Loading
 */
function vts_disable_speculative_loading($config) {
    return null; // Return null to completely disable Speculative Loading
}
add_filter('wp_speculation_rules_configuration', 'vts_disable_speculative_loading');

/**
 * Optimize WP Rocket minify HTML options
 */
function vts_rocket_minify_html_options($options) {
    if (!isset($options['ignoreCustomComments'])) {
        $options['ignoreCustomComments'] = [];
    }
    
    // Add pre and code tags to exclusion list
    $options['excludes'][] = '<pre';
    $options['excludes'][] = '</pre>';
    $options['excludes'][] = '<code';
    $options['excludes'][] = '</code>';

    return $options;
}
add_filter('rocket_minify_html_options', 'vts_rocket_minify_html_options');

/**
 * Double preloading for WP Rocket
 */
function vts_x2_preloading($concurrent_batches) {
    return $concurrent_batches * 2;
}
add_filter('action_scheduler_queue_runner_concurrent_batches', 'vts_x2_preloading');

/**
 * Remove query strings from static resources
 */
function vts_remove_query_strings($src) {
    $parts = explode('?', $src);
    if (strpos($src, '?ver=')) {
        // Keep version parameter for effective caching
        $version = strpos($src, '?ver=') ? substr(strrchr($src, '?ver='), 5) : '';
        return $parts[0] . '?ver=' . $version;
    }
    return $parts[0];
}
add_filter('script_loader_src', 'vts_remove_query_strings', 999);
add_filter('style_loader_src', 'vts_remove_query_strings', 999);

/**
 * Only allow main feed
 */
function vts_only_main_feed() {
    if (is_feed() && '/feed/' !== trailingslashit($_SERVER['REQUEST_URI'])) {
        wp_redirect(home_url() . '/feed');
        exit();
    }
}
add_action('template_redirect', 'vts_only_main_feed');

/**
 * Show modified posts first in archives
 */
function vts_modify_query_order($query) {
    // Check that we're not in admin and this is the main query
    if (!is_admin() && $query->is_main_query()) {
        // Apply to home, archive, category and tag pages
        if (is_home() || is_archive() || is_category() || is_tag()) {
            // Sort posts by most recent modification date
            $query->set('orderby', 'modified');
            $query->set('order', 'DESC');

            // Include only published posts
            $query->set('post_status', ['publish']);
        }
    }
}
add_action('pre_get_posts', 'vts_modify_query_order');

/**
 * Remove pagination on homepage
 */
function vts_remove_homepage_pagination($query) {
    if ($query->is_main_query() && is_front_page()) {
        $query->set('no_found_rows', true);
        $query->set('posts_per_archive_page', 1);
    }
}
add_action('pre_get_posts', 'vts_remove_homepage_pagination');

/**
 * Redirect homepage pagination to home
 */
function vts_redirect_homepage_pagination() {
    if (is_front_page() && (get_query_var('paged') > 1 || get_query_var('page') > 1)) {
        wp_redirect(home_url(), 301);
        exit;
    }
}
add_action('template_redirect', 'vts_redirect_homepage_pagination');
