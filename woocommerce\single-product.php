<?php
/**
 * The Template for displaying all single products
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see         https://docs.woocommerce.com/document/template-structure/
 * @package     WooCommerce/Templates
 * @version     1.6.4
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly
}

get_header( 'shop' ); ?>

    <?php
        /**
         * woocommerce_before_main_content hook.
         *
         * @hooked woocommerce_output_content_wrapper - 10 (outputs opening divs for the content)
         * @hooked woocommerce_breadcrumb - 20
         */
        //do_action( 'woocommerce_before_main_content' );
    ?>

        <?php while ( have_posts() ) : the_post(); ?>

            <?php wc_get_template_part( 'content', 'single-product' ); ?>

        <?php endwhile; // end of the loop. ?>

    <?php
        /**
         * woocommerce_after_main_content hook.
         *
         * @hooked woocommerce_output_content_wrapper_end - 10 (outputs closing divs for the content)
         */
        //do_action( 'woocommerce_after_main_content' );
    ?>

    <?php
        /**
         * woocommerce_sidebar hook.
         *
         * @hooked woocommerce_get_sidebar - 10
         */
        //do_action( 'woocommerce_sidebar' );
    ?>

<?php 
if(is_product()) : 
$exerpt = get_the_excerpt();

?>
    <script type="application/ld+json">
{
  "@context": "https://schema.org/", 
  "@type": "Product", 
  "name": "<?php the_title(); ?>",
  "image": "<?php echo get_the_post_thumbnail_url($post->ID, 'full');  ?>",
  "description": "<?php echo strip_tags($exerpt) ?>",
  "brand": {
    "@type": "Brand",
    "name": "Dự án web Vũ Trụ Số"
  },
  "sku": "web_vts_2022",
  "offers": {
    "@type": "Offer",
    "url": "https://vutruso.com",
    "priceCurrency": "VND",
    "price": "1000000",
    "priceValidUntil": "2023-08-16",
    "availability": "https://schema.org/InStock",
    "itemCondition": "https://schema.org/NewCondition"
  },
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "4.9",
    "bestRating": "5.0",
    "worstRating": "4.5",
    "ratingCount": "8",
    "reviewCount": "8"
  },
  "review": {
    "@type": "Review",
    "name": "",
    "reviewBody": "",
    "reviewRating": {
      "@type": "Rating",
      "ratingValue": "4.9",
      "bestRating": "4.9",
      "worstRating": "4.5"
    },
    "datePublished": "2022-04-26",
    "author": {"@type": "Person", "name": "Tien Dung Dao"},
    "publisher": {"@type": "Organization", "name": "Vũ Trụ Số"}
  }
}
</script>

<?php endif; ?>

<?php get_footer( 'shop' );

/* Omit closing PHP tag at the end of PHP files to avoid "headers already sent" issues. */
