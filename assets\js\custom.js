document.addEventListener('DOMContentLoaded', function() {
    // Tab handling
    document.querySelectorAll('ul.tabs li a').forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            const target = this.getAttribute('data');
            
            // Remove active class
            document.querySelectorAll('ul.tabs li a').forEach(t => 
                t.classList.remove('active'));
            
            // Add active class
            this.classList.add('active');
            
            // Hide all tabs
            document.querySelectorAll('.tab1,.tab2,.tab3,.tab4')
                .forEach(content => content.style.display = 'none');
            
            // Show selected tab
            document.querySelector('.' + target).style.display = 'block';
        });
    });

    // Form handling
    const formHandler = (triggerID, formID, closeID) => {
        const trigger = document.getElementById(triggerID);
        const form = document.getElementById(formID);
        const close = document.getElementById(closeID);

        if(trigger && form && close) {
            trigger.addEventListener('click', () => {
                form.classList.add('fade-in');
                form.classList.remove('fade-out');
            });

            close.addEventListener('click', () => {
                form.classList.add('fade-out');
                form.classList.remove('fade-in');
            });
        }
    };

    // Initialize forms
    formHandler('gui-yeu-cau', 'form-yeu-cau', 'tat');
    formHandler('bao-gia', 'form-bao-gia', 'close');
});